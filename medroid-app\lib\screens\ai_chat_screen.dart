import 'dart:io';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:medroid_app/models/chat_message.dart';
import 'package:medroid_app/models/chat_recommendation.dart';
import 'package:medroid_app/models/time_slot.dart';
import 'package:medroid_app/screens/referral_screen.dart';
import 'package:medroid_app/services/api_service.dart';
import 'package:medroid_app/widgets/chat_message.dart';
import 'package:medroid_app/utils/app_colors.dart';
import 'package:medroid_app/widgets/medroid_logo.dart';
import 'package:medroid_app/widgets/universal_chat_input.dart';
import 'package:medroid_app/widgets/appointment_slot_selector.dart';
import 'package:medroid_app/widgets/premium_empty_state.dart';
import 'package:medroid_app/screens/payment_screen.dart';
import 'package:medroid_app/widgets/notification_bell.dart';

import 'package:medroid_app/utils/responsive_utils.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AIChatScreen extends StatefulWidget {
  final String? initialConversationId;

  const AIChatScreen({Key? key, this.initialConversationId}) : super(key: key);

  @override
  _AIChatScreenState createState() => _AIChatScreenState();
}

class _AIChatScreenState extends State<AIChatScreen>
    with TickerProviderStateMixin {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  String? _conversationId;
  List<ChatMessage> _messages = [];
  List<ChatRecommendation> _recommendations = [];
  List<String> _healthConcerns = [];
  List<dynamic> _userAppointments = []; // User appointments data
  bool _isLoading = false;
  bool _isTyping = false;
  bool _hasError = false;
  String _errorMessage = '';
  bool _escalated = false;
  String? _escalationReason;
  bool _appointmentBooked = false;
  String? _referralNote; // Store the referral note for appointment booking
  bool _waitingForAppointmentResponse =
      false; // Flag to track if we're waiting for appointment booking response

  // Animation controllers for typing indicator
  late final List<AnimationController> _typingDotControllers;

  @override
  void initState() {
    super.initState();

    // Initialize animation controllers
    _typingDotControllers = List.generate(
      3,
      (index) => AnimationController(
        vsync: this,
        duration: const Duration(milliseconds: 600),
      )..repeat(reverse: true),
    );

    // Check for transferred conversation ID from anonymous chat
    _checkForTransferredConversation().then((transferredId) async {
      if (transferredId != null) {
        debugPrint(
            'CHAT TRANSFER: Loading transferred conversation: $transferredId');
        await _loadExistingConversation(transferredId);

        // Check if we need to trigger appointment booking after loading the conversation
        _checkForAppointmentBookingTrigger(transferredId);
      } else if (widget.initialConversationId != null) {
        debugPrint(
            'Loading initial conversation: ${widget.initialConversationId}');
        await _loadExistingConversation(widget.initialConversationId!);

        // Check if we need to trigger appointment booking after loading the conversation
        _checkForAppointmentBookingTrigger(widget.initialConversationId!);
      } else {
        debugPrint('Starting new conversation');
        _startNewConversation();
      }
    });

    // Fetch user appointments
    _fetchUserAppointments();
  }

  // Check if we need to trigger appointment booking after loading the conversation
  Future<void> _checkForAppointmentBookingTrigger(String conversationId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final triggerBookingAfterLogin =
          prefs.getBool('trigger_appointment_booking_after_login') ?? false;

      if (triggerBookingAfterLogin) {
        debugPrint('Triggering appointment booking after login');

        // Clear the flag to avoid processing again
        await prefs.setBool('trigger_appointment_booking_after_login', false);

        // Add a small delay to ensure the conversation is fully loaded
        Future.delayed(const Duration(milliseconds: 500), () {
          if (mounted) {
            _requestAppointment(null);
          }
        });
      }
    } catch (e) {
      debugPrint('Error checking for appointment booking trigger: $e');
    }
  }

  // Check for transferred conversation ID from anonymous chat
  Future<String?> _checkForTransferredConversation() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Check if we're coming from anonymous chat
      final comingFromAnonymousChat =
          prefs.getBool('coming_from_anonymous_chat') ?? false;
      final transferReady =
          prefs.getBool('anonymous_chat_transfer_ready') ?? false;
      final hasTransferredMessages =
          prefs.getString('transferred_messages') != null;

      debugPrint(
          'CHAT TRANSFER: Coming from anonymous chat: $comingFromAnonymousChat');
      debugPrint('CHAT TRANSFER: Transfer ready: $transferReady');
      debugPrint(
          'CHAT TRANSFER: Has transferred messages: $hasTransferredMessages');

      if (comingFromAnonymousChat || transferReady || hasTransferredMessages) {
        // We need to create a new conversation and transfer the anonymous chat data
        return await _createConversationFromAnonymousChat();
      }

      // Try multiple keys for the conversation ID for redundancy
      final conversationId = prefs.getString('post_login_conversation_id') ??
          prefs.getString('transferred_conversation_id') ??
          prefs.getString('active_conversation_id') ??
          prefs.getString('initial_conversation_id');

      if (conversationId != null) {
        debugPrint(
            'CHAT TRANSFER: Found transferred conversation ID: $conversationId');

        // Clear the transfer flags now that we've retrieved the conversation ID
        await prefs.remove('post_login_conversation_id');
        await prefs.remove('transferred_conversation_id');
        await prefs.remove('active_conversation_id');
        await prefs.remove('initial_conversation_id');
        await prefs.remove('coming_from_anonymous_chat');
        await prefs.remove('anonymous_chat_transfer_complete');
        await prefs.remove('anonymous_chat_transfer_ready');

        return conversationId;
      }

      return null;
    } catch (e) {
      debugPrint('CHAT TRANSFER ERROR: $e');
      return null;
    }
  }

  // Create a new conversation from anonymous chat data
  Future<String?> _createConversationFromAnonymousChat() async {
    try {
      debugPrint(
          'CHAT TRANSFER: Creating new conversation from anonymous chat data');
      final prefs = await SharedPreferences.getInstance();

      // Get the transferred messages
      final transferredMessagesJson = prefs.getString('transferred_messages');
      final anonymousChatSummary = prefs.getString('anonymous_chat_summary');

      if (transferredMessagesJson == null && anonymousChatSummary == null) {
        debugPrint('CHAT TRANSFER: No transferred messages or summary found');
        return null;
      }

      // Create a new conversation
      final apiService = RepositoryProvider.of<ApiService>(context);
      final newConversationId = await apiService.startChatConversation();
      debugPrint(
          'CHAT TRANSFER: Created new conversation with ID: $newConversationId');

      // Prepare the summary message
      String summaryMessage = '';

      // If we have a prepared summary, use it
      if (anonymousChatSummary != null) {
        summaryMessage = anonymousChatSummary;
      } else {
        // Otherwise, create a summary from the transferred messages
        summaryMessage = 'Previous anonymous chat summary:\n';

        try {
          final transferredMessages =
              json.decode(transferredMessagesJson!) as List;

          // Get the last 8 messages or all if less than 8
          int messageCount = transferredMessages.length;
          int startIndex = messageCount > 8 ? messageCount - 8 : 0;

          for (int i = startIndex; i < messageCount; i++) {
            final message = transferredMessages[i];
            summaryMessage +=
                "${message['role'] == 'user' ? 'Me' : 'Assistant'}: ${message['content']}\n";
          }

          // Check for appointment intent
          bool hasAppointmentIntent = transferredMessages.any((m) =>
              m['content'].toString().toLowerCase().contains('appointment') ||
              m['content'].toString().toLowerCase().contains('book') ||
              m['content'].toString().toLowerCase().contains('doctor'));

          if (hasAppointmentIntent) {
            summaryMessage +=
                '\nI was interested in booking an appointment. Please help me continue with the booking process.';
          }
        } catch (e) {
          debugPrint(
              'CHAT TRANSFER: Error creating summary from transferred messages: $e');
          summaryMessage +=
              'I was chatting with the AI doctor before logging in.';
        }
      }

      // Send the summary message to the new conversation
      debugPrint('CHAT TRANSFER: Sending summary message: $summaryMessage');
      await apiService.sendChatMessage(newConversationId, summaryMessage);

      // Get demographic information if available
      final transferGender = prefs.getString('transfer_gender');
      final transferAge = prefs.getString('transfer_age');

      if (transferGender != null || transferAge != null) {
        String demographicMessage = 'My demographic information: ';
        if (transferGender != null) {
          demographicMessage += 'Gender: $transferGender';
        }
        if (transferAge != null) {
          demographicMessage += transferGender != null
              ? ', Age group: $transferAge'
              : 'Age group: $transferAge';
        }

        debugPrint(
            'CHAT TRANSFER: Sending demographic information: $demographicMessage');
        await apiService.sendChatMessage(newConversationId, demographicMessage);
      }

      // Clear all transfer flags
      await prefs.remove('transferred_messages');
      await prefs.remove('anonymous_chat_summary');
      await prefs.remove('coming_from_anonymous_chat');
      await prefs.remove('anonymous_chat_transfer_ready');
      await prefs.remove('anonymous_chat_transfer_complete');
      await prefs.remove('transfer_gender');
      await prefs.remove('transfer_age');
      await prefs.remove('original_anonymous_conversation_id');
      await prefs.remove('original_anonymous_id');

      // Show a snackbar to inform the user
      if (mounted) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text(
                  'Your anonymous chat has been transferred to your account'),
              duration: Duration(seconds: 3),
            ),
          );
        });
      }

      return newConversationId;
    } catch (e) {
      debugPrint(
          'CHAT TRANSFER ERROR: Error creating conversation from anonymous chat: $e');
      return null;
    }
  }

  // Fetch user appointments from the API
  Future<void> _fetchUserAppointments({bool forceRefresh = false}) async {
    try {
      // If we already have appointments and not forcing a refresh, return
      if (!forceRefresh && _userAppointments.isNotEmpty) {
        debugPrint(
            'Using cached appointments: ${_userAppointments.length} appointments');
        return;
      }

      debugPrint('Fetching fresh appointments data...');
      final apiService = RepositoryProvider.of<ApiService>(context);
      final appointments = await apiService.getUserAppointments();

      if (mounted) {
        setState(() {
          _userAppointments = appointments;
        });
        debugPrint(
            'Successfully fetched ${_userAppointments.length} appointments');

        // Debug the first appointment if available
        if (_userAppointments.isNotEmpty) {
          debugPrint('First appointment: ${_userAppointments.first}');
        }
      }
    } catch (e) {
      debugPrint('Error fetching user appointments: $e');
      // Initialize with empty list to prevent null errors
      if (mounted) {
        setState(() {
          _userAppointments = [];
        });
      }

      // Show a snackbar only if this was a forced refresh (user explicitly asked for appointments)
      if (forceRefresh && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(
                'Unable to fetch your appointments. Please try again later.'),
            duration: Duration(seconds: 3),
          ),
        );
      }
    }
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();

    // Only dispose controllers if they're initialized
    try {
      for (final controller in _typingDotControllers) {
        if (controller.isAnimating) {
          controller.stop();
        }
        controller.dispose();
      }
    } catch (e) {
      // Ignore errors during disposal
      // Using print for debugging only
      print('Error disposing animation controllers: $e');
    }

    super.dispose();
  }

  // Build an animated typing dot for the typing indicator
  Widget _buildTypingDot(int delayMilliseconds) {
    // Use a delayed animation for each dot
    if (delayMilliseconds > 0) {
      Future.delayed(Duration(milliseconds: delayMilliseconds), () {
        if (mounted && _isTyping) {
          _typingDotControllers[delayMilliseconds ~/ 150].forward();
        }
      });
    }

    return AnimatedBuilder(
      animation: _typingDotControllers[delayMilliseconds ~/ 150],
      builder: (context, child) {
        return Container(
          width: 8,
          height: 8,
          decoration: BoxDecoration(
            color: Color.fromRGBO(
              23, 195, 178, // AppColors.tealSurge RGB values
              0.4 +
                  (0.6 * _typingDotControllers[delayMilliseconds ~/ 150].value),
            ),
            borderRadius: BorderRadius.circular(4),
          ),
        );
      },
    );
  }

  Future<void> _loadExistingConversation(String conversationId) async {
    setState(() {
      _isLoading = true;
      _hasError = false;
      _errorMessage = '';
      _conversationId = conversationId;
    });

    try {
      final apiService = RepositoryProvider.of<ApiService>(context);
      final conversation = await apiService.getConversation(conversationId);

      // Format the messages from the API response
      final List<dynamic> messagesData = conversation['messages'] ?? [];
      final List<ChatMessage> messages = messagesData.map((m) {
        return ChatMessage(
          id: m['_id'] != null
              ? m['_id'].toString()
              : DateTime.now().millisecondsSinceEpoch.toString(),
          role: m['role'] ?? 'user',
          content: m['content'] ?? '',
          timestamp: m['timestamp'] != null
              ? DateTime.parse(m['timestamp'])
              : DateTime.now(),
        );
      }).toList();

      // Get recommendations and health concerns
      final List<dynamic> recommendationsData =
          conversation['recommendations'] ?? [];
      final List<ChatRecommendation> recommendations =
          recommendationsData.map((r) {
        return ChatRecommendation.fromJson(r);
      }).toList();

      if (mounted) {
        setState(() {
          _messages = messages;
          _recommendations = recommendations;
          _healthConcerns =
              List<String>.from(conversation['health_concerns'] ?? []);
          _escalated = conversation['escalated'] ?? false;
          _escalationReason = conversation['escalation_reason'];
          _appointmentBooked =
              false; // Reset appointment booking flag when loading a conversation
          _isLoading = false;
        });

        _scrollToBottom();
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _hasError = true;
          _errorMessage = e.toString();
        });
      }
    }
  }

  Future<void> _startNewConversation() async {
    setState(() {
      _isLoading = true;
      _hasError = false;
      _errorMessage = '';
    });

    try {
      print('Starting new conversation');
      final apiService = RepositoryProvider.of<ApiService>(context);
      final conversationId = await apiService.startChatConversation();

      print('Successfully created conversation with ID: $conversationId');

      setState(() {
        _conversationId = conversationId;
        _messages = [];
        _recommendations = [];
        _healthConcerns = [];
        _escalated = false;
        _escalationReason = null;
        _appointmentBooked = false; // Reset appointment booking flag
        _isLoading = false;
      });

      // No longer adding the welcome message - first message must be from the user
    } catch (e) {
      print('Error in _startNewConversation: $e');
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = e.toString();
      });

      // Show a more detailed error message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error starting conversation: $e'),
          duration: const Duration(seconds: 5),
          action: SnackBarAction(
            label: 'Retry',
            onPressed: () {
              ScaffoldMessenger.of(context).hideCurrentSnackBar();
              _startNewConversation();
            },
          ),
        ),
      );
    }
  }

  void _addSystemMessage(String text) {
    final message = ChatMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      role: 'assistant',
      content: text,
      timestamp: DateTime.now(),
    );

    setState(() {
      _messages.add(message);
    });
  }

  // Save appointment message to backend conversation
  Future<void> _saveAppointmentMessageToBackend(ChatMessage message) async {
    if (_conversationId == null) return;

    try {
      final apiService = RepositoryProvider.of<ApiService>(context);

      // Create a special message format for appointment confirmations
      final messageContent = '${message.content}\n\n[APPOINTMENT_CONFIRMATION]';

      // Send the appointment message to the backend
      await apiService.sendChatMessage(
        _conversationId!,
        messageContent,
        includePatientContext: false,
        requestFullResponse: false,
        generateTitle: false,
      );

      debugPrint('Appointment message saved to backend successfully');
    } catch (e) {
      debugPrint('Error saving appointment message to backend: $e');
      // Don't show error to user as this is a background operation
    }
  }

  Future<void> _sendMessage(
      {String? predefinedMessage,
      bool isLoadingMore = false,
      File? imageFile}) async {
    // Use either the predefined message or get from text controller
    final text = predefinedMessage ?? _messageController.text.trim();
    if ((text.isEmpty && imageFile == null) ||
        _isTyping ||
        _conversationId == null) {
      return;
    }

    // Check if the user is requesting to book a new appointment
    if (_checkForAppointmentBookingRequest(text)) {
      return _handleAppointmentBookingRequest(text);
    }

    // Check if the user is asking about their existing appointments
    if (_checkForAppointmentQuery(text)) {
      return _handleAppointmentQuery(text);
    }

    // Check if an appointment has already been booked and the user is confirming with messages like "Perfect!"
    if (_appointmentBooked &&
        (text.toLowerCase() == 'perfect!' ||
            text.toLowerCase() == 'perfect' ||
            text.toLowerCase() == 'thanks' ||
            text.toLowerCase() == 'thank you' ||
            text.toLowerCase() == 'great' ||
            text.toLowerCase() == 'ok' ||
            text.toLowerCase() == 'okay')) {
      // Add user message to chat
      final userMessage = ChatMessage(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        role: 'user',
        content: text,
        timestamp: DateTime.now(),
        contentType: MessageContentType.text,
      );

      setState(() {
        _messages.add(userMessage);
      });

      // Only clear if using the text controller
      if (predefinedMessage == null) {
        _messageController.clear();
      }

      // Add a response acknowledging the confirmation
      final aiMessage = ChatMessage(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        role: 'assistant',
        content:
            "You're welcome! Your appointment has been confirmed. Is there anything else I can help you with?",
        timestamp: DateTime.now(),
      );

      setState(() {
        _messages.add(aiMessage);
      });

      _scrollToBottom();
      return;
    }

    // Check if we're waiting for appointment response and user is responding positively
    if (_waitingForAppointmentResponse &&
        _checkForAppointmentConfirmation(text)) {
      // Add user message to chat
      final userMessage = ChatMessage(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        role: 'user',
        content: text,
        timestamp: DateTime.now(),
        contentType: MessageContentType.text,
      );

      setState(() {
        _messages.add(userMessage);
        _isTyping = true;
      });

      // Only clear if using the text controller
      if (predefinedMessage == null) {
        _messageController.clear();
      }

      _scrollToBottom();

      // Extract specialty from previous messages if available
      String? detectedSpecialty = _extractSpecialtyFromPreviousMessages();

      // Add a response acknowledging the confirmation
      final aiMessage = ChatMessage(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        role: 'assistant',
        content:
            "Great! I'll find available appointment slots for you right away.",
        timestamp: DateTime.now(),
      );

      setState(() {
        _messages.add(aiMessage);
        _isTyping = false;
        _waitingForAppointmentResponse = false; // Reset the flag
      });

      _scrollToBottom();

      // Request appointment with the detected specialty
      _requestAppointment(detectedSpecialty);
      return;
    }

    // If we're loading more, we don't need to add a new user message
    if (!isLoadingMore) {
      final userMessage = ChatMessage(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        role: 'user',
        content: text,
        timestamp: DateTime.now(),
        contentType: imageFile != null
            ? MessageContentType.image
            : MessageContentType.text,
        localImagePath: imageFile?.path,
      );

      setState(() {
        _messages.add(userMessage);
        _isTyping = true;
      });

      // Only clear if using the text controller
      if (predefinedMessage == null) {
        _messageController.clear();
      }

      _scrollToBottom();
    } else {
      // If loading more, just set typing state
      setState(() {
        _isTyping = true;
      });
    }

    try {
      final apiService = RepositoryProvider.of<ApiService>(context);
      print('Sending message to conversation: $_conversationId');

      // Determine which API method to call based on whether we have an image
      final response = imageFile != null
          ? await apiService.sendChatMessageWithImage(
              _conversationId!,
              text,
              imageFile,
            )
          : await apiService.sendChatMessage(
              _conversationId!,
              text,
              requestFullResponse:
                  isLoadingMore, // Request full response if loading more
            );

      print('Successfully received response: $response');

      // Check if a new conversation ID was returned (happens when the original conversation was not found)
      if (response.containsKey('new_conversation_id')) {
        print(
            'Received new conversation ID: ${response['new_conversation_id']}');
        setState(() {
          _conversationId = response['new_conversation_id'];
        });

        // Show a message to the user that a new conversation was started
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(
                'Started a new conversation due to an error with the previous one.'),
            duration: Duration(seconds: 3),
          ),
        );
      }

      if (response['message'] == null) {
        throw Exception('Response message is null');
      }

      // Check if the AI response contains appointment booking intent
      final String aiResponseMessage = response['message'];
      bool containsAppointmentIntent =
          _checkForAppointmentBookingIntent(aiResponseMessage);

      // Check if the response contains appointment slots (direct booking response)

      bool hasAppointmentSlots = response.containsKey('available_slots') &&
          response['available_slots'] != null &&
          response['available_slots'] is List &&
          (response['available_slots'] as List).isNotEmpty;

      // If we have appointment slots, show them immediately
      if (hasAppointmentSlots) {
        debugPrint(
            'Received appointment slots, showing appointment booking dialog');

        // Remove the loading message if it exists
        setState(() {
          if (_messages.isNotEmpty &&
              _messages.last.role == 'assistant' &&
              _messages.last.content
                  .contains("I'm checking if we're ready to book")) {
            _messages.removeLast();
          }
          _isTyping = false;
        });

        // Show appointment slots immediately
        final availableSlots = response['available_slots'] as List<dynamic>;

        Future.delayed(const Duration(milliseconds: 300), () {
          if (mounted) {
            _showAppointmentSlotSelector(availableSlots);
          }
        });

        _scrollToBottom();
        return; // Don't process as regular message
      }

      // If we're loading more, update the last AI message instead of adding a new one
      if (isLoadingMore &&
          _messages.isNotEmpty &&
          _messages.last.role == 'assistant') {
        setState(() {
          // Update the last message with the full response
          final lastIndex = _messages.length - 1;
          _messages[lastIndex] = ChatMessage(
            id: _messages[lastIndex].id,
            role: 'assistant',
            content: response['message'],
            timestamp: _messages[lastIndex].timestamp,
          );

          _healthConcerns =
              List<String>.from(response['health_concerns'] ?? []);

          if (response['recommendations'] != null) {
            try {
              _recommendations = (response['recommendations'] as List)
                  .map((r) => ChatRecommendation.fromJson(r))
                  .toList();
            } catch (e) {
              print('Error parsing recommendations: $e');
              // Continue without recommendations if there's an error
              _recommendations = [];
            }
          }

          _escalated = response['escalated'] ?? false;
          _escalationReason = response['escalation_reason'];
          _isTyping = false;
        });
      } else {
        // Create a new AI message
        final aiMessage = ChatMessage(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          role: 'assistant',
          content: response['message'],
          timestamp: DateTime.now(),
          isTruncated:
              response['is_truncated'] ?? false, // Store truncation state
        );

        setState(() {
          _messages.add(aiMessage);
          _healthConcerns =
              List<String>.from(response['health_concerns'] ?? []);

          if (response['recommendations'] != null) {
            try {
              _recommendations = (response['recommendations'] as List)
                  .map((r) => ChatRecommendation.fromJson(r))
                  .toList();
            } catch (e) {
              print('Error parsing recommendations: $e');
              // Continue without recommendations if there's an error
              _recommendations = [];
            }
          }

          _escalated = response['escalated'] ?? false;
          _escalationReason = response['escalation_reason'];
          _isTyping = false;
        });

        // If the AI response contains appointment booking intent, wait for user response
        if (containsAppointmentIntent && _conversationId != null) {
          debugPrint('Detected appointment booking intent in AI response');
          // Set a flag to indicate we're waiting for appointment booking response
          setState(() {
            _waitingForAppointmentResponse = true;
          });
          debugPrint(
              'Waiting for user response to appointment booking question');
        }
      }

      _scrollToBottom();
    } catch (e) {
      print('Error in _sendMessage: $e');
      setState(() {
        _isTyping = false;
        if (!isLoadingMore) {
          // Check if the error message contains API key related issues
          String errorMsg = e.toString();
          if (errorMsg.contains('API key') ||
              errorMsg.contains('OpenAI') ||
              errorMsg.contains('authentication')) {
            _addSystemMessage(
                'I apologize, but our AI service is currently unavailable. Please contact support or try again later.');
          } else {
            _addSystemMessage(
                'Sorry, I encountered an error processing your message. Please try again.');
          }
        }
      });

      // Show a more detailed error message to help with debugging
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
                'Error: ${e.toString().substring(0, e.toString().length > 100 ? 100 : e.toString().length)}...'),
            duration: const Duration(seconds: 5),
            action: SnackBarAction(
              label: 'Retry',
              onPressed: () {
                ScaffoldMessenger.of(context).hideCurrentSnackBar();
                if (!isLoadingMore && predefinedMessage != null) {
                  _sendMessage(
                      predefinedMessage: predefinedMessage,
                      imageFile: imageFile);
                }
              },
            ),
          ),
        );
      }
    }
  }

  // Function to load more content for a truncated message
  Future<void> _loadMoreContent(String originalMessage) async {
    if (_conversationId == null) return;

    // Create a prompt that asks for the complete response
    const prompt =
        'Please provide the complete response to my previous question. Your last response seemed to be cut off.';

    // Call sendMessage with the load more flag
    await _sendMessage(predefinedMessage: prompt, isLoadingMore: true);
  }

  // Show dialog to edit conversation title
  Future<void> _showEditTitleDialog(Map<String, dynamic> conversation) async {
    final TextEditingController titleController = TextEditingController();
    titleController.text = conversation['title'] ?? '';

    if (!mounted) return;

    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Edit Conversation Title'),
        content: TextField(
          controller: titleController,
          decoration: const InputDecoration(
            labelText: 'Title',
            hintText: 'Enter a descriptive title',
          ),
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              final newTitle = titleController.text.trim();
              if (newTitle.isNotEmpty) {
                Navigator.of(context).pop();

                final apiService = RepositoryProvider.of<ApiService>(context);
                final success = await apiService.updateConversationTitle(
                    conversation['id'].toString(), newTitle);

                if (success && mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Title updated')),
                  );
                  // Refresh the chat history
                  _showChatHistoryDialog();
                } else if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Failed to update title')),
                  );
                }
              }
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );

    titleController.dispose();
  }

  Future<void> _analyzeSymptoms() async {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return SymptomCheckerWidget(
          onSubmit: (symptoms) {
            Navigator.pop(context);

            // Add user message to chat
            final userMessage = 'Symptom check: $symptoms';
            _sendMessage(predefinedMessage: userMessage);
          },
        );
      },
    );
  }

  Future<void> _lookupMedication() async {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return MedicationLookupWidget(
          onSubmit: (medication) {
            Navigator.pop(context);

            // Add user message to chat
            final userMessage = 'Tell me about the medication: $medication';
            _sendMessage(predefinedMessage: userMessage);
          },
        );
      },
    );
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  // Check if the user is requesting to book a new appointment
  // This is now more conservative and only returns true for very explicit booking requests
  bool _checkForAppointmentBookingRequest(String message) {
    // We'll only detect explicit appointment booking requests
    // The AI will handle more nuanced cases through conversation

    // For now, we'll return false to prevent premature appointment booking
    // This ensures the AI has a proper conversation first
    return false;

    // The code below is kept for reference but is not used
    /*
    final lowerCaseMessage = message.toLowerCase();

    // Patterns that indicate the user wants to book a new appointment
    final bookingPatterns = [
      'book appointment',
      'book an appointment',
      'schedule appointment',
      'schedule an appointment',
      'make appointment',
      'make an appointment',
      'get appointment',
      'get an appointment',
      'need appointment',
      'need an appointment',
      'want appointment',
      'want an appointment',
      'book doctor',
      'see a doctor',
      'visit a doctor',
    ];

    // Check for booking patterns
    for (final pattern in bookingPatterns) {
      if (lowerCaseMessage.contains(pattern)) {
        return true;
      }
    }

    // Check for more complex patterns
    if ((lowerCaseMessage.contains('appointment') ||
            lowerCaseMessage.contains('doctor') ||
            lowerCaseMessage.contains('consultation')) &&
        (lowerCaseMessage.contains('book') ||
            lowerCaseMessage.contains('schedule') ||
            lowerCaseMessage.contains('make') ||
            lowerCaseMessage.contains('need') ||
            lowerCaseMessage.contains('want'))) {
      // Make sure it's not asking about existing appointments
      if (!(lowerCaseMessage.contains('my appointments') ||
          lowerCaseMessage.contains('do i have') ||
          lowerCaseMessage.contains('show me') ||
          lowerCaseMessage.contains('list my') ||
          lowerCaseMessage.contains('view my') ||
          lowerCaseMessage.contains('see my') ||
          lowerCaseMessage.contains('check my'))) {
        return true;
      }
    }

    return false;
    */
  }

  // Check if the user is asking about their existing appointments
  bool _checkForAppointmentQuery(String message) {
    final lowerCaseMessage = message.toLowerCase();

    // Check for appointment-related keywords
    return (lowerCaseMessage.contains('appointment') ||
            lowerCaseMessage.contains('appointments') ||
            lowerCaseMessage.contains('schedule') ||
            lowerCaseMessage.contains('scheduled') ||
            lowerCaseMessage.contains('booking') ||
            lowerCaseMessage.contains('booked')) &&
        (lowerCaseMessage.contains('my') ||
            lowerCaseMessage.contains('i have') ||
            lowerCaseMessage.contains('do i have') ||
            lowerCaseMessage.contains('show') ||
            lowerCaseMessage.contains('list') ||
            lowerCaseMessage.contains('view') ||
            lowerCaseMessage.contains('see') ||
            lowerCaseMessage.contains('check') ||
            lowerCaseMessage.contains('tell'));
  }

  // Handle appointment query and provide information about booked appointments
  Future<void> _handleAppointmentQuery(String query) async {
    // Add user message to chat
    final userMessage = ChatMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      role: 'user',
      content: query,
      timestamp: DateTime.now(),
      contentType: MessageContentType.text,
    );

    setState(() {
      _messages.add(userMessage);
      _isTyping = true;
    });

    _messageController.clear();
    _scrollToBottom();

    // Always fetch fresh appointment data when user asks about appointments
    await _fetchUserAppointments(forceRefresh: true);

    // Prepare response based on appointments
    String responseMessage =
        'I\'m checking your appointments...'; // Default value

    if (_userAppointments.isEmpty) {
      responseMessage =
          'You don\'t have any appointments scheduled at the moment. Would you like me to help you book an appointment?';
    } else {
      // Filter upcoming appointments
      final now = DateTime.now();
      final upcomingAppointments = _userAppointments.where((appointment) {
        final appointmentDate = DateTime.parse(appointment['date']);
        return appointmentDate.isAfter(now) ||
            (appointmentDate.year == now.year &&
                appointmentDate.month == now.month &&
                appointmentDate.day == now.day);
      }).toList();

      // Filter past appointments
      final pastAppointments = _userAppointments.where((appointment) {
        final appointmentDate = DateTime.parse(appointment['date']);
        return appointmentDate.isBefore(now) &&
            !(appointmentDate.year == now.year &&
                appointmentDate.month == now.month &&
                appointmentDate.day == now.day);
      }).toList();

      // Check what type of appointments the user is asking about
      final lowerQuery = query.toLowerCase();
      final bool askingAboutPast = lowerQuery.contains('past') ||
          lowerQuery.contains('previous') ||
          lowerQuery.contains('history');

      final bool askingAboutUpcoming = lowerQuery.contains('upcoming') ||
          lowerQuery.contains('next') ||
          lowerQuery.contains('scheduled') ||
          lowerQuery.contains('future');

      // If the query is generic (not specifically asking about past or upcoming)
      if (!askingAboutPast && !askingAboutUpcoming) {
        // Show both upcoming and past appointments
        responseMessage = 'Here\'s a summary of your appointments:\n\n';

        if (upcomingAppointments.isNotEmpty) {
          responseMessage += '📅 UPCOMING APPOINTMENTS:\n';

          for (int i = 0; i < upcomingAppointments.length; i++) {
            final appointment = upcomingAppointments[i];
            final date = DateTime.parse(appointment['date']);
            final formattedDate = '${date.day}/${date.month}/${date.year}';
            final timeSlot = appointment['time_slot'];
            final providerName =
                appointment['provider']?['user']?['name'] ?? 'Unknown Provider';
            final reason = appointment['reason'] ?? 'General consultation';
            final isTelemedicine = appointment['is_telemedicine'] ?? false;

            responseMessage +=
                '${i + 1}. $providerName on $formattedDate at ${timeSlot['start_time']} - ${timeSlot['end_time']}\n';
            responseMessage += '   Reason: $reason\n';
            if (isTelemedicine) {
              responseMessage += '   Type: Video Consultation\n';
            }
            responseMessage += '\n';
          }
        } else {
          responseMessage += '📅 UPCOMING APPOINTMENTS: None\n\n';
        }

        if (pastAppointments.isNotEmpty) {
          responseMessage += '📚 PAST APPOINTMENTS:\n';

          // Show only the most recent 3 past appointments to avoid clutter
          final recentPastAppointments = pastAppointments.length > 3
              ? pastAppointments.sublist(0, 3)
              : pastAppointments;

          for (int i = 0; i < recentPastAppointments.length; i++) {
            final appointment = recentPastAppointments[i];
            final date = DateTime.parse(appointment['date']);
            final formattedDate = '${date.day}/${date.month}/${date.year}';
            final timeSlot = appointment['time_slot'];
            final providerName =
                appointment['provider']?['user']?['name'] ?? 'Unknown Provider';
            final reason = appointment['reason'] ?? 'General consultation';

            responseMessage +=
                '${i + 1}. $providerName on $formattedDate at ${timeSlot['start_time']} - ${timeSlot['end_time']}\n';
            responseMessage += '   Reason: $reason\n\n';
          }

          if (pastAppointments.length > 3) {
            responseMessage +=
                '(Showing 3 most recent past appointments. Ask about "past appointments" to see more.)\n\n';
          }
        } else {
          responseMessage += '📚 PAST APPOINTMENTS: None\n\n';
        }

        responseMessage +=
            'Would you like more details about your upcoming or past appointments?';
      }
      // If specifically asking about past appointments
      else if (askingAboutPast) {
        if (pastAppointments.isEmpty) {
          responseMessage =
              'You don\'t have any past appointments in your history.';
        } else {
          responseMessage = 'Here are your past appointments:\n\n';

          for (int i = 0; i < pastAppointments.length; i++) {
            final appointment = pastAppointments[i];
            final date = DateTime.parse(appointment['date']);
            final formattedDate = '${date.day}/${date.month}/${date.year}';
            final timeSlot = appointment['time_slot'];
            final providerName =
                appointment['provider']?['user']?['name'] ?? 'Unknown Provider';
            final reason = appointment['reason'] ?? 'General consultation';

            responseMessage +=
                '${i + 1}. $providerName on $formattedDate at ${timeSlot['start_time']} - ${timeSlot['end_time']}\n';
            responseMessage += '   Reason: $reason\n\n';
          }
        }
      }
      // If specifically asking about upcoming appointments
      else if (askingAboutUpcoming) {
        if (upcomingAppointments.isEmpty) {
          responseMessage =
              'You don\'t have any upcoming appointments scheduled. Would you like me to help you book an appointment?';
        } else {
          responseMessage = 'Here are your upcoming appointments:\n\n';

          for (int i = 0; i < upcomingAppointments.length; i++) {
            final appointment = upcomingAppointments[i];
            final date = DateTime.parse(appointment['date']);
            final formattedDate = '${date.day}/${date.month}/${date.year}';
            final timeSlot = appointment['time_slot'];
            final providerName =
                appointment['provider']?['user']?['name'] ?? 'Unknown Provider';
            final reason = appointment['reason'] ?? 'General consultation';
            final isTelemedicine = appointment['is_telemedicine'] ?? false;

            responseMessage +=
                '${i + 1}. $providerName on $formattedDate at ${timeSlot['start_time']} - ${timeSlot['end_time']}\n';
            responseMessage += '   Reason: $reason\n';
            if (isTelemedicine) {
              responseMessage += '   Type: Video Consultation\n';
            }
            responseMessage += '\n';
          }

          responseMessage +=
              'Is there anything specific you\'d like to know about these appointments?';
        }
      }
    }

    // Add AI response
    final aiMessage = ChatMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      role: 'assistant',
      content: responseMessage,
      timestamp: DateTime.now(),
    );

    setState(() {
      _messages.add(aiMessage);
      _isTyping = false;
    });

    _scrollToBottom();
  }

  // Check if the AI response contains appointment booking intent
  bool _checkForAppointmentBookingIntent(String message) {
    // Define patterns that indicate appointment booking intent
    final List<String> appointmentPatterns = [
      'I can help you book an appointment',
      'would you like to book an appointment',
      'I recommend booking an appointment',
      'you should see a',
      'you should consult with a',
      'appointment with a',
      'schedule a visit',
      'schedule an appointment',
      'book a consultation',
      'make an appointment',
    ];

    // Check if any of the patterns are in the message (case insensitive)
    final lowerMessage = message.toLowerCase();
    for (final pattern in appointmentPatterns) {
      if (lowerMessage.contains(pattern.toLowerCase())) {
        debugPrint('Detected appointment booking intent: $pattern');
        return true;
      }
    }

    return false;
  }

  // Check if the user is confirming an appointment booking suggestion
  bool _checkForAppointmentConfirmation(String message) {
    final lowerCaseMessage = message.toLowerCase().trim();

    // Patterns that indicate the user is confirming an appointment booking
    final confirmationPatterns = [
      'yes',
      'yeah',
      'sure',
      'ok',
      'okay',
      'book it',
      'book appointment',
      'find slots',
      'find appointment',
      'proceed',
      'go ahead',
      'i want to book',
      'i would like to book',
      'please book',
      'please proceed',
      'that sounds good',
      'sounds good',
      'let\'s do it',
      'let\'s book',
    ];

    // Check for exact matches first (for short responses like "yes")
    if (confirmationPatterns.contains(lowerCaseMessage)) {
      return true;
    }

    // Check for partial matches in longer responses
    for (final pattern in confirmationPatterns) {
      if (lowerCaseMessage.contains(pattern)) {
        return true;
      }
    }

    return false;
  }

  // Extract specialty from previous messages
  String? _extractSpecialtyFromPreviousMessages() {
    // Check the last few messages for specialty mentions
    final messagesToCheck = _messages.length > 5
        ? _messages.sublist(_messages.length - 5)
        : _messages;

    // Map of keywords to specialties
    final specialtyKeywords = {
      'dermatologist': 'Dermatology',
      'skin': 'Dermatology',
      'rash': 'Dermatology',
      'cardiologist': 'Cardiology',
      'heart': 'Cardiology',
      'neurologist': 'Neurology',
      'brain': 'Neurology',
      'headache': 'Neurology',
      'orthopedist': 'Orthopedics',
      'bone': 'Orthopedics',
      'joint': 'Orthopedics',
      'psychiatrist': 'Psychiatry',
      'mental': 'Psychiatry',
      'anxiety': 'Psychiatry',
      'depression': 'Psychiatry',
      'ophthalmologist': 'Ophthalmology',
      'eye': 'Ophthalmology',
      'ent': 'ENT',
      'ear': 'ENT',
      'nose': 'ENT',
      'throat': 'ENT',
      'gastroenterologist': 'Gastroenterology',
      'stomach': 'Gastroenterology',
      'digestive': 'Gastroenterology',
      'obstetrician': 'Obstetrics',
      'pregnancy': 'Obstetrics',
      'gynecologist': 'Gynecology',
      'women': 'Gynecology',
      'pediatrician': 'Pediatrics',
      'child': 'Pediatrics',
    };

    // Check each message for specialty keywords
    for (final message in messagesToCheck.reversed) {
      final content = message.content.toLowerCase();

      for (final entry in specialtyKeywords.entries) {
        if (content.contains(entry.key)) {
          return entry.value;
        }
      }
    }

    return null;
  }

  void _shareConversation() {
    // Create a formatted string of the conversation
    String conversationText = 'AI Doctor Chat\n\n';

    for (final message in _messages) {
      final sender = message.isUser ? 'Me' : 'AI Assistant';
      conversationText += '$sender: ${message.content}\n\n';
    }

    // Share the conversation
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Share Conversation',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Column(
                  children: [
                    IconButton(
                      icon: const Icon(Icons.copy),
                      onPressed: () {
                        Navigator.pop(context);
                        // Copy to clipboard
                        Clipboard.setData(
                            ClipboardData(text: conversationText));
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(content: Text('Copied to clipboard')),
                        );
                      },
                    ),
                    const Text('Copy'),
                  ],
                ),
                Column(
                  children: [
                    IconButton(
                      icon: const Icon(Icons.share),
                      onPressed: () {
                        Navigator.pop(context);
                        // This would use a platform-specific share dialog
                        // For web, we'll just copy to clipboard
                        Clipboard.setData(
                            ClipboardData(text: conversationText));
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                              content: Text('Copied to clipboard for sharing')),
                        );
                      },
                    ),
                    const Text('Share'),
                  ],
                ),
                Column(
                  children: [
                    IconButton(
                      icon: const Icon(Icons.public),
                      onPressed: () async {
                        Navigator.pop(context);
                        if (_conversationId == null) return;

                        // Show confirmation dialog
                        showDialog(
                          context: context,
                          builder: (context) => AlertDialog(
                            title: const Text('Share to Feed'),
                            content: const Text(
                                'This will share your conversation to the public feed where other users can see it. Continue?'),
                            actions: [
                              TextButton(
                                onPressed: () {
                                  Navigator.pop(context);
                                },
                                child: const Text('Cancel'),
                              ),
                              ElevatedButton(
                                onPressed: () async {
                                  Navigator.pop(context);

                                  setState(() {
                                    _isLoading = true;
                                  });

                                  try {
                                    final apiService =
                                        RepositoryProvider.of<ApiService>(
                                            context);

                                    // Share directly to feed
                                    final shared = await apiService
                                        .shareConversationToFeed(
                                            _conversationId!);

                                    if (shared) {
                                      if (mounted) {
                                        ScaffoldMessenger.of(context)
                                            .showSnackBar(
                                          const SnackBar(
                                            content: Text(
                                                'Conversation shared to discovery feed'),
                                          ),
                                        );
                                      }
                                    } else {
                                      if (mounted) {
                                        ScaffoldMessenger.of(context)
                                            .showSnackBar(
                                          const SnackBar(
                                            content: Text(
                                                'Failed to share to discovery feed'),
                                          ),
                                        );
                                      }
                                    }
                                  } catch (e) {
                                    if (mounted) {
                                      ScaffoldMessenger.of(context)
                                          .showSnackBar(
                                        SnackBar(
                                          content: Text(
                                              'Error sharing conversation: $e'),
                                        ),
                                      );
                                    }
                                  } finally {
                                    setState(() {
                                      _isLoading = false;
                                    });
                                  }
                                },
                                child: const Text('Share'),
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                    const Text('Post to Feed'),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _showRecommendationsBottomSheet() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Health Recommendations',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: 8),
              if (_escalated)
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.red.shade100,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          const Icon(
                            Icons.warning_amber_rounded,
                            color: Colors.red,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              'This conversation has been escalated. Please consult with a healthcare professional as soon as possible.',
                              style: TextStyle(
                                  color: Colors.red.shade800,
                                  fontWeight: FontWeight.bold),
                            ),
                          ),
                        ],
                      ),
                      if (_escalationReason != null) ...[
                        const SizedBox(height: 8),
                        Text(
                          'Reason: $_escalationReason',
                          style: TextStyle(color: Colors.red.shade800),
                        ),
                      ],
                    ],
                  ),
                ),
              if (_healthConcerns.isNotEmpty) ...[
                const SizedBox(height: 16),
                Text(
                  'Health Topics',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const SizedBox(height: 8),
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: _healthConcerns.map((topic) {
                    return Chip(
                      label: Text(topic),
                      backgroundColor: const Color.fromRGBO(23, 195, 178, 0.1),
                    );
                  }).toList(),
                ),
              ],
              if (_recommendations.isNotEmpty) ...[
                const SizedBox(height: 16),
                Text(
                  'Recommendations',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const SizedBox(height: 8),
                ..._recommendations.map((recommendation) {
                  return Card(
                    margin: const EdgeInsets.only(bottom: 8),
                    child: Padding(
                      padding: const EdgeInsets.all(12),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            padding: const EdgeInsets.all(8),
                            decoration: const BoxDecoration(
                              color: Color.fromRGBO(23, 195, 178, 0.1),
                              shape: BoxShape.circle,
                            ),
                            child: _getIconForRecommendationType(
                                recommendation.type),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  recommendation.type.capitalize(),
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(recommendation.content),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                }).toList(),
              ],
              if (_healthConcerns.isEmpty && _recommendations.isEmpty)
                const Center(
                  child: Padding(
                    padding: EdgeInsets.all(24),
                    child: Text(
                      'No recommendations yet. Continue your conversation to get personalized health advice.',
                      textAlign: TextAlign.center,
                      style: TextStyle(color: Colors.grey),
                    ),
                  ),
                ),
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  TextButton.icon(
                    icon: const Icon(Icons.medical_services_outlined),
                    label: const Text('Find Providers'),
                    onPressed: () {
                      // Navigate to provider marketplace
                      Navigator.pop(context);
                      Navigator.pushNamed(context, '/providers');
                    },
                  ),
                  const SizedBox(width: 12),
                  ElevatedButton.icon(
                    icon: const Icon(Icons.calendar_today, size: 16),
                    label: const Text('Request Appointment'),
                    onPressed: () {
                      Navigator.pop(context);
                      _showAppointmentRequestModal();
                    },
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Icon _getIconForRecommendationType(String type) {
    switch (type.toLowerCase()) {
      case 'medication':
      case 'medication_info':
        return const Icon(Icons.medication, color: AppColors.tealSurge);
      case 'lifestyle':
        return const Icon(Icons.self_improvement, color: AppColors.tealSurge);
      case 'specialist':
        return const Icon(Icons.medical_services, color: AppColors.tealSurge);
      case 'resource':
        return const Icon(Icons.menu_book, color: AppColors.tealSurge);
      case 'followup':
        return const Icon(Icons.event_note, color: AppColors.tealSurge);
      case 'prevention':
        return const Icon(Icons.health_and_safety, color: AppColors.tealSurge);
      default:
        return const Icon(Icons.recommend, color: AppColors.tealSurge);
    }
  }

  void _showAppointmentRequestModal() {
    final serviceCategories = [
      'General Practice',
      'Cardiology',
      'Dermatology',
      'Neurology',
      'Psychiatry',
      'Orthopedics',
      'Gynecology',
      'Pediatrics',
    ];

    String selectedCategory = (_healthConcerns.isNotEmpty)
        ? serviceCategories.first
        : 'General Practice';

    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('Request Virtual Appointment'),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Based on your conversation, we recommend scheduling a virtual appointment with a healthcare professional.',
                      style: TextStyle(fontWeight: FontWeight.w500),
                    ),
                    const SizedBox(height: 16),
                    const Text('Preferred service category:'),
                    DropdownButton<String>(
                      isExpanded: true,
                      value: selectedCategory,
                      items: serviceCategories.map((category) {
                        return DropdownMenuItem<String>(
                          value: category,
                          child: Text(category),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          selectedCategory = value!;
                        });
                      },
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'We\'ll show you available appointment slots with providers who offer services in this category.',
                      style: TextStyle(
                        fontStyle: FontStyle.italic,
                        fontSize: 12,
                        color: Colors.grey.shade700,
                      ),
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  child: const Text('Cancel'),
                ),
                ElevatedButton(
                  onPressed: () {
                    Navigator.pop(context);
                    // Show confirmation dialog before loading slots
                    showDialog(
                      context: context,
                      builder: (context) => AlertDialog(
                        title: const Text('Book an Appointment?'),
                        content: Text(
                            'Would you like me to find available appointment slots with a $selectedCategory?'),
                        actions: [
                          TextButton(
                            onPressed: () {
                              Navigator.pop(context);
                            },
                            child: const Text('Not Now'),
                          ),
                          ElevatedButton(
                            onPressed: () {
                              Navigator.pop(context);
                              _requestAppointment(selectedCategory);
                            },
                            child: const Text('Find Available Slots'),
                          ),
                        ],
                      ),
                    );
                  },
                  child: const Text('Continue'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  // Handle a user request to book a new appointment
  Future<void> _handleAppointmentBookingRequest(String message) async {
    // Add user message to chat
    final userMessage = ChatMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      role: 'user',
      content: message,
      timestamp: DateTime.now(),
      contentType: MessageContentType.text,
    );

    setState(() {
      _messages.add(userMessage);
      _isTyping = true;
    });

    _messageController.clear();
    _scrollToBottom();

    // Extract potential condition from the message
    String? detectedCondition = _extractConditionFromMessage(message);

    // Add AI response acknowledging the booking request
    final aiMessage = ChatMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      role: 'assistant',
      content:
          "I'll help you book an appointment${detectedCondition != null ? ' for your $detectedCondition' : ''}. Let me find available healthcare providers for you.",
      timestamp: DateTime.now(),
    );

    setState(() {
      _messages.add(aiMessage);
      _isTyping = false;
    });

    _scrollToBottom();

    // Ask for confirmation within the chat
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        // Add a confirmation message from the AI
        final confirmationMessage = ChatMessage(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          role: 'assistant',
          content:
              "Would you like me to find available appointment slots with a healthcare provider? Please respond with 'Yes' if you'd like to proceed.",
          timestamp: DateTime.now(),
        );

        setState(() {
          _messages.add(confirmationMessage);
        });

        _scrollToBottom();
      }
    });
  }

  // Extract potential health condition from the message
  String? _extractConditionFromMessage(String message) {
    final lowerCaseMessage = message.toLowerCase();

    // Common patterns for conditions in booking requests
    final conditionPatterns = [
      RegExp(
          r'(?:for|about)\s+(?:my|a)\s+([a-z\s]+)(?:issue|problem|condition|pain|rash|infection)'),
      RegExp(
          r'(?:have|got)\s+(?:a|an)\s+([a-z\s]+)(?:issue|problem|condition|pain|rash|infection)'),
      RegExp(
          r'(?:my|with|for)\s+([a-z\s]+)(?:issue|problem|condition|pain|rash|infection)'),
    ];

    for (final pattern in conditionPatterns) {
      final match = pattern.firstMatch(lowerCaseMessage);
      if (match != null && match.groupCount >= 1) {
        return match.group(1)?.trim();
      }
    }

    // Check for specific conditions mentioned directly
    final specificConditions = [
      'skin rash',
      'headache',
      'migraine',
      'back pain',
      'stomach pain',
      'fever',
      'cough',
      'cold',
      'flu',
      'allergy',
      'infection',
      'anxiety',
      'depression',
      'insomnia',
      'diabetes',
      'hypertension'
    ];

    for (final condition in specificConditions) {
      if (lowerCaseMessage.contains(condition)) {
        return condition;
      }
    }

    return null;
  }

  // Extract potential specialty from the message

  // Ask for appointment confirmation within the chat
  void _askForAppointmentConfirmationInChat() {
    // Add a confirmation message from the AI - always use general healthcare provider
    final confirmationMessage = ChatMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      role: 'assistant',
      content:
          "Would you like me to find available appointment slots with a healthcare provider? Please respond with 'Yes' if you'd like to proceed.",
      timestamp: DateTime.now(),
    );

    setState(() {
      _messages.add(confirmationMessage);
    });

    _scrollToBottom();
  }

  Future<void> _requestAppointment(String? serviceCategory) async {
    if (_conversationId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
            content:
                Text('Unable to request appointment: No active conversation')),
      );
      return;
    }

    // Add a message to indicate we're checking if we're ready to book
    final loadingMessage = ChatMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      role: 'assistant',
      content: "I'm checking if we're ready to book an appointment...",
      timestamp: DateTime.now(),
    );

    setState(() {
      _messages.add(loadingMessage);
      _isLoading = true;
    });

    _scrollToBottom();

    try {
      final apiService = RepositoryProvider.of<ApiService>(context);

      // Log the request for debugging
      debugPrint('Requesting appointment for conversation: $_conversationId');

      final result = await apiService.requestAiAppointment(
        conversationId: _conversationId!,
        serviceCategory: serviceCategory,
      );

      // Log the response for debugging
      debugPrint('Appointment request response: $result');

      if (mounted) {
        setState(() {
          _isLoading = false;

          // Remove the loading message since the backend will add appropriate messages
          if (_messages.isNotEmpty &&
              _messages.last.role == 'assistant' &&
              _messages.last.content.contains("I'm checking if we're ready")) {
            _messages.removeLast();
          }
        });

        // Check if we need more conversation before showing appointments
        if (result.containsKey('needs_more_context') &&
            result['needs_more_context'] == true) {
          debugPrint('More conversation needed before booking appointment');
          // The backend has already added a message asking for more information
          // We'll just fetch the updated conversation
          _fetchConversation();
          return;
        }

        // Check if we need explicit consent before showing appointments
        if (result.containsKey('needs_consent') &&
            result['needs_consent'] == true) {
          debugPrint('Waiting for explicit consent before booking appointment');
          // The backend has already added a message asking for consent
          // We'll just fetch the updated conversation
          _fetchConversation();
          return;
        }

        // If we get here, we have consent and enough context to show appointments
        // Check if there are available slots
        if (!result.containsKey('available_slots')) {
          debugPrint('No available_slots key in response');
          _fetchConversation();
          return;
        }

        final availableSlots = result['available_slots'] as List<dynamic>;

        // Store the referral note for use when booking the appointment
        _referralNote = result['referral_note'] as String?;
        debugPrint('Stored referral note: $_referralNote');

        if (availableSlots.isEmpty) {
          // No available slots found
          showDialog(
            context: context,
            builder: (context) => AlertDialog(
              title: const Text('No Available Slots'),
              content: const Text(
                  'We couldn\'t find any available appointment slots in the next 7 days. Please check again later.'),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  child: const Text('OK'),
                ),
              ],
            ),
          );
        } else {
          // Sort slots by date and time to find the earliest available appointment
          final sortedSlots = List<Map<String, dynamic>>.from(availableSlots);
          sortedSlots.sort((a, b) {
            // First compare dates
            final dateA = DateTime.parse(a['date']);
            final dateB = DateTime.parse(b['date']);
            final dateComparison = dateA.compareTo(dateB);

            if (dateComparison != 0) {
              return dateComparison;
            }

            // If dates are the same, compare the earliest time slot
            final slotsA = a['slots'] as List;
            final slotsB = b['slots'] as List;

            if (slotsA.isEmpty) return 1;
            if (slotsB.isEmpty) return -1;

            final earliestSlotA = slotsA.first;
            final earliestSlotB = slotsB.first;

            final startTimeA = earliestSlotA['start_time'];
            final startTimeB = earliestSlotB['start_time'];

            return startTimeA.compareTo(startTimeB);
          });

          // Get the earliest available appointment
          final earliestSlot = sortedSlots.first;
          final earliestProvider = earliestSlot['provider'];
          final earliestDate = earliestSlot['date'];
          final earliestDayOfWeek = earliestSlot['day_of_week'];
          final earliestTimeSlots = earliestSlot['slots'] as List;
          final earliestTime = earliestTimeSlots.isNotEmpty
              ? '${earliestTimeSlots.first['start_time']} - ${earliestTimeSlots.first['end_time']}'
              : 'No specific time available';

          // Get unique providers from available slots
          final Set<String> providerIds = {};
          final List<Map<String, dynamic>> uniqueProviders = [];

          for (final slot in availableSlots) {
            final provider = slot['provider'];
            final providerId = provider['id'].toString();

            if (!providerIds.contains(providerId)) {
              providerIds.add(providerId);
              uniqueProviders.add(provider);
            }
          }

          // Show dialog with earliest appointment and provider list
          showDialog(
            context: context,
            builder: (context) => AlertDialog(
              title: const Text('Available Appointments'),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Earliest available appointment section
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.green.withAlpha(25), // 0.1 opacity
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                            color: Colors.green.withAlpha(76)), // 0.3 opacity
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Row(
                            children: [
                              Icon(Icons.access_time,
                                  color: Colors.green, size: 16),
                              SizedBox(width: 8),
                              Text(
                                'Earliest Available Appointment',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: Colors.green,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Text('Provider: ${earliestProvider['name']}'),
                          Text('Date: $earliestDayOfWeek, $earliestDate'),
                          Text('Time: $earliestTime'),
                        ],
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Available providers section
                    const Text(
                      'Available Providers',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 8),
                    ...uniqueProviders.map((provider) => ListTile(
                          contentPadding: EdgeInsets.zero,
                          leading: CircleAvatar(
                            child: Text(provider['name'].substring(0, 1)),
                          ),
                          title: Text(provider['name']),
                          subtitle: Text(provider['specialization'] ??
                              'General Practitioner'),
                        )),
                    const SizedBox(height: 16),
                    const Text(
                      'Would you like to see all available appointment slots?',
                      style: TextStyle(fontStyle: FontStyle.italic),
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  child: const Text('Cancel'),
                ),
                ElevatedButton(
                  onPressed: () {
                    Navigator.pop(context);
                    _showAppointmentSlotSelector(availableSlots);
                  },
                  child: const Text('View All Slots'),
                ),
              ],
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;

          // Remove the loading message
          if (_messages.isNotEmpty &&
              _messages.last.role == 'assistant' &&
              _messages.last.content.contains("I'm checking if we're ready")) {
            _messages.removeLast();
          }
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error requesting appointment: $e')),
        );
      }
    }
  }

  // Fetch the latest conversation to get updated messages
  Future<void> _fetchConversation() async {
    try {
      if (_conversationId == null || !mounted) return;

      final apiService = RepositoryProvider.of<ApiService>(context);
      final conversation = await apiService.getConversation(_conversationId!);

      if (mounted && conversation['messages'] != null) {
        final List<dynamic> messages = conversation['messages'];

        setState(() {
          _messages = messages
              .map((msg) => ChatMessage(
                    id: msg['timestamp'] ??
                        DateTime.now().millisecondsSinceEpoch.toString(),
                    role: msg['role'] ?? 'unknown',
                    content: msg['content'] ?? '',
                    timestamp: msg['timestamp'] != null
                        ? DateTime.parse(msg['timestamp'])
                        : DateTime.now(),
                  ))
              .toList();
        });

        _scrollToBottom();
      }
    } catch (e) {
      debugPrint('Error fetching conversation: $e');
    }
  }

  void _showAppointmentSlotSelector(List<dynamic> availableSlots) {
    // TEMPORARY FIX: Sort slots by date and time to show earliest first
    final sortedSlots = List<Map<String, dynamic>>.from(availableSlots);
    sortedSlots.sort((a, b) {
      // First compare dates
      final dateA = DateTime.parse(a['date']);
      final dateB = DateTime.parse(b['date']);
      final dateComparison = dateA.compareTo(dateB);

      if (dateComparison != 0) {
        return dateComparison;
      }

      // If dates are the same, compare the earliest time slot
      final slotsA = a['slots'] as List;
      final slotsB = b['slots'] as List;

      if (slotsA.isEmpty) return 1;
      if (slotsB.isEmpty) return -1;

      final earliestSlotA = slotsA.first;
      final earliestSlotB = slotsB.first;

      final startTimeA = earliestSlotA['start_time'];
      final startTimeB = earliestSlotB['start_time'];

      return startTimeA.compareTo(startTimeB);
    });

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return Container(
          padding: const EdgeInsets.all(16),
          height: MediaQuery.of(context).size.height * 0.8,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Available Appointment Slots',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: 16),
              Expanded(
                child: AppointmentSlotSelector(
                  availableSlots: sortedSlots, // Use the sorted slots
                  onSlotSelected: (selectedSlot, selectedTime) {
                    Navigator.pop(context);
                    // Convert from provider.TimeSlot to time_slot.TimeSlot
                    final timeSlot = TimeSlot(
                      startTime: selectedTime.startTime,
                      endTime: selectedTime.endTime,
                    );
                    _bookAppointment(selectedSlot, timeSlot);
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Future<void> _bookAppointment(
      Map<String, dynamic> selectedSlot, TimeSlot selectedTime) async {
    setState(() {
      _isLoading = true;
    });

    try {
      final apiService = RepositoryProvider.of<ApiService>(context);

      // Prepare appointment data
      final providerId = selectedSlot['provider']['id'].toString();
      final date = selectedSlot['date'];
      final timeSlot = {
        'start_time': selectedTime.startTime,
        'end_time': selectedTime.endTime,
      };

      // Include service_id if available
      String? serviceId;
      if (selectedSlot.containsKey('service') &&
          selectedSlot['service'] != null) {
        serviceId = selectedSlot['service']['id'].toString();
      }

      // Use the stored referral note as the reason, or fallback to a generic message
      final appointmentReason = _referralNote?.isNotEmpty == true
          ? _referralNote!
          : 'Appointment requested via AI chat assistant';

      debugPrint('Using appointment reason: $appointmentReason');

      // Book the appointment (initially without payment)
      final appointmentData = await apiService.bookAppointment(
        providerId: providerId,
        date: date,
        timeSlot: timeSlot,
        reason: appointmentReason,
        serviceId: serviceId,
        requirePayment:
            true, // This will mark the appointment as requiring payment
      );

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        // Navigate to payment screen
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => PaymentScreen(
              appointmentData: appointmentData,
              onPaymentComplete: (success, paymentId) {
                if (success) {
                  // Payment was successful
                  setState(() {
                    _appointmentBooked = true;
                  });

                  // Add a prominent appointment confirmation message
                  final appointmentMessage = ChatMessage(
                    id: DateTime.now().millisecondsSinceEpoch.toString(),
                    role: 'assistant',
                    content: '🎉 **APPOINTMENT CONFIRMED** 🎉\n\n'
                        '✅ **Provider:** ${selectedSlot['provider']['name']}\n'
                        '📅 **Date:** ${selectedSlot['day_of_week']}, $date\n'
                        '⏰ **Time:** ${selectedTime.startTime} - ${selectedTime.endTime}\n'
                        '💳 **Payment:** Completed Successfully\n'
                        '📧 **Confirmation:** Details sent to your email\n\n'
                        '**Your appointment is now confirmed and secured!**',
                    timestamp: DateTime.now(),
                    contentType: MessageContentType.appointment,
                  );

                  setState(() {
                    _messages.add(appointmentMessage);
                  });
                  _scrollToBottom();

                  // Save the appointment message to the backend
                  _saveAppointmentMessageToBackend(appointmentMessage);

                  // Add an AI response acknowledging the booking
                  Future.delayed(const Duration(milliseconds: 1000), () {
                    if (mounted) {
                      final aiMessage = ChatMessage(
                        id: DateTime.now().millisecondsSinceEpoch.toString(),
                        role: 'assistant',
                        content:
                            'Perfect! Your appointment is confirmed. I\'ve sent you all the details and you\'ll receive a reminder 24 hours before your appointment.\n\nIs there anything else I can help you with regarding your health concerns or do you have any other questions?',
                        timestamp: DateTime.now(),
                      );

                      setState(() {
                        _messages.add(aiMessage);
                      });
                      _scrollToBottom();

                      // Save the follow-up AI message to the backend
                      _saveAppointmentMessageToBackend(aiMessage);
                    }
                  });

                  // Show success confirmation after returning from payment screen
                  showDialog(
                    context: context,
                    builder: (context) => AlertDialog(
                      title: const Text('Appointment Booked'),
                      content: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                              'Your appointment has been booked and payment processed successfully.'),
                          const SizedBox(height: 16),
                          const Text('Details:'),
                          const SizedBox(height: 8),
                          Text(
                              '• Provider: ${selectedSlot['provider']['name']}'),
                          Text('• Date: ${selectedSlot['day_of_week']}, $date'),
                          Text(
                              '• Time: ${selectedTime.startTime} - ${selectedTime.endTime}'),
                          if (selectedSlot.containsKey('service') &&
                              selectedSlot['service'] != null)
                            Text(
                                '• Service: ${selectedSlot['service']['name']}'),
                          if (paymentId != null)
                            Text('• Payment ID: $paymentId'),
                          const SizedBox(height: 16),
                          const Text(
                            'You can view and manage your appointments in the profile section.',
                            style: TextStyle(fontStyle: FontStyle.italic),
                          ),
                        ],
                      ),
                      actions: [
                        TextButton(
                          onPressed: () {
                            Navigator.pop(context);
                          },
                          child: const Text('OK'),
                        ),
                      ],
                    ),
                  );
                } else {
                  // Payment failed
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text(
                          'Payment failed. Your appointment has not been confirmed.'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              },
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error booking appointment: $e')),
        );
      }
    }
  }

  // Chat history methods
  Future<void> _showChatHistoryDialog() async {
    // Close any existing dialog first
    if (mounted) {
      Navigator.of(context, rootNavigator: true).popUntil((route) {
        return route.settings.name != 'chat_history_dialog';
      });
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final apiService = RepositoryProvider.of<ApiService>(context);
      // Get chat history, filtering out empty conversations
      final conversations = await apiService.getChatHistory();

      setState(() {
        _isLoading = false;
      });

      if (conversations.isEmpty) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('No previous conversations found')),
          );
        }
        return;
      }

      if (mounted) {
        showDialog(
          context: context,
          routeSettings: const RouteSettings(name: 'chat_history_dialog'),
          builder: (context) => AlertDialog(
            title: const Text('Chat History'),
            content: SizedBox(
              width: double.maxFinite,
              height: 300,
              child: ListView.builder(
                itemCount: conversations.length,
                itemBuilder: (context, index) {
                  final conversation = conversations[index];
                  final date = conversation['updated_at'] != null
                      ? DateTime.parse(conversation['updated_at'])
                      : DateTime.now();

                  // Get the conversation title or generate one from the first message
                  String previewText =
                      conversation['title'] ?? 'New Conversation';

                  // If no title exists, try to generate one from the first user message
                  if (previewText == 'New Conversation' &&
                      conversation['messages'] is List &&
                      conversation['messages'].isNotEmpty) {
                    final firstUserMsg = conversation['messages'].firstWhere(
                        (m) => m['role'] == 'user',
                        orElse: () => {'content': 'New conversation'});
                    previewText = firstUserMsg['content'];
                    if (previewText.length > 50) {
                      previewText = '${previewText.substring(0, 47)}...';
                    }
                  }

                  // Format date
                  final formattedDate =
                      '${date.day}/${date.month}/${date.year} ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';

                  return Card(
                    margin: const EdgeInsets.symmetric(vertical: 6),
                    child: Dismissible(
                      key: Key(conversation['id'].toString()),
                      background: Container(
                        color: Colors.red,
                        alignment: Alignment.centerRight,
                        padding: const EdgeInsets.only(right: 16),
                        child: const Icon(Icons.delete, color: Colors.white),
                      ),
                      direction: DismissDirection.endToStart,
                      confirmDismiss: (direction) async {
                        return await showDialog(
                          context: context,
                          builder: (BuildContext context) {
                            return AlertDialog(
                              title: const Text('Confirm Delete'),
                              content: const Text(
                                  'Are you sure you want to delete this conversation?'),
                              actions: [
                                TextButton(
                                  onPressed: () =>
                                      Navigator.of(context).pop(false),
                                  child: const Text('Cancel'),
                                ),
                                TextButton(
                                  onPressed: () =>
                                      Navigator.of(context).pop(true),
                                  child: const Text('Delete',
                                      style: TextStyle(color: Colors.red)),
                                ),
                              ],
                            );
                          },
                        );
                      },
                      onDismissed: (direction) async {
                        final apiService =
                            RepositoryProvider.of<ApiService>(context);
                        final success = await apiService
                            .deleteConversation(conversation['id'].toString());

                        if (success && mounted) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                                content: Text('Conversation deleted')),
                          );
                        } else if (mounted) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                                content: Text('Failed to delete conversation')),
                          );
                          // Refresh the list to show the conversation again
                          _showChatHistoryDialog();
                        }
                      },
                      child: ListTile(
                        title: Text(previewText,
                            maxLines: 1, overflow: TextOverflow.ellipsis),
                        subtitle: Row(
                          children: [
                            Text(formattedDate),
                            const SizedBox(width: 8),
                            if (conversation['escalated'] == true)
                              const Icon(Icons.warning_amber_rounded,
                                  color: Colors.red, size: 14),
                          ],
                        ),
                        trailing: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            IconButton(
                              icon: const Icon(Icons.edit, size: 18),
                              onPressed: () {
                                _showEditTitleDialog(conversation);
                              },
                            ),
                            const Icon(Icons.arrow_forward_ios, size: 16),
                          ],
                        ),
                        onTap: () {
                          Navigator.pop(context);
                          _loadExistingConversation(
                              conversation['id'].toString());
                        },
                      ),
                    ),
                  );
                },
              ),
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                },
                child: const Text('Close'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading chat history: $e')),
        );
      }
    }
  }

  // Navigate to chat history screen
  void _navigateToChatHistory() {
    // Navigate to the main screen and replace the current screen
    // This ensures we go back to the main navigation with bottom bar
    Navigator.of(context).pushNamedAndRemoveUntil('/main', (route) => false);
  }

  // Start a new chat
  void _startNewChat() {
    // Clear the current conversation and start a new one
    setState(() {
      _messages = [];
      _conversationId = null;
      _isLoading = true;
      _hasError = false;
    });

    // Start a new conversation
    _startNewConversation();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      appBar: AppBar(
        backgroundColor:
            AppColors.backgroundLight, // Match Discover screen background
        foregroundColor: Colors.black, // Set icon color to black for contrast
        automaticallyImplyLeading: false, // Remove back/close button
        centerTitle: false, // Left-aligned title like anonymous chat
        titleSpacing: 16, // Add some padding on the left
        title: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Image.asset(
              'assets/images/medroid_icon.png',
              height: 32,
              width: 32,
              errorBuilder: (context, error, stackTrace) {
                return const MedroidLogo(
                  size: 32,
                  color: AppColors.tealSurge,
                  useDarkVersion: false,
                );
              },
            ),
            const SizedBox(width: 8),
            const Text('Medroid'),
          ],
        ),
        elevation: 0,
        scrolledUnderElevation: 0,
        actions: [
          // Compact Referral Gift Button
          GestureDetector(
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const ReferralScreen(),
                ),
              );
            },
            child: Container(
              margin: const EdgeInsets.only(right: 8),
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Color(0xFF0D9488), Color(0xFF0F766E)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(8),
                boxShadow: const [
                  BoxShadow(
                    color: Color(0x330D9488), // 20% opacity teal
                    blurRadius: 4,
                    spreadRadius: 0,
                    offset: Offset(0, 1),
                  ),
                ],
              ),
              child: const Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.card_giftcard,
                    color: Colors.white,
                    size: 16,
                  ),
                  SizedBox(width: 4),
                  Text(
                    '\$3',
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                      fontSize: 13,
                    ),
                  ),
                ],
              ),
            ),
          ),
          const NotificationBell(),
          // New Chat Icon (plus icon, lined style)
          IconButton(
            icon: const Icon(
              Icons.add_outlined,
              color: Color(0xFF0D9488),
              size: 26,
            ),
            tooltip: 'New Chat',
            onPressed: _startNewChat,
            padding: const EdgeInsets.all(8),
            constraints: const BoxConstraints(),
            visualDensity: VisualDensity.compact,
          ),
        ],
      ),
      backgroundColor:
          AppColors.backgroundLight, // Match Discover screen background
      body: Column(
        children: [
          // Escalation Banner
          if (_escalated)
            Container(
              width: double.infinity,
              margin: const EdgeInsets.fromLTRB(16, 12, 16, 0),
              padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
              decoration: BoxDecoration(
                color: isDarkMode
                    ? const Color(0xFF3A2A2A)
                    : const Color(0xFFFFF0F0),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: isDarkMode
                      ? const Color(0xFF5A3A3A)
                      : const Color(0xFFFFD6D6),
                  width: 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.red.withAlpha(25), // 0.1 * 255
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                children: [
                  Container(
                    width: 36,
                    height: 36,
                    decoration: BoxDecoration(
                      color: isDarkMode
                          ? const Color(0xFF5A3A3A)
                          : const Color(0xFFFFE6E6),
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.priority_high_rounded,
                      color: Colors.red,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Medical Attention Needed',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: isDarkMode
                                ? Colors.red.shade300
                                : Colors.red.shade700,
                          ),
                        ),
                        if (_escalationReason != null) ...[
                          const SizedBox(height: 4),
                          Text(
                            _escalationReason!,
                            style: TextStyle(
                              color: isDarkMode
                                  ? Colors.red.shade200
                                  : Colors.red.shade800,
                              fontSize: 13,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.red.shade50,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.red.shade200),
                    ),
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        borderRadius: BorderRadius.circular(12),
                        onTap: () {
                          // Show appointment modal
                          _showAppointmentRequestModal();
                        },
                        child: Padding(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 12, vertical: 8),
                          child: Text(
                            'Request Appointment',
                            style: TextStyle(
                              color: Colors.red.shade700,
                              fontSize: 13,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),

          // Main Chat Area
          Expanded(
            child: _hasError
                ? _buildErrorState()
                : _isLoading
                    ? _buildLoadingState()
                    : _messages.isEmpty
                        ? PremiumEmptyState(
                            onSuggestionTap: (suggestion) {
                              _sendMessage(predefinedMessage: suggestion);
                            },
                          )
                        : _buildPremiumChatMessages(),
          ),

          // Chat Input
          UniversalChatInput(
            onSendMessage: (message, {File? imageFile}) => _sendMessage(
              predefinedMessage: message,
              imageFile: imageFile,
            ),
            conversationId: _conversationId,
            hintText: 'Type your health question...',
            isEnabled: !_isLoading,
          ),
        ],
      ),
    );
  }

  // Error State Widget
  Widget _buildErrorState() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Center(
      child: Container(
        margin: const EdgeInsets.all(24),
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: isDarkMode ? const Color(0xFF2A2D31) : Colors.white,
          borderRadius: BorderRadius.circular(24),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(13),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 72,
              height: 72,
              decoration: BoxDecoration(
                color: isDarkMode
                    ? const Color(0xFF3A3D41)
                    : const Color(0xFFF5F7FA),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.error_outline_rounded,
                size: 36,
                color: isDarkMode ? Colors.red.shade300 : Colors.red.shade400,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'Connection Error',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: isDarkMode ? Colors.white : Colors.black87,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              _errorMessage,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                color: isDarkMode ? Colors.grey.shade400 : Colors.grey.shade700,
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _startNewConversation,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.tealSurge,
                foregroundColor: Colors.white,
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                elevation: 0,
              ),
              child: const Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.refresh_rounded, size: 20),
                  SizedBox(width: 8),
                  Text(
                    'Try Again',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Loading State Widget
  Widget _buildLoadingState() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: isDarkMode ? const Color(0xFF2A2D31) : Colors.white,
              shape: BoxShape.circle,
              boxShadow: const [
                BoxShadow(
                  color: Color.fromRGBO(23, 195, 178, 0.1),
                  blurRadius: 10,
                  offset: Offset(0, 2),
                ),
              ],
            ),
            child: const Padding(
              padding: EdgeInsets.all(12),
              child: CircularProgressIndicator(
                strokeWidth: 3,
                valueColor: AlwaysStoppedAnimation<Color>(
                  AppColors.tealSurge,
                ),
              ),
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'Loading conversation...',
            style: TextStyle(
              fontSize: 14,
              color: isDarkMode ? Colors.grey.shade400 : Colors.grey.shade700,
            ),
          ),
        ],
      ),
    );
  }

  // Empty State Widget
  Widget _buildEmptyState() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Center(
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: isDarkMode
                    ? const Color.fromRGBO(23, 195, 178, 0.2)
                    : const Color.fromRGBO(23, 195, 178, 0.1),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.health_and_safety_rounded,
                size: 40,
                color: AppColors.tealSurge,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'Hi, I\'m an AI Doctor',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: isDarkMode ? Colors.white : Colors.black87,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              'Ask me anything about your health, symptoms, medications, or wellness advice.',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                color: isDarkMode ? Colors.grey.shade400 : Colors.grey.shade700,
              ),
            ),
            const SizedBox(height: 32),

            // Quick suggestion buttons
            Wrap(
              spacing: 12,
              runSpacing: 12,
              children: [
                _buildSuggestionButton(
                  'I\'ve got a headache',
                  Icons.sick,
                  Colors.red,
                ),
                _buildSuggestionButton(
                  'My ear hurts, help!',
                  Icons.hearing,
                  Colors.purple,
                ),
                _buildSuggestionButton(
                  'Tell me how to lose weight',
                  Icons.fitness_center,
                  Colors.green,
                ),
                _buildSuggestionButton(
                  'How to lower cholesterol?',
                  Icons.favorite,
                  Colors.orange,
                ),
                _buildSuggestionButton(
                  'I have a skin rash, book appointment',
                  Icons.calendar_month,
                  Colors.blue,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // Suggestion Button Widget
  Widget _buildSuggestionButton(String text, IconData icon, Color color) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          _sendMessage(predefinedMessage: text);
        },
        borderRadius: BorderRadius.circular(16),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: isDarkMode
                ? color.withAlpha(38)
                : color.withAlpha(25), // 0.15 and 0.1 * 255
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: isDarkMode
                  ? color.withAlpha(77)
                  : color.withAlpha(51), // 0.3 and 0.2 * 255
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                size: 18,
                color: color,
              ),
              const SizedBox(width: 8),
              Flexible(
                child: Text(
                  text,
                  style: TextStyle(
                    color: isDarkMode ? Colors.white : Colors.black87,
                    fontSize: 14,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Chat Messages Widget
  Widget _buildChatMessages() {
    return Center(
      child: Container(
        constraints: BoxConstraints(
          maxWidth: ResponsiveUtils.isDesktop(context)
              ? 900 // Match desktop chat input width
              : ResponsiveUtils.isTablet(context)
                  ? 700 // Match tablet chat input width
                  : double.infinity, // Full width for mobile
        ),
        child: ListView.builder(
          controller: _scrollController,
          padding: const EdgeInsets.fromLTRB(16, 16, 16, 80),
          itemCount: _messages.length + 1, // +1 for bottom padding
          itemBuilder: (context, index) {
            // Add extra bottom padding at the end of the list
            if (index == _messages.length) {
              return const SizedBox(height: 24);
            }

            final message = _messages[index];
            return Padding(
              padding: EdgeInsets.only(
                // Add extra top padding for the first message
                top: index == 0 ? 8 : 0,
                // Add extra bottom padding for the last message
                bottom: index == _messages.length - 1 ? 8 : 0,
              ),
              child: ChatMessageWidget(
                message: message,
                onLoadMore: message.isTruncated && !message.isUser
                    ? () => _loadMoreContent(message.content)
                    : null,
              ),
            );
          },
        ),
      ),
    );
  }

  // Chat Messages Widget - using the same style as anonymous chat
  Widget _buildPremiumChatMessages() {
    return Center(
      child: Container(
        constraints: BoxConstraints(
          maxWidth: ResponsiveUtils.isDesktop(context)
              ? 900 // Match desktop chat input width
              : ResponsiveUtils.isTablet(context)
                  ? 700 // Match tablet chat input width
                  : double.infinity, // Full width for mobile
        ),
        child: ListView.builder(
          controller: _scrollController,
          padding: const EdgeInsets.fromLTRB(16, 16, 16, 80),
          itemCount: _messages.length + 1, // +1 for bottom padding
          itemBuilder: (context, index) {
            // Add extra bottom padding at the end of the list
            if (index == _messages.length) {
              return const SizedBox(height: 24);
            }

            final message = _messages[index];
            return Padding(
              padding: EdgeInsets.only(
                // Add extra top padding for the first message
                top: index == 0 ? 8 : 0,
                // Add extra bottom padding for the last message
                bottom: index == _messages.length - 1 ? 8 : 0,
              ),
              child: ChatMessageWidget(
                message: message,
                onLoadMore: message.isTruncated && !message.isUser
                    ? () => _loadMoreContent(message.content)
                    : null,
              ),
            );
          },
        ),
      ),
    );
  }
}

class SymptomCheckerWidget extends StatefulWidget {
  final Function(String) onSubmit;

  const SymptomCheckerWidget({
    Key? key,
    required this.onSubmit,
  }) : super(key: key);

  @override
  _SymptomCheckerWidgetState createState() => _SymptomCheckerWidgetState();
}

class _SymptomCheckerWidgetState extends State<SymptomCheckerWidget> {
  final TextEditingController _symptomsController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  @override
  void dispose() {
    _symptomsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom,
        left: 16,
        right: 16,
        top: 16,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Quick Symptom Checker',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          const Text(
            'Describe your symptoms in detail to get a preliminary analysis. Please include when they started, their severity, and any other relevant information.',
          ),
          const SizedBox(height: 16),
          Form(
            key: _formKey,
            child: TextFormField(
              controller: _symptomsController,
              decoration: const InputDecoration(
                labelText: 'Describe your symptoms',
                hintText:
                    'E.g., I\'ve had a headache for 2 days with slight fever',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please describe your symptoms';
                }
                if (value.length < 10) {
                  return 'Please provide more details about your symptoms';
                }
                return null;
              },
            ),
          ),
          const SizedBox(height: 16),
          Text(
            '⚠️ Note: This is not a substitute for professional medical advice. For serious or persistent symptoms, please consult with a healthcare provider.',
            style: TextStyle(
              color: Colors.red.shade800,
              fontSize: 12,
              fontStyle: FontStyle.italic,
            ),
          ),
          const SizedBox(height: 24),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: const Text('Cancel'),
              ),
              const SizedBox(width: 16),
              ElevatedButton(
                onPressed: () {
                  if (_formKey.currentState!.validate()) {
                    widget.onSubmit(_symptomsController.text);
                  }
                },
                child: const Text('Check Symptoms'),
              ),
            ],
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }
}

class MedicationLookupWidget extends StatefulWidget {
  final Function(String) onSubmit;

  const MedicationLookupWidget({
    Key? key,
    required this.onSubmit,
  }) : super(key: key);

  @override
  _MedicationLookupWidgetState createState() => _MedicationLookupWidgetState();
}

class _MedicationLookupWidgetState extends State<MedicationLookupWidget> {
  final TextEditingController _medicationController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  @override
  void dispose() {
    _medicationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom,
        left: 16,
        right: 16,
        top: 16,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Medication Information',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          const Text(
            'Get information about a medication including its uses, side effects, and warnings.',
          ),
          const SizedBox(height: 16),
          Form(
            key: _formKey,
            child: TextFormField(
              controller: _medicationController,
              decoration: const InputDecoration(
                labelText: 'Medication Name',
                hintText: 'E.g., Ibuprofen, Lisinopril, Metformin',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter a medication name';
                }
                if (value.length < 2) {
                  return 'Medication name is too short';
                }
                return null;
              },
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'The information provided is for educational purposes only and should not replace professional advice from a healthcare provider.',
            style: TextStyle(
              color: Colors.grey.shade700,
              fontSize: 12,
              fontStyle: FontStyle.italic,
            ),
          ),
          const SizedBox(height: 24),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: const Text('Cancel'),
              ),
              const SizedBox(width: 16),
              ElevatedButton(
                onPressed: () {
                  if (_formKey.currentState!.validate()) {
                    widget.onSubmit(_medicationController.text);
                  }
                },
                child: const Text('Get Information'),
              ),
            ],
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }
}

extension StringExtension on String {
  String capitalize() {
    return '${this[0].toUpperCase()}${substring(1)}';
  }
}
