<?php

namespace Tests\Unit\Models;

use App\Models\EmailTemplate;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class EmailTemplateTest extends TestCase
{
    use RefreshDatabase;

    #[Test]
    public function it_can_create_an_email_template()
    {
        $template = EmailTemplate::create([
            'name' => 'Test Template',
            'slug' => 'test-template',
            'subject' => 'Test Subject',
            'content' => 'Test content with {{userName}}',
            'description' => 'Test description',
            'is_active' => true,
        ]);

        $this->assertInstanceOf(EmailTemplate::class, $template);
        $this->assertEquals('Test Template', $template->name);
        $this->assertEquals('test-template', $template->slug);
        $this->assertEquals('Test Subject', $template->subject);
        $this->assertEquals('Test content with {{userName}}', $template->content);
        $this->assertEquals('Test description', $template->description);
        $this->assertTrue($template->is_active);
    }

    #[Test]
    public function it_has_fillable_attributes()
    {
        $template = new EmailTemplate();
        $fillable = $template->getFillable();

        $expectedFillable = [
            'name',
            'slug',
            'subject',
            'content',
            'description',
            'is_active',
        ];

        $this->assertEquals($expectedFillable, $fillable);
    }

    #[Test]
    public function it_casts_is_active_to_boolean()
    {
        $template = EmailTemplate::create([
            'name' => 'Test Template',
            'slug' => 'test-template',
            'subject' => 'Test Subject',
            'content' => 'Test content',
            'is_active' => '1',
        ]);

        $this->assertIsBool($template->is_active);
        $this->assertTrue($template->is_active);

        $template->update(['is_active' => '0']);
        $template->refresh();

        $this->assertIsBool($template->is_active);
        $this->assertFalse($template->is_active);
    }

    #[Test]
    public function it_can_find_template_by_slug()
    {
        $template = EmailTemplate::create([
            'name' => 'Test Template',
            'slug' => 'test-template',
            'subject' => 'Test Subject',
            'content' => 'Test content',
        ]);

        $found = EmailTemplate::where('slug', 'test-template')->first();
        $this->assertNotNull($found);
        $this->assertEquals($template->id, $found->id);
    }

    #[Test]
    public function it_requires_name_field()
    {
        $this->expectException(\Illuminate\Database\QueryException::class);

        EmailTemplate::create([
            'slug' => 'test-template',
            'subject' => 'Test Subject',
            'content' => 'Test content',
        ]);
    }

    #[Test]
    public function it_requires_slug_field()
    {
        $this->expectException(\Illuminate\Database\QueryException::class);

        EmailTemplate::create([
            'name' => 'Test Template',
            'subject' => 'Test Subject',
            'content' => 'Test content',
        ]);
    }

    #[Test]
    public function it_requires_subject_field()
    {
        $this->expectException(\Illuminate\Database\QueryException::class);

        EmailTemplate::create([
            'name' => 'Test Template',
            'slug' => 'test-template',
            'content' => 'Test content',
        ]);
    }

    #[Test]
    public function it_requires_content_field()
    {
        $this->expectException(\Illuminate\Database\QueryException::class);

        EmailTemplate::create([
            'name' => 'Test Template',
            'slug' => 'test-template',
            'subject' => 'Test Subject',
        ]);
    }

    #[Test]
    public function it_enforces_unique_name()
    {
        EmailTemplate::create([
            'name' => 'Test Template',
            'slug' => 'test-template-1',
            'subject' => 'Test Subject',
            'content' => 'Test content',
        ]);

        $this->expectException(\Illuminate\Database\QueryException::class);

        EmailTemplate::create([
            'name' => 'Test Template',
            'slug' => 'test-template-2',
            'subject' => 'Test Subject',
            'content' => 'Test content',
        ]);
    }

    #[Test]
    public function it_enforces_unique_slug()
    {
        EmailTemplate::create([
            'name' => 'Test Template 1',
            'slug' => 'test-template',
            'subject' => 'Test Subject',
            'content' => 'Test content',
        ]);

        $this->expectException(\Illuminate\Database\QueryException::class);

        EmailTemplate::create([
            'name' => 'Test Template 2',
            'slug' => 'test-template',
            'subject' => 'Test Subject',
            'content' => 'Test content',
        ]);
    }

    #[Test]
    public function it_defaults_is_active_to_true()
    {
        $template = EmailTemplate::create([
            'name' => 'Test Template',
            'slug' => 'test-template',
            'subject' => 'Test Subject',
            'content' => 'Test content',
        ]);

        $this->assertTrue($template->is_active);
    }

    #[Test]
    public function it_allows_nullable_description()
    {
        $template = EmailTemplate::create([
            'name' => 'Test Template',
            'slug' => 'test-template',
            'subject' => 'Test Subject',
            'content' => 'Test content',
            'description' => null,
        ]);

        $this->assertNull($template->description);
    }

    #[Test]
    public function it_can_update_template()
    {
        $template = EmailTemplate::create([
            'name' => 'Test Template',
            'slug' => 'test-template',
            'subject' => 'Test Subject',
            'content' => 'Test content',
        ]);

        $template->update([
            'name' => 'Updated Template',
            'subject' => 'Updated Subject',
            'content' => 'Updated content with {{newVariable}}',
            'description' => 'Updated description',
            'is_active' => false,
        ]);

        $this->assertEquals('Updated Template', $template->name);
        $this->assertEquals('Updated Subject', $template->subject);
        $this->assertEquals('Updated content with {{newVariable}}', $template->content);
        $this->assertEquals('Updated description', $template->description);
        $this->assertFalse($template->is_active);
    }

    #[Test]
    public function it_can_delete_template()
    {
        $template = EmailTemplate::create([
            'name' => 'Test Template',
            'slug' => 'test-template',
            'subject' => 'Test Subject',
            'content' => 'Test content',
        ]);

        $templateId = $template->id;
        $template->delete();

        $this->assertNull(EmailTemplate::find($templateId));
    }

    #[Test]
    public function it_has_timestamps()
    {
        $template = EmailTemplate::create([
            'name' => 'Test Template',
            'slug' => 'test-template',
            'subject' => 'Test Subject',
            'content' => 'Test content',
        ]);

        $this->assertNotNull($template->created_at);
        $this->assertNotNull($template->updated_at);
    }
}
