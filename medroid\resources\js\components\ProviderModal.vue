<template>
    <!-- <PERSON><PERSON> Backdrop with proper blur -->
    <div class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
        <!-- Background overlay with blur effect -->
        <div class="fixed inset-0 bg-gray-900 bg-opacity-50 backdrop-blur-sm transition-opacity" @click="$emit('close')"></div>

        <!-- Modal Container -->
        <div class="flex min-h-full items-center justify-center p-4">
            <div class="relative w-full max-w-4xl transform overflow-hidden rounded-lg bg-white shadow-xl transition-all" @click.stop>
                <!-- Header -->
                <div class="bg-white px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold text-gray-900">
                            {{ isEdit ? 'Edit Provider' : 'Create New Provider' }}
                        </h3>
                        <button @click="$emit('close')" class="text-gray-400 hover:text-gray-600 transition-colors">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- Form -->
                <form @submit.prevent="saveProvider" class="px-6 py-4">
                    <div class="space-y-6">
                        <!-- User Information Section -->
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h4 class="text-md font-semibold text-gray-900 mb-4">User Information</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Full Name *</label>
                                    <input
                                        v-model="form.name"
                                        type="text"
                                        required
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="Enter full name"
                                    >
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Email *</label>
                                    <input
                                        v-model="form.email"
                                        type="email"
                                        required
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="Enter email address"
                                    >
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Phone Number</label>
                                    <input
                                        v-model="form.phone_number"
                                        type="tel"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="Enter phone number"
                                    >
                                </div>
                                <div v-if="!isEdit">
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Password *</label>
                                    <input
                                        v-model="form.password"
                                        type="password"
                                        :required="!isEdit"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="Enter password"
                                    >
                                </div>
                            </div>
                        </div>

                        <!-- Provider Information Section -->
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h4 class="text-md font-semibold text-gray-900 mb-4">Provider Information</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Specialization *</label>
                                    <input
                                        v-model="form.specialization"
                                        type="text"
                                        required
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="e.g., Cardiology, Dermatology"
                                    >
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">License Number *</label>
                                    <input
                                        v-model="form.license_number"
                                        type="text"
                                        required
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="Medical license number"
                                    >
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Clinic</label>
                                    <select
                                        v-model="form.clinic_id"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                    >
                                        <option value="">Select Clinic (Default will be assigned if none selected)</option>
                                        <option v-for="clinic in clinics" :key="clinic.id" :value="clinic.id">
                                            {{ clinic.name }}
                                        </option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Additional Information Section -->
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h4 class="text-md font-semibold text-gray-900 mb-4">Additional Information</h4>
                            <div class="space-y-4">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Gender</label>
                                        <select
                                            v-model="form.gender"
                                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                        >
                                            <option value="">Select Gender</option>
                                            <option value="male">Male</option>
                                            <option value="female">Female</option>
                                            <option value="other">Other</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Education</label>
                                        <input
                                            v-model="form.education"
                                            type="text"
                                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                            placeholder="Educational background"
                                        >
                                    </div>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Bio</label>
                                    <textarea
                                        v-model="form.bio"
                                        rows="3"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="Brief professional biography"
                                    ></textarea>
                                </div>
                                <div class="flex items-center">
                                    <input
                                        v-model="form.accepts_insurance"
                                        type="checkbox"
                                        id="accepts_insurance"
                                        class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                    >
                                    <label for="accepts_insurance" class="ml-2 block text-sm text-gray-900">Accepts Insurance</label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="flex justify-end space-x-3 pt-6 border-t">
                        <button
                            type="button"
                            @click="$emit('close')"
                            class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                        >
                            Cancel
                        </button>
                        <button
                            type="submit"
                            :disabled="saving"
                            class="px-4 py-2 text-sm font-medium text-white bg-medroid-orange border border-transparent rounded-md hover:bg-medroid-orange-dark disabled:opacity-50"
                        >
                            {{ saving ? 'Saving...' : (isEdit ? 'Update Provider' : 'Create Provider') }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue';
import axios from 'axios';

const props = defineProps({
    provider: Object,
    isEdit: Boolean
});

const emit = defineEmits(['close', 'saved']);

// Reactive data
const saving = ref(false);
const clinics = ref([]);

const form = ref({
    // User fields
    name: '',
    email: '',
    phone_number: '',
    password: '',
    // Provider fields
    clinic_id: '',
    specialization: '',
    license_number: '',
    gender: '',
    bio: '',
    education: '',
    accepts_insurance: false
});

// Methods
const saveProvider = async () => {
    saving.value = true;
    try {
        if (props.isEdit) {
            // Update existing provider
            await axios.put(`/update-provider/${props.provider.id}`, form.value);
        } else {
            // Create new provider (user + provider)
            await axios.post('/save-provider-with-user', form.value);
        }
        emit('saved');
    } catch (error) {
        console.error('Error saving provider:', error);
        alert('Error saving provider. Please try again.');
    } finally {
        saving.value = false;
    }
};

const fetchClinics = async () => {
    try {
        const response = await axios.get('/clinics-list');
        clinics.value = response.data.data || response.data.clinics || [];
    } catch (error) {
        console.error('Error fetching clinics:', error);
    }
};

// Watchers
watch(() => props.provider, (newProvider) => {
    if (newProvider && props.isEdit) {
        Object.assign(form.value, {
            name: newProvider.user?.name || '',
            email: newProvider.user?.email || '',
            phone_number: newProvider.user?.phone_number || '',
            clinic_id: newProvider.clinic_id || '',
            specialization: newProvider.specialization || '',
            license_number: newProvider.license_number || '',
            gender: newProvider.gender || '',
            bio: newProvider.bio || '',
            education: newProvider.education || '',
            accepts_insurance: newProvider.accepts_insurance || false
        });
    }
}, { immediate: true });

// Lifecycle
onMounted(() => {
    fetchClinics();
});
</script>
