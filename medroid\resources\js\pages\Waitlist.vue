<script setup lang="ts">
import { Head } from '@inertiajs/vue3';
import { ref, onMounted } from 'vue';
import axios from 'axios';
import AppLayout from '@/layouts/AppLayout.vue';

// Reactive data
const stats = ref({});
const analytics = ref({});
const isLoading = ref(true);
const isToggling = ref(false);
const dateRange = ref(30);
const waitlistRequests = ref([]);
const recentInvitations = ref([]);
const showAllRequests = ref(false);
const showInviteModal = ref(false);
const showBulkInviteModal = ref(false);
const selectedRequest = ref(null);
const inviteForm = ref({
    club_type: 'regular'
});
const bulkInviteForm = ref({
    emails: '',
    club_type: 'regular'
});
const isSubmittingInvite = ref(false);
const isSubmittingBulkInvite = ref(false);
const inviteSuccess = ref(false);
const inviteError = ref('');
const requestsPerPage = ref(10);
const currentPage = ref(1);

// Fetch waitlist statistics
const fetchStats = async () => {
    try {
        isLoading.value = true;
        const response = await axios.get('/waitlist-stats', {
            params: { date_range: dateRange.value }
        });
        stats.value = response.data;
    } catch (error) {
        console.error('Error fetching waitlist stats:', error);
    } finally {
        isLoading.value = false;
    }
};

// Fetch detailed analytics
const fetchAnalytics = async () => {
    try {
        const response = await axios.get('/waitlist-analytics');
        analytics.value = response.data;
    } catch (error) {
        console.error('Error fetching analytics:', error);
    }
};

// Toggle waitlist mode
const toggleWaitlistMode = async () => {
    try {
        isToggling.value = true;
        const response = await axios.post('/waitlist-toggle', {
            enabled: !stats.value.stats?.waitlist_enabled
        });
        
        if (response.data.success) {
            // Refresh stats
            await fetchStats();
            alert(response.data.message);
        }
    } catch (error) {
        console.error('Error toggling waitlist mode:', error);
        alert('Failed to toggle waitlist mode');
    } finally {
        isToggling.value = false;
    }
};

// Format date
const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString();
};

// Get signup type badge class
const getSignupTypeBadge = (type) => {
    switch (type) {
        case 'founder':
            return 'bg-purple-100 text-purple-800';
        case 'user_referral':
            return 'bg-blue-100 text-blue-800';
        default:
            return 'bg-gray-100 text-gray-800';
    }
};

// Fetch waitlist requests
const fetchWaitlistRequests = async (append = false) => {
    try {
        const response = await axios.get('/waitlist-requests', {
            params: {
                per_page: requestsPerPage.value,
                page: append ? currentPage.value : 1
            }
        });

        if (append) {
            waitlistRequests.value = [...waitlistRequests.value, ...(response.data.data || response.data)];
        } else {
            waitlistRequests.value = response.data.data || response.data;
            currentPage.value = 1;
        }
    } catch (error) {
        console.error('Error fetching waitlist requests:', error);
    }
};

// Fetch invitation stats
const fetchInvitationStats = async () => {
    try {
        const response = await axios.get('/waitlist-invitation-stats');
        recentInvitations.value = response.data.recent_invitations || [];
    } catch (error) {
        console.error('Error fetching invitation stats:', error);
    }
};

// Show invite modal
const showInviteModalFor = (request) => {
    selectedRequest.value = request;
    showInviteModal.value = true;
    inviteSuccess.value = false;
    inviteError.value = '';
};

// Close invite modal
const closeInviteModal = () => {
    showInviteModal.value = false;
    selectedRequest.value = null;
    inviteSuccess.value = false;
    inviteError.value = '';
};

// Send invitation
const sendInvitation = async () => {
    if (!selectedRequest.value) return;

    try {
        isSubmittingInvite.value = true;
        inviteError.value = '';

        const response = await axios.post(`/waitlist-requests/${selectedRequest.value.id}/invite`, inviteForm.value);

        if (response.data.success) {
            inviteSuccess.value = true;
            setTimeout(() => {
                closeInviteModal();
            }, 2000);
            await fetchWaitlistRequests();
            await fetchInvitationStats();
        } else {
            inviteError.value = response.data.message || 'Failed to send invitation';
        }
    } catch (error) {
        console.error('Error sending invitation:', error);
        inviteError.value = error.response?.data?.message || 'Failed to send invitation';
    } finally {
        isSubmittingInvite.value = false;
    }
};

// Show bulk invite modal
const showBulkInviteModalFor = () => {
    showBulkInviteModal.value = true;
    bulkInviteForm.value.emails = '';
    bulkInviteForm.value.club_type = 'regular';
    inviteSuccess.value = false;
    inviteError.value = '';
};

// Close bulk invite modal
const closeBulkInviteModal = () => {
    showBulkInviteModal.value = false;
    bulkInviteForm.value.emails = '';
    inviteSuccess.value = false;
    inviteError.value = '';
};

// Send bulk invitations
const sendBulkInvitations = async () => {
    if (!bulkInviteForm.value.emails.trim()) {
        inviteError.value = 'Please enter at least one email address';
        return;
    }

    try {
        isSubmittingBulkInvite.value = true;
        inviteError.value = '';

        // Parse emails from comma-separated string
        const emails = bulkInviteForm.value.emails
            .split(',')
            .map(email => email.trim())
            .filter(email => email.length > 0);

        if (emails.length === 0) {
            inviteError.value = 'Please enter valid email addresses';
            return;
        }

        const response = await axios.post('/send-bulk-invitations', {
            emails: emails,
            club_type: bulkInviteForm.value.club_type
        });

        if (response.data.success) {
            inviteSuccess.value = true;
            setTimeout(() => {
                closeBulkInviteModal();
            }, 3000);
            await fetchWaitlistRequests();
            await fetchInvitationStats();
        } else {
            inviteError.value = response.data.message || 'Failed to send bulk invitations';
        }
    } catch (error) {
        console.error('Error sending bulk invitations:', error);
        inviteError.value = error.response?.data?.message || 'Failed to send bulk invitations';
    } finally {
        isSubmittingBulkInvite.value = false;
    }
};

// Load more requests
const loadMoreRequests = () => {
    currentPage.value++;
    fetchWaitlistRequests(true);
};

// Get displayed requests with pagination
const getDisplayedRequests = () => {
    if (showAllRequests.value) {
        return waitlistRequests.value;
    }
    return waitlistRequests.value.slice(0, 5);
};

onMounted(() => {
    fetchStats();
    fetchAnalytics();
    fetchWaitlistRequests();
    fetchInvitationStats();
});
</script>

<template>
    <Head title="Waitlist Management" />

    <AppLayout>
        <div class="py-6">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <!-- Header -->
                <div class="mb-8">
                    <div class="flex justify-between items-center">
                        <div>
                            <h1 class="text-3xl font-bold text-gray-900">Waitlist Management</h1>
                            <p class="mt-2 text-gray-600">Monitor and control invitation-only signup system</p>
                        </div>
                        
                        <!-- Waitlist Toggle -->
                        <div class="flex items-center space-x-4">
                            <span class="text-sm font-medium text-gray-700">Waitlist Mode:</span>
                            <button
                                @click="toggleWaitlistMode"
                                :disabled="isToggling"
                                :class="[
                                    'relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:ring-offset-2',
                                    stats.stats?.waitlist_enabled ? 'bg-medroid-orange' : 'bg-gray-200'
                                ]"
                            >
                                <span
                                    :class="[
                                        'pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out',
                                        stats.stats?.waitlist_enabled ? 'translate-x-5' : 'translate-x-0'
                                    ]"
                                />
                            </button>
                            <span :class="[
                                'text-sm font-medium',
                                stats.stats?.waitlist_enabled ? 'text-medroid-orange' : 'text-gray-500'
                            ]">
                                {{ stats.stats?.waitlist_enabled ? 'Enabled' : 'Disabled' }}
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Stats Cards -->
                <div v-if="!isLoading" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <div class="bg-white rounded-lg shadow p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">Total Users</dt>
                                    <dd class="text-lg font-medium text-gray-900">{{ stats.stats?.total_users || 0 }}</dd>
                                </dl>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">Recent Signups</dt>
                                    <dd class="text-lg font-medium text-gray-900">{{ stats.stats?.total_signups_period || 0 }}</dd>
                                </dl>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">Founder Signups</dt>
                                    <dd class="text-lg font-medium text-gray-900">{{ stats.stats?.founder_signups || 0 }}</dd>
                                </dl>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-orange-500 rounded-md flex items-center justify-center">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">Referral Signups</dt>
                                    <dd class="text-lg font-medium text-gray-900">{{ stats.stats?.referral_signups || 0 }}</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Waitlist Requests Management -->
                <div v-if="!isLoading" class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                    <!-- Waitlist Requests -->
                    <div class="bg-white shadow rounded-lg">
                        <div class="px-4 py-5 sm:p-6">
                            <div class="flex justify-between items-center mb-4">
                                <h3 class="text-lg leading-6 font-medium text-gray-900">Waitlist Requests</h3>
                                <div class="flex space-x-2">
                                    <button
                                        @click="showBulkInviteModalFor"
                                        class="text-xs bg-purple-600 text-white px-3 py-1 rounded hover:bg-purple-700 transition-colors"
                                    >
                                        Bulk Invite
                                    </button>
                                    <button
                                        @click="fetchWaitlistRequests"
                                        class="text-sm text-medroid-orange hover:text-medroid-orange/80"
                                    >
                                        Refresh
                                    </button>
                                </div>
                            </div>

                            <div v-if="waitlistRequests.length > 0" class="space-y-3">
                                <div
                                    v-for="request in getDisplayedRequests()"
                                    :key="request.id"
                                    class="flex items-center justify-between p-3 border border-gray-200 rounded-lg"
                                >
                                    <div class="flex-1">
                                        <div class="text-sm font-medium text-gray-900">{{ request.name || 'Anonymous' }}</div>
                                        <div class="text-sm text-gray-500">{{ request.email }}</div>
                                        <div class="text-xs text-gray-400">{{ formatDate(request.created_at) }}</div>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <span :class="[
                                            'inline-flex px-2 py-1 text-xs font-semibold rounded-full',
                                            request.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                                            request.status === 'invited' ? 'bg-blue-100 text-blue-800' :
                                            'bg-green-100 text-green-800'
                                        ]">
                                            {{ request.status }}
                                        </span>
                                        <button
                                            v-if="request.status === 'pending'"
                                            @click="showInviteModalFor(request)"
                                            class="text-xs bg-medroid-orange text-white px-2 py-1 rounded hover:bg-medroid-orange/90"
                                        >
                                            Invite
                                        </button>
                                    </div>
                                </div>

                                <div class="text-center pt-2 space-y-2">
                                    <div v-if="!showAllRequests">
                                        <button
                                            @click="showAllRequests = true"
                                            class="text-sm text-medroid-orange hover:text-medroid-orange/80"
                                        >
                                            View All Requests ({{ waitlistRequests.length }})
                                        </button>
                                    </div>
                                    <div v-else>
                                        <button
                                            @click="loadMoreRequests"
                                            class="text-sm bg-gray-100 text-gray-700 px-3 py-1 rounded hover:bg-gray-200 mr-2"
                                        >
                                            Load More
                                        </button>
                                        <button
                                            @click="showAllRequests = false"
                                            class="text-sm text-medroid-orange hover:text-medroid-orange/80"
                                        >
                                            Show Less
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div v-else class="text-center py-6 text-gray-500">
                                No waitlist requests yet
                            </div>
                        </div>
                    </div>

                    <!-- Recent Invitations -->
                    <div class="bg-white shadow rounded-lg">
                        <div class="px-4 py-5 sm:p-6">
                            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Recent Invitations</h3>

                            <div v-if="recentInvitations.length > 0" class="space-y-3">
                                <div
                                    v-for="invitation in recentInvitations.slice(0, 5)"
                                    :key="invitation.id"
                                    class="flex items-center justify-between p-3 border border-gray-200 rounded-lg"
                                >
                                    <div class="flex-1">
                                        <div class="text-sm font-medium text-gray-900">{{ invitation.email }}</div>
                                        <div class="text-sm text-gray-500">{{ invitation.club_type }}</div>
                                        <div class="text-xs text-gray-400">{{ formatDate(invitation.created_at) }}</div>
                                    </div>
                                    <span :class="[
                                        'inline-flex px-2 py-1 text-xs font-semibold rounded-full',
                                        invitation.status === 'sent' ? 'bg-blue-100 text-blue-800' :
                                        invitation.status === 'used' ? 'bg-green-100 text-green-800' :
                                        invitation.status === 'expired' ? 'bg-red-100 text-red-800' :
                                        'bg-gray-100 text-gray-800'
                                    ]">
                                        {{ invitation.status }}
                                    </span>
                                </div>
                            </div>

                            <div v-else class="text-center py-6 text-gray-500">
                                No invitations sent yet
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Signups -->
                <div v-if="!isLoading && stats.recent_signups" class="bg-white shadow rounded-lg mb-8">
                    <div class="px-4 py-5 sm:p-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Recent Signups</h3>
                        <div class="overflow-hidden">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Referrer/Code</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr v-for="signup in stats.recent_signups" :key="signup.id">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div>
                                                <div class="text-sm font-medium text-gray-900">{{ signup.name }}</div>
                                                <div class="text-sm text-gray-500">{{ signup.email }}</div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span :class="[
                                                'inline-flex px-2 py-1 text-xs font-semibold rounded-full',
                                                getSignupTypeBadge(signup.referral_type)
                                            ]">
                                                {{ signup.referral_type === 'founder' ? 'Founder' :
                                                   signup.referral_type === 'user_referral' ? 'Referral' : 'Direct' }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {{ signup.referrer_name || signup.founder_code || '-' }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {{ formatDate(signup.created_at) }}
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Loading State -->
                <div v-if="isLoading" class="flex justify-center items-center py-12">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-medroid-orange"></div>
                </div>
            </div>
        </div>

        <!-- Single Invitation Modal -->
        <div v-if="showInviteModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div class="bg-white rounded-lg p-6 w-full max-w-md mx-4">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-900">Send Invitation</h3>
                    <button @click="closeInviteModal" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <div v-if="selectedRequest" class="mb-4">
                    <p class="text-sm text-gray-600">Sending invitation to:</p>
                    <p class="font-medium">{{ selectedRequest.name || 'Anonymous' }}</p>
                    <p class="text-sm text-gray-500">{{ selectedRequest.email }}</p>
                </div>

                <div class="mb-4">
                    <label for="club_type" class="block text-sm font-medium text-gray-700 mb-2">
                        Club Type
                    </label>
                    <select
                        id="club_type"
                        v-model="inviteForm.club_type"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-medroid-orange"
                    >
                        <option value="regular">Regular Club</option>
                        <option value="premium">Premium Club</option>
                        <option value="founder">Founder Club</option>
                    </select>
                </div>

                <div v-if="inviteSuccess" class="mb-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded">
                    Invitation sent successfully!
                </div>

                <div v-if="inviteError" class="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
                    {{ inviteError }}
                </div>

                <div class="flex justify-end space-x-3">
                    <button
                        @click="closeInviteModal"
                        class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200"
                    >
                        Cancel
                    </button>
                    <button
                        @click="sendInvitation"
                        :disabled="isSubmittingInvite"
                        class="px-4 py-2 text-sm font-medium text-white bg-medroid-orange border border-transparent rounded-md hover:bg-medroid-orange/90 disabled:opacity-50"
                    >
                        <span v-if="isSubmittingInvite">Sending...</span>
                        <span v-else>Send Invitation</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- Bulk Invitation Modal -->
        <div v-if="showBulkInviteModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div class="bg-white rounded-lg p-6 w-full max-w-2xl mx-4">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-900">Send Bulk Invitations</h3>
                    <button @click="closeBulkInviteModal" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <div class="mb-4">
                    <label for="bulk_emails" class="block text-sm font-medium text-gray-700 mb-2">
                        Email Addresses (comma-separated)
                    </label>
                    <textarea
                        id="bulk_emails"
                        v-model="bulkInviteForm.emails"
                        rows="6"
                        placeholder="<EMAIL>, <EMAIL>, <EMAIL>"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-medroid-orange"
                    ></textarea>
                    <p class="text-xs text-gray-500 mt-1">Enter multiple email addresses separated by commas</p>
                </div>

                <div class="mb-4">
                    <label for="bulk_club_type" class="block text-sm font-medium text-gray-700 mb-2">
                        Club Type
                    </label>
                    <select
                        id="bulk_club_type"
                        v-model="bulkInviteForm.club_type"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-medroid-orange"
                    >
                        <option value="regular">Regular Club</option>
                        <option value="premium">Premium Club</option>
                        <option value="founder">Founder Club</option>
                    </select>
                </div>

                <div v-if="inviteSuccess" class="mb-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded">
                    Bulk invitations sent successfully!
                </div>

                <div v-if="inviteError" class="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
                    {{ inviteError }}
                </div>

                <div class="flex justify-end space-x-3">
                    <button
                        @click="closeBulkInviteModal"
                        class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200"
                    >
                        Cancel
                    </button>
                    <button
                        @click="sendBulkInvitations"
                        :disabled="isSubmittingBulkInvite"
                        class="px-4 py-2 text-sm font-medium text-white bg-purple-600 border border-transparent rounded-md hover:bg-purple-700 disabled:opacity-50"
                    >
                        <span v-if="isSubmittingBulkInvite">Sending...</span>
                        <span v-else>Send Bulk Invitations</span>
                    </button>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
