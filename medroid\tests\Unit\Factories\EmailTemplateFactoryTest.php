<?php

namespace Tests\Unit\Factories;

use App\Models\EmailTemplate;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class EmailTemplateFactoryTest extends TestCase
{
    use RefreshDatabase;

    #[Test]
    public function it_can_create_email_template_using_factory()
    {
        // Check if factory exists
        if (!class_exists(\Database\Factories\EmailTemplateFactory::class)) {
            $this->markTestSkipped('EmailTemplateFactory does not exist');
        }

        $template = EmailTemplate::factory()->create();

        $this->assertInstanceOf(EmailTemplate::class, $template);
        $this->assertNotEmpty($template->name);
        $this->assertNotEmpty($template->slug);
        $this->assertNotEmpty($template->subject);
        $this->assertNotEmpty($template->content);
        $this->assertTrue($template->is_active);
    }

    #[Test]
    public function it_can_create_multiple_templates_with_unique_data()
    {
        if (!class_exists(\Database\Factories\EmailTemplateFactory::class)) {
            $this->markTestSkipped('EmailTemplateFactory does not exist');
        }

        $templates = EmailTemplate::factory()->count(3)->create();

        $this->assertCount(3, $templates);

        $slugs = $templates->pluck('slug')->toArray();
        $this->assertEquals(3, count(array_unique($slugs)), 'All slugs should be unique');

        $names = $templates->pluck('name')->toArray();
        $this->assertEquals(3, count(array_unique($names)), 'All names should be unique');
    }

    #[Test]
    public function it_can_create_template_with_custom_attributes()
    {
        if (!class_exists(\Database\Factories\EmailTemplateFactory::class)) {
            $this->markTestSkipped('EmailTemplateFactory does not exist');
        }

        $template = EmailTemplate::factory()->create([
            'name' => 'Custom Test Template',
            'slug' => 'custom-test-template',
            'subject' => 'Custom Subject',
            'content' => 'Custom content with {{ $userName }}',
            'is_active' => false,
        ]);

        $this->assertEquals('Custom Test Template', $template->name);
        $this->assertEquals('custom-test-template', $template->slug);
        $this->assertEquals('Custom Subject', $template->subject);
        $this->assertEquals('Custom content with {{ $userName }}', $template->content);
        $this->assertFalse($template->is_active);
    }

    #[Test]
    public function it_can_create_inactive_template_state()
    {
        if (!class_exists(\Database\Factories\EmailTemplateFactory::class)) {
            $this->markTestSkipped('EmailTemplateFactory does not exist');
        }

        // Check if inactive state exists
        $reflection = new \ReflectionClass(\Database\Factories\EmailTemplateFactory::class);
        if (!$reflection->hasMethod('inactive')) {
            $this->markTestSkipped('EmailTemplateFactory does not have inactive state');
        }

        $template = EmailTemplate::factory()->inactive()->create();

        $this->assertFalse($template->is_active);
    }

    #[Test]
    public function it_can_create_template_with_html_content()
    {
        if (!class_exists(\Database\Factories\EmailTemplateFactory::class)) {
            $this->markTestSkipped('EmailTemplateFactory does not exist');
        }

        $htmlContent = '<div style="color: blue;"><h1>Welcome {{ $userName }}</h1><p>This is HTML content.</p></div>';

        $template = EmailTemplate::factory()->create([
            'content' => $htmlContent,
        ]);

        $this->assertEquals($htmlContent, $template->content);
        $this->assertStringContainsString('<div style="color: blue;">', $template->content);
        $this->assertStringContainsString('{{ $userName }}', $template->content);
    }

    #[Test]
    public function it_can_create_template_with_long_content()
    {
        if (!class_exists(\Database\Factories\EmailTemplateFactory::class)) {
            $this->markTestSkipped('EmailTemplateFactory does not exist');
        }

        $longContent = str_repeat('This is a long email template content. ', 100);

        $template = EmailTemplate::factory()->create([
            'content' => $longContent,
        ]);

        $this->assertEquals($longContent, $template->content);
        $this->assertGreaterThan(1000, strlen($template->content));
    }

    #[Test]
    public function it_can_create_template_with_special_characters()
    {
        if (!class_exists(\Database\Factories\EmailTemplateFactory::class)) {
            $this->markTestSkipped('EmailTemplateFactory does not exist');
        }

        $specialContent = 'Welcome {{ $userName }}! 🎉 Your appointment is confirmed. Special chars: @#$%^&*()';

        $template = EmailTemplate::factory()->create([
            'content' => $specialContent,
        ]);

        $this->assertEquals($specialContent, $template->content);
        $this->assertStringContainsString('🎉', $template->content);
        $this->assertStringContainsString('@#$%^&*()', $template->content);
    }

    #[Test]
    public function it_respects_database_constraints_when_using_factory()
    {
        if (!class_exists(\Database\Factories\EmailTemplateFactory::class)) {
            $this->markTestSkipped('EmailTemplateFactory does not exist');
        }

        // Create first template
        $template1 = EmailTemplate::factory()->create(['slug' => 'unique-slug']);

        // Try to create second template with same slug - should fail
        $this->expectException(\Illuminate\Database\QueryException::class);
        EmailTemplate::factory()->create(['slug' => 'unique-slug']);
    }

    #[Test]
    public function it_can_make_template_without_persisting()
    {
        if (!class_exists(\Database\Factories\EmailTemplateFactory::class)) {
            $this->markTestSkipped('EmailTemplateFactory does not exist');
        }

        $template = EmailTemplate::factory()->make();

        $this->assertInstanceOf(EmailTemplate::class, $template);
        $this->assertNull($template->id);
        $this->assertFalse($template->exists);

        // Verify it's not in database
        $this->assertEquals(0, EmailTemplate::count());
    }

    #[Test]
    public function it_can_create_template_for_specific_types()
    {
        if (!class_exists(\Database\Factories\EmailTemplateFactory::class)) {
            $this->markTestSkipped('EmailTemplateFactory does not exist');
        }

        $appointmentTemplate = EmailTemplate::factory()->create([
            'slug' => 'appointment-booked-patient',
            'name' => 'Appointment Booked - Patient',
            'subject' => 'Your appointment is confirmed',
            'content' => 'Dear {{ $patientName }}, your appointment with {{ $providerName }} is confirmed.',
        ]);

        $this->assertEquals('appointment-booked-patient', $appointmentTemplate->slug);
        $this->assertStringContainsString('{{ $patientName }}', $appointmentTemplate->content);
        $this->assertStringContainsString('{{ $providerName }}', $appointmentTemplate->content);
    }
}
