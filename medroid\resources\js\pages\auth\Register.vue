<script setup lang="ts">
import InputError from '@/components/InputError.vue';
import { Head, Link, useForm } from '@inertiajs/vue3';
import { ref, onMounted } from 'vue';
import axios from 'axios';

// Props from the controller
const props = defineProps({
    canResetPassword: {
        type: Boolean,
        default: false,
    },
    status: {
        type: String,
        default: null,
    },
    isLoginMode: {
        type: Boolean,
        default: false,
    },
    waitlistStatus: {
        type: Object,
        default: () => ({ enabled: false, messages: {} }),
    },
    invitationToken: {
        type: String,
        default: null,
    },
    invitationValid: {
        type: Boolean,
        default: false,
    },
    invitation: {
        type: Object,
        default: null,
    },
});

const form = useForm({
    name: '',
    email: props.invitation?.email || '',
    password: '',
    password_confirmation: '',
    role: 'patient', // Always patient for signup
    gender: '',
    date_of_birth: '',
    referral_code: '',
    invitation_token: props.invitationToken || '',
});

// Reactive waitlist status (will be updated from API if props are not available)
const waitlistStatus = ref(props.waitlistStatus || { enabled: false, messages: {} });

// Fetch waitlist status from API if not provided by props
const fetchWaitlistStatus = async () => {
    try {
        console.log('Fetching waitlist status from API...');
        const response = await axios.get('/api/waitlist/status');
        waitlistStatus.value = response.data;
        console.log('Waitlist status fetched:', response.data);
        console.log('Waitlist enabled:', response.data.enabled);
    } catch (error) {
        console.error('Error fetching waitlist status:', error);
        // Keep default values
    }
};

const showPassword = ref(false);
const showPasswordConfirmation = ref(false);
const isLoading = ref(false);
const isLoginMode = ref(props.isLoginMode); // Toggle between login and register

// Chat demo animation
const visibleMessages = ref([]);
const allMessages = [
    { type: 'user', text: 'I have been feeling tired lately and having headaches. What could be the cause?' },
    { type: 'bot', text: 'I understand your concern. Fatigue and headaches can have various causes. Can you tell me more about when these symptoms started and if you\'ve noticed any patterns?' },
    { type: 'user', text: 'It started about a week ago, mostly in the afternoons.' },
    { type: 'bot', text: 'Based on your symptoms, this could be related to dehydration, stress, or sleep patterns. I recommend drinking more water, ensuring adequate sleep, and monitoring your symptoms. If they persist, please consult with a healthcare provider.' },
    { type: 'user', text: 'Thank you! Should I be concerned about anything specific?' },
    { type: 'bot', text: 'Monitor for severe headaches, fever, or vision changes. These would require immediate medical attention. For now, focus on hydration and rest. Feel better soon!' }
];
// Animation functions
const startChatAnimation = () => {
    visibleMessages.value = [];
    let currentIndex = 0;

    const showNextMessage = () => {
        if (currentIndex < allMessages.length) {
            visibleMessages.value.push(allMessages[currentIndex]);
            currentIndex++;

            // Delay between messages (2 seconds for bot, 1.5 seconds for user)
            const delay = allMessages[currentIndex - 1]?.type === 'bot' ? 2000 : 1500;
            setTimeout(showNextMessage, delay);
        } else {
            // Restart animation after all messages are shown
            setTimeout(() => {
                startChatAnimation();
            }, 3000);
        }
    };

    // Start with first message after a short delay
    setTimeout(showNextMessage, 1000);
};

const submit = () => {
    isLoading.value = true;
    const route_name = isLoginMode.value ? 'login' : 'register';
    form.post(route(route_name), {
        onFinish: () => {
            if (!isLoginMode.value) {
                form.reset('password', 'password_confirmation');
            } else {
                form.reset('password');
            }
            isLoading.value = false;
        },
    });
};

const togglePasswordVisibility = () => {
    showPassword.value = !showPassword.value;
};

const togglePasswordConfirmationVisibility = () => {
    showPasswordConfirmation.value = !showPasswordConfirmation.value;
};

const toggleMode = () => {
    isLoginMode.value = !isLoginMode.value;
    form.reset();
};

// Start chat animation when component mounts
onMounted(() => {
    startChatAnimation();

    console.log('Component mounted. Props waitlistStatus:', props.waitlistStatus);

    // Always fetch waitlist status to ensure we have the latest
    fetchWaitlistStatus();
});
</script>

<template>
    <Head :title="isLoginMode ? 'Sign In' : 'Register'" />

    <div class="min-h-screen bg-medroid-sage flex">
        <!-- Left Panel - Auth Form -->
        <div class="w-full lg:w-1/2 flex items-center justify-center p-8 bg-medroid-cream">
            <div class="max-w-md w-full">
                <!-- Header -->
                <div class="text-left mb-8">
                    <h1 class="text-4xl font-bold text-medroid-navy mb-2">
                        Your AI Doctor,
                    </h1>
                    <h2 class="text-4xl font-bold text-medroid-navy mb-4">
                        Always On Call
                    </h2>
                    <p class="text-medroid-slate mb-8">
                        AI-powered healthcare that puts your well-being first.
                    </p>
                </div>

                <!-- Google Sign In Button -->
                <a
                    :href="route('auth.google')"
                    :disabled="!isLoginMode && waitlistStatus.enabled && !invitationValid"
                    :class="[
                        'w-full flex items-center justify-center px-4 py-3 border border-medroid-border rounded-lg shadow-sm text-sm font-medium transition-colors duration-200 mb-6 no-underline',
                        (!isLoginMode && waitlistStatus.enabled && !invitationValid)
                            ? 'text-gray-400 bg-gray-100 cursor-not-allowed'
                            : 'text-medroid-navy bg-white hover:bg-medroid-sage',
                        'no-underline'
                    ]"
                >
                    <svg class="w-5 h-5 mr-3" viewBox="0 0 24 24">
                        <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                        <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                        <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                        <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                    </svg>
                    {{ isLoginMode ? 'Continue with Google' :
                       (waitlistStatus.enabled && !invitationValid ? 'Google Sign-up Disabled' : 'Continue with Google') }}
                </a>

                <!-- Waitlist Notice for Google Sign-up -->
                <div v-if="!isLoginMode && waitlistStatus.enabled && !invitationValid" class="mb-4 p-3 bg-orange-50 border border-orange-200 rounded-lg">
                    <p class="text-sm text-orange-700">
                        {{ waitlistStatus.messages.google_signin_disabled }}
                    </p>
                </div>

                <!-- Divider -->
                <div class="relative mb-6">
                    <div class="absolute inset-0 flex items-center">
                        <div class="w-full border-t border-medroid-border"></div>
                    </div>
                    <div class="relative flex justify-center text-sm">
                        <span class="px-2 bg-medroid-cream text-medroid-slate">OR</span>
                    </div>
                </div>

                <!-- Status Message -->
                <div v-if="status" class="mb-4 text-center text-sm font-medium text-medroid-teal">
                    {{ status }}
                </div>

                <!-- Invitation Notice -->
                <div v-if="!isLoginMode && invitationValid && invitation" class="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                    <div class="flex items-start space-x-3">
                        <div class="flex-shrink-0">
                            <svg class="w-5 h-5 text-green-500 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-sm font-medium text-green-800 mb-1">🎉 Welcome to {{ invitation.club_info.club_name }}!</h3>
                            <p class="text-sm text-green-700">
                                You've been invited to join Medroid. Complete your registration below.
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Waitlist Notice (when no valid invitation) -->
                <div v-else-if="!isLoginMode && waitlistStatus.enabled && !invitationValid" class="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <div class="flex items-start space-x-3">
                        <div class="flex-shrink-0">
                            <svg class="w-5 h-5 text-blue-500 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-sm font-medium text-blue-800 mb-1">Invitation Required</h3>
                            <p class="text-sm text-blue-700">
                                {{ waitlistStatus.messages.signup_disabled }}
                            </p>
                            <p class="text-sm text-blue-600 mt-2">
                                <a href="/" class="underline hover:text-blue-800">Join our waitlist</a> to get an invitation!
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Auth Form -->
                <form class="space-y-5" @submit.prevent="submit">
                    <!-- Name Field (Register mode only) -->
                    <div v-if="!isLoginMode">
                        <input
                            id="name"
                            v-model="form.name"
                            name="name"
                            type="text"
                            autocomplete="name"
                            required
                            class="w-full px-4 py-3 border border-medroid-border rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-medroid-orange transition-all duration-200 text-medroid-navy placeholder-medroid-slate bg-white"
                            placeholder="Enter your full name"
                        />
                        <InputError class="mt-2" :message="form.errors.name" />
                    </div>

                    <!-- Gender Selection (Register mode only) -->
                    <div v-if="!isLoginMode">
                        <label class="block text-sm font-medium text-medroid-navy mb-3">Gender (Optional)</label>
                        <div class="flex space-x-6">
                            <label class="flex items-center">
                                <input
                                    type="radio"
                                    v-model="form.gender"
                                    value="male"
                                    name="gender"
                                    class="w-4 h-4 text-medroid-orange border-medroid-border focus:ring-medroid-orange focus:ring-2"
                                />
                                <span class="ml-2 text-sm text-medroid-navy">Male</span>
                            </label>
                            <label class="flex items-center">
                                <input
                                    type="radio"
                                    v-model="form.gender"
                                    value="female"
                                    name="gender"
                                    class="w-4 h-4 text-medroid-orange border-medroid-border focus:ring-medroid-orange focus:ring-2"
                                />
                                <span class="ml-2 text-sm text-medroid-navy">Female</span>
                            </label>
                            <label class="flex items-center">
                                <input
                                    type="radio"
                                    v-model="form.gender"
                                    value="other"
                                    name="gender"
                                    class="w-4 h-4 text-medroid-orange border-medroid-border focus:ring-medroid-orange focus:ring-2"
                                />
                                <span class="ml-2 text-sm text-medroid-navy">Other</span>
                            </label>
                        </div>
                        <InputError class="mt-2" :message="form.errors.gender" />
                    </div>

                    <!-- Date of Birth (Register mode only) -->
                    <div v-if="!isLoginMode">
                        <label for="date_of_birth" class="block text-sm font-medium text-medroid-navy mb-2">Date of Birth (Optional)</label>
                        <input
                            id="date_of_birth"
                            v-model="form.date_of_birth"
                            name="date_of_birth"
                            type="date"
                            class="w-full px-4 py-3 border border-medroid-border rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-medroid-orange transition-all duration-200 text-medroid-navy bg-white"
                        />
                        <InputError class="mt-2" :message="form.errors.date_of_birth" />
                        <p class="mt-1 text-xs text-medroid-slate">Helps us provide better healthcare recommendations</p>
                    </div>

                    <!-- Email Field -->
                    <div>
                        <input
                            id="email"
                            v-model="form.email"
                            name="email"
                            type="email"
                            autocomplete="email"
                            required
                            :readonly="invitationValid && invitation"
                            :class="[
                                'w-full px-4 py-3 border border-medroid-border rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-medroid-orange transition-all duration-200 text-medroid-navy placeholder-medroid-slate',
                                (invitationValid && invitation) ? 'bg-gray-100' : 'bg-white'
                            ]"
                            :placeholder="isLoginMode ? 'Enter your personal or work email' : 'Enter your email address'"
                        />
                        <InputError class="mt-2" :message="form.errors.email" />
                        <p v-if="invitationValid && invitation" class="mt-1 text-xs text-gray-500">
                            Email is pre-filled from your invitation
                        </p>
                    </div>

                    <!-- Password Field -->
                    <div>
                        <div class="relative">
                            <input
                                id="password"
                                v-model="form.password"
                                name="password"
                                :type="showPassword ? 'text' : 'password'"
                                :autocomplete="isLoginMode ? 'current-password' : 'new-password'"
                                required
                                class="w-full px-4 py-3 border border-medroid-border rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-medroid-orange transition-all duration-200 text-medroid-navy placeholder-medroid-slate bg-white"
                                :placeholder="isLoginMode ? 'Enter your password' : 'Create a strong password'"
                            />
                            <button
                                type="button"
                                @click="togglePasswordVisibility"
                                class="absolute inset-y-0 right-0 pr-4 flex items-center hover:text-medroid-orange transition-colors duration-200"
                            >
                                <svg v-if="showPassword" class="w-5 h-5 text-medroid-slate" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                </svg>
                                <svg v-else class="w-5 h-5 text-medroid-slate" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"></path>
                                </svg>
                            </button>
                        </div>
                        <InputError class="mt-2" :message="form.errors.password" />
                    </div>

                    <!-- Referral/Invitation Code (Register mode only, when no valid invitation) -->
                    <div v-if="!isLoginMode && !invitationValid">
                        <label for="referral_code" class="block text-sm font-medium text-medroid-navy mb-2">
                            {{ waitlistStatus.enabled ? 'Invitation Code (Required)' : 'Referral Code (Optional)' }}
                        </label>
                        <input
                            id="referral_code"
                            v-model="form.referral_code"
                            name="referral_code"
                            type="text"
                            :required="waitlistStatus.enabled"
                            class="w-full px-4 py-3 border border-medroid-border rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-medroid-orange transition-all duration-200 text-medroid-navy placeholder-medroid-slate bg-white"
                            :placeholder="waitlistStatus.enabled ? 'Enter your invitation code' : 'Enter referral code'"
                        />
                        <InputError class="mt-2" :message="form.errors.referral_code" />
                        <p class="mt-1 text-xs text-medroid-slate">
                            {{ waitlistStatus.enabled
                                ? 'An invitation code is required to create an account during our beta phase.'
                                : 'Have a referral code? Enter it here to get bonus points!'
                            }}
                        </p>
                    </div>

                    <!-- Password Confirmation Field (Register mode only) -->
                    <div v-if="!isLoginMode">
                        <div class="relative">
                            <input
                                id="password_confirmation"
                                v-model="form.password_confirmation"
                                name="password_confirmation"
                                :type="showPasswordConfirmation ? 'text' : 'password'"
                                autocomplete="new-password"
                                required
                                class="w-full px-4 py-3 border border-medroid-border rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-medroid-orange transition-all duration-200 text-medroid-navy placeholder-medroid-slate bg-white"
                                placeholder="Confirm your password"
                            />
                            <button
                                type="button"
                                @click="togglePasswordConfirmationVisibility"
                                class="absolute inset-y-0 right-0 pr-4 flex items-center hover:text-medroid-orange transition-colors duration-200"
                            >
                                <svg v-if="showPasswordConfirmation" class="w-5 h-5 text-medroid-slate" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                </svg>
                                <svg v-else class="w-5 h-5 text-medroid-slate" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"></path>
                                </svg>
                            </button>
                        </div>
                        <InputError class="mt-2" :message="form.errors.password_confirmation" />
                    </div>

                    <!-- Submit Button -->
                    <div class="pt-4">
                        <button
                            type="submit"
                            :disabled="form.processing || isLoading"
                            class="w-full bg-medroid-orange hover:bg-medroid-orange/90 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg"
                        >
                            {{ form.processing || isLoading ? (isLoginMode ? 'Signing In...' : 'Creating Account...') : (isLoginMode ? 'Sign In' : 'Sign Up') }}
                        </button>
                    </div>

                    <!-- Toggle Mode Link -->
                    <div class="text-center pt-4">
                        <p class="text-sm text-medroid-slate">
                            {{ isLoginMode ? "Don't have an account?" : "Already have an account?" }}
                            <button
                                type="button"
                                @click="toggleMode"
                                class="font-medium text-medroid-orange hover:text-medroid-orange/80 transition-colors duration-200 underline"
                            >
                                {{ isLoginMode ? 'Sign up' : 'Sign in here' }}
                            </button>
                        </p>
                    </div>

                    <!-- Forgot Password (Login mode only) -->
                    <div v-if="isLoginMode && canResetPassword" class="text-center">
                        <Link
                            :href="route('password.request')"
                            class="text-sm text-medroid-slate hover:text-medroid-orange transition-colors duration-200 underline"
                        >
                            Forgot password?
                        </Link>
                    </div>
                </form>

                <!-- Privacy Policy - Fixed Position -->
                <div class="text-center mt-6">
                    <p class="text-xs text-medroid-slate">
                        By continuing, you acknowledge Medroid's
                        <a href="https://medroid.ai/privacy-policy/" target="_blank" class="text-medroid-orange hover:text-medroid-orange/80 underline">Privacy Policy</a>
                    </p>
                </div>
            </div>
        </div>

        <!-- Right Panel - Chat Demo -->
        <div class="hidden lg:flex lg:w-1/2 bg-medroid-sage flex-col">
            <!-- Chat Header -->
            <div class="bg-white p-6 border-b border-medroid-border">
                <h3 class="text-xl font-semibold text-medroid-navy mb-2">Experience Medroid</h3>
                <p class="text-medroid-slate text-sm">Chat with our AI doctor for instant health insights</p>
            </div>

            <!-- Chat Messages -->
            <div class="flex-1 p-6 overflow-y-auto space-y-4 chat-container">
                <div
                    v-for="(message, index) in visibleMessages"
                    :key="index"
                    :class="message.type === 'user' ? 'flex justify-end' : 'flex justify-start'"
                    class="animate-fade-in-up"
                    :style="{ animationDelay: `${index * 0.1}s` }"
                >
                    <div
                        :class="[
                            'message-bubble max-w-xs lg:max-w-md px-4 py-3 rounded-2xl text-sm',
                            message.type === 'user'
                                ? 'bg-medroid-orange text-white rounded-br-md shadow-lg'
                                : 'bg-white text-medroid-navy border border-medroid-border rounded-bl-md shadow-sm hover:shadow-md'
                        ]"
                    >
                        {{ message.text }}
                    </div>
                </div>
            </div>

            <!-- Chat Input -->
            <div class="p-6 bg-white border-t border-medroid-border">
                <div class="flex items-center space-x-3">
                    <input
                        type="text"
                        placeholder="Type your health question here..."
                        class="flex-1 px-4 py-3 border border-medroid-border rounded-full focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-medroid-orange text-sm text-medroid-navy placeholder-medroid-slate"
                        disabled
                    />
                    <button
                        class="bg-medroid-orange hover:bg-medroid-orange/90 text-white p-3 rounded-full transition-colors duration-200 disabled:opacity-50"
                        disabled
                    >
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                        </svg>
                    </button>
                </div>

                <!-- Learn More Link -->
                <div class="mt-4 text-center">
                    <a href="#" class="text-sm text-medroid-slate hover:text-medroid-orange transition-colors duration-200">
                        Learn more about Medroid →
                    </a>
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped>
/* Chat message animations */
.animate-fade-in-up {
    animation: fadeInUp 0.8s ease-out forwards;
    opacity: 0;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Typing indicator animation */
.typing-indicator {
    animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 0.6;
    }
    50% {
        opacity: 1;
    }
}

/* Smooth scroll for chat messages */
.chat-container {
    scroll-behavior: smooth;
}

/* Enhanced message bubble animations */
.message-bubble {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.message-bubble:hover {
    transform: translateY(-2px);
}
</style>