<?php

namespace Tests\Unit\Services;

use App\Models\EmailTemplate;
use App\Services\EmailTemplateService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Log;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class EmailTemplateServiceTest extends TestCase
{
    use RefreshDatabase;

    private EmailTemplateService $service;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = new EmailTemplateService();
    }

    #[Test]
    public function it_can_render_template_with_data()
    {
        $template = EmailTemplate::create([
            'name' => 'Test Template',
            'slug' => 'test-template',
            'subject' => 'Hello {{ $userName }}',
            'content' => 'Welcome {{ $userName }} to {{ $appName }}!',
        ]);

        $data = [
            'userName' => 'John Doe',
            'appName' => 'Medroid',
        ];

        $result = $this->service->renderTemplate('test-template', $data);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('subject', $result);
        $this->assertArrayHasKey('content', $result);
        $this->assertEquals('Hello {{ $userName }}', $result['subject']);
        $this->assertStringContainsString('Welcome John Doe to Medroid!', $result['content']);
    }

    #[Test]
    public function it_returns_error_template_when_template_not_found()
    {
        $result = $this->service->renderTemplate('non-existent-template');

        $this->assertIsArray($result);
        $this->assertEquals('Template Not Found', $result['subject']);
        $this->assertStringContainsString('Template Not Found', $result['content']);
    }

    #[Test]
    public function it_can_find_template_by_id_when_slug_is_numeric()
    {
        $template = EmailTemplate::create([
            'name' => 'Test Template',
            'slug' => 'test-template',
            'subject' => 'Test Subject',
            'content' => 'Test content',
        ]);

        $result = $this->service->renderTemplate((string)$template->id);

        $this->assertIsArray($result);
        $this->assertEquals('Test Subject', $result['subject']);
    }

    #[Test]
    public function it_handles_template_compilation_errors_gracefully()
    {
        $template = EmailTemplate::create([
            'name' => 'Test Template',
            'slug' => 'test-template',
            'subject' => 'Test Subject',
            'content' => 'Invalid blade syntax @if($invalid',
        ]);

        Log::shouldReceive('error')->once();

        $result = $this->service->renderTemplate('test-template');

        $this->assertIsArray($result);
        $this->assertArrayHasKey('subject', $result);
        $this->assertArrayHasKey('content', $result);
    }

    #[Test]
    public function it_compiles_template_with_blade_syntax()
    {
        $template = EmailTemplate::create([
            'name' => 'Test Template',
            'slug' => 'test-template',
            'subject' => 'Test Subject',
            'content' => '@if($userName)Hello {{ $userName }}@endif',
        ]);

        $data = ['userName' => 'John'];

        $result = $this->service->renderTemplate('test-template', $data);

        $this->assertIsArray($result);
        $this->assertStringContainsString('Hello John', $result['content']);
    }

    #[Test]
    public function it_handles_empty_data_array()
    {
        $template = EmailTemplate::create([
            'name' => 'Test Template',
            'slug' => 'test-template',
            'subject' => 'Test Subject',
            'content' => 'Simple content without variables',
        ]);

        $result = $this->service->renderTemplate('test-template', []);

        $this->assertIsArray($result);
        $this->assertEquals('Test Subject', $result['subject']);
        $this->assertStringContainsString('Simple content without variables', $result['content']);
    }

    #[Test]
    public function it_handles_null_data()
    {
        $template = EmailTemplate::create([
            'name' => 'Test Template',
            'slug' => 'test-template',
            'subject' => 'Test Subject',
            'content' => 'Simple content',
        ]);

        $result = $this->service->renderTemplate('test-template');

        $this->assertIsArray($result);
        $this->assertEquals('Test Subject', $result['subject']);
    }

    #[Test]
    public function it_preserves_html_content()
    {
        $htmlContent = '<div style="color: red;"><h1>Welcome</h1><p>Hello {{ $userName }}</p></div>';

        $template = EmailTemplate::create([
            'name' => 'HTML Template',
            'slug' => 'html-template',
            'subject' => 'HTML Subject',
            'content' => $htmlContent,
        ]);

        $result = $this->service->renderTemplate('html-template', ['userName' => 'John']);

        $this->assertStringContainsString('<div style="color: red;">', $result['content']);
        $this->assertStringContainsString('<h1>Welcome</h1>', $result['content']);
        $this->assertStringContainsString('<p>Hello John</p>', $result['content']);
    }

    #[Test]
    public function it_handles_complex_data_structures()
    {
        $template = EmailTemplate::create([
            'name' => 'Complex Template',
            'slug' => 'complex-template',
            'subject' => 'Appointment with {{ $providerName }}',
            'content' => 'Your appointment on {{ $appointmentDate }} with {{ $providerName }} is confirmed.',
        ]);

        $data = [
            'providerName' => 'Dr. Smith',
            'appointmentDate' => '2024-01-15 10:00 AM',
            'patientName' => 'John Doe',
            'clinicName' => 'Medroid Clinic',
        ];

        $result = $this->service->renderTemplate('complex-template', $data);

        $this->assertIsArray($result);
        $this->assertEquals('Appointment with {{ $providerName }}', $result['subject']);
        $this->assertStringContainsString('Your appointment on 2024-01-15 10:00 AM with Dr. Smith is confirmed.', $result['content']);
    }

    #[Test]
    public function it_handles_special_characters_in_content()
    {
        $template = EmailTemplate::create([
            'name' => 'Special Chars Template',
            'slug' => 'special-chars',
            'subject' => 'Special Characters: @#$%^&*()',
            'content' => 'Content with special chars: @#$%^&*() and {{userName}}',
        ]);

        $result = $this->service->renderTemplate('special-chars', ['userName' => 'John']);

        $this->assertStringContainsString('@#$%^&*()', $result['subject']);
        $this->assertStringContainsString('@#$%^&*()', $result['content']);
    }

    #[Test]
    public function it_handles_unicode_characters()
    {
        $template = EmailTemplate::create([
            'name' => 'Unicode Template',
            'slug' => 'unicode-template',
            'subject' => 'Welcome 🎉 {{ $userName }}',
            'content' => 'Hello {{ $userName }} 👋 Welcome to Medroid! 🏥',
        ]);

        $result = $this->service->renderTemplate('unicode-template', ['userName' => 'John']);

        $this->assertStringContainsString('🎉', $result['subject']);
        $this->assertStringContainsString('👋', $result['content']);
        $this->assertStringContainsString('🏥', $result['content']);
        $this->assertStringContainsString('Hello John', $result['content']);
    }

    #[Test]
    public function it_logs_errors_when_template_rendering_fails()
    {
        Log::shouldReceive('error')
            ->once()
            ->with(
                \Mockery::pattern('/Error rendering email template/'),
                \Mockery::type('array')
            );

        $result = $this->service->renderTemplate('non-existent-template');

        $this->assertIsArray($result);
    }

    #[Test]
    public function it_returns_consistent_structure_on_success()
    {
        $template = EmailTemplate::create([
            'name' => 'Test Template',
            'slug' => 'test-template',
            'subject' => 'Test Subject',
            'content' => 'Test content',
        ]);

        $result = $this->service->renderTemplate('test-template');

        $this->assertIsArray($result);
        $this->assertCount(2, $result);
        $this->assertArrayHasKey('subject', $result);
        $this->assertArrayHasKey('content', $result);
        $this->assertIsString($result['subject']);
        $this->assertIsString($result['content']);
    }

    #[Test]
    public function it_returns_consistent_structure_on_error()
    {
        $result = $this->service->renderTemplate('non-existent-template');

        $this->assertIsArray($result);
        $this->assertCount(2, $result);
        $this->assertArrayHasKey('subject', $result);
        $this->assertArrayHasKey('content', $result);
        $this->assertIsString($result['subject']);
        $this->assertIsString($result['content']);
    }
}
