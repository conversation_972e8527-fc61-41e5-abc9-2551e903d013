<?php

namespace Tests\Feature;

use App\Models\EmailTemplate;
use App\Services\EmailTemplateService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class EmailTemplatePerformanceTest extends TestCase
{
    use RefreshDatabase;

    private EmailTemplateService $emailTemplateService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->emailTemplateService = app(EmailTemplateService::class);
    }

    #[Test]
    public function it_can_render_multiple_templates_efficiently()
    {
        // Create multiple templates
        $templates = EmailTemplate::factory()->count(10)->create();

        $startTime = microtime(true);

        foreach ($templates as $template) {
            $result = $this->emailTemplateService->renderTemplate($template->slug, [
                'userName' => 'John Doe',
                'appName' => 'Medroid',
            ]);

            $this->assertIsArray($result);
            $this->assertArrayHasKey('subject', $result);
            $this->assertArrayHasKey('content', $result);
        }

        $endTime = microtime(true);
        $executionTime = $endTime - $startTime;

        // Should render 10 templates in less than 2 seconds
        $this->assertLessThan(2.0, $executionTime, "Template rendering took too long: {$executionTime} seconds");
    }

    #[Test]
    public function it_can_handle_concurrent_template_rendering()
    {
        $template = EmailTemplate::factory()->create([
            'content' => 'Hello {{ $userName }}, welcome to {{ $appName }}! Your ID is {{ $userId }}.',
        ]);

        $users = [
            ['userName' => 'John Doe', 'userId' => 1, 'appName' => 'Medroid'],
            ['userName' => 'Jane Smith', 'userId' => 2, 'appName' => 'Medroid'],
            ['userName' => 'Bob Johnson', 'userId' => 3, 'appName' => 'Medroid'],
            ['userName' => 'Alice Brown', 'userId' => 4, 'appName' => 'Medroid'],
            ['userName' => 'Charlie Wilson', 'userId' => 5, 'appName' => 'Medroid'],
        ];

        $startTime = microtime(true);
        $results = [];

        foreach ($users as $userData) {
            $results[] = $this->emailTemplateService->renderTemplate($template->slug, $userData);
        }

        $endTime = microtime(true);
        $executionTime = $endTime - $startTime;

        // Verify all results
        $this->assertCount(5, $results);
        foreach ($results as $result) {
            $this->assertIsArray($result);
            $this->assertArrayHasKey('subject', $result);
            $this->assertArrayHasKey('content', $result);
        }

        // Should handle 5 concurrent renders in less than 1 second
        $this->assertLessThan(1.0, $executionTime, "Concurrent rendering took too long: {$executionTime} seconds");
    }

    #[Test]
    public function it_can_render_large_templates_efficiently()
    {
        $largeContent = 'Hello {{ $userName }}, ' . str_repeat('This is a large email template content. ', 1000) . ' Thank you!';

        $template = EmailTemplate::factory()->create([
            'content' => $largeContent,
        ]);

        $startTime = microtime(true);

        $result = $this->emailTemplateService->renderTemplate($template->slug, [
            'userName' => 'John Doe',
        ]);

        $endTime = microtime(true);
        $executionTime = $endTime - $startTime;

        $this->assertIsArray($result);
        $this->assertStringContainsString('John Doe', $result['content']);

        // Large template should render in less than 0.5 seconds
        $this->assertLessThan(0.5, $executionTime, "Large template rendering took too long: {$executionTime} seconds");
    }

    #[Test]
    public function it_can_handle_complex_data_structures_efficiently()
    {
        $template = EmailTemplate::factory()->create([
            'content' => '
                Dear {{ $patientName }},
                Your appointment with {{ $providerName }} is scheduled for {{ $appointmentDate }}.
                Clinic: {{ $clinicName }}
                Address: {{ $clinicAddress }}
                Services: {{ $services }}
                Total Cost: {{ $totalCost }}
                Insurance: {{ $insuranceInfo }}
                Notes: {{ $notes }}
            ',
        ]);

        $complexData = [
            'patientName' => 'John Doe',
            'providerName' => 'Dr. Jane Smith',
            'appointmentDate' => '2024-01-15 10:00 AM',
            'clinicName' => 'Medroid Health Center',
            'clinicAddress' => '123 Health St, Medical City, MC 12345',
            'services' => 'General Consultation, Blood Test, X-Ray',
            'totalCost' => '$250.00',
            'insuranceInfo' => 'Blue Cross Blue Shield - Policy #123456789',
            'notes' => 'Please arrive 15 minutes early. Bring your insurance card and ID.',
        ];

        $startTime = microtime(true);

        $result = $this->emailTemplateService->renderTemplate($template->slug, $complexData);

        $endTime = microtime(true);
        $executionTime = $endTime - $startTime;

        $this->assertIsArray($result);
        $this->assertStringContainsString('John Doe', $result['content']);
        $this->assertStringContainsString('Dr. Jane Smith', $result['content']);
        $this->assertStringContainsString('$250.00', $result['content']);

        // Complex data rendering should be fast
        $this->assertLessThan(0.3, $executionTime, "Complex data rendering took too long: {$executionTime} seconds");
    }

    #[Test]
    public function it_can_handle_database_queries_efficiently()
    {
        // Create 50 templates
        EmailTemplate::factory()->count(50)->create();

        $startTime = microtime(true);

        // Get all templates
        $templates = $this->emailTemplateService->getAllTemplates();

        $endTime = microtime(true);
        $executionTime = $endTime - $startTime;

        $this->assertCount(50, $templates);

        // Database query should be fast
        $this->assertLessThan(0.1, $executionTime, "Database query took too long: {$executionTime} seconds");
    }

    #[Test]
    public function it_can_handle_template_caching_efficiently()
    {
        $template = EmailTemplate::factory()->create();

        $data = [
            'userName' => 'John Doe',
            'appName' => 'Medroid',
        ];

        // First render (cold)
        $startTime = microtime(true);
        $result1 = $this->emailTemplateService->renderTemplate($template->slug, $data);
        $endTime = microtime(true);
        $firstRenderTime = $endTime - $startTime;

        // Second render (should be faster if caching is implemented)
        $startTime = microtime(true);
        $result2 = $this->emailTemplateService->renderTemplate($template->slug, $data);
        $endTime = microtime(true);
        $secondRenderTime = $endTime - $startTime;

        $this->assertEquals($result1, $result2);

        // Both renders should be reasonably fast
        $this->assertLessThan(0.2, $firstRenderTime, "First render took too long: {$firstRenderTime} seconds");
        $this->assertLessThan(0.2, $secondRenderTime, "Second render took too long: {$secondRenderTime} seconds");
    }

    #[Test]
    public function it_can_handle_memory_usage_efficiently()
    {
        $initialMemory = memory_get_usage();

        // Create and render multiple templates
        for ($i = 0; $i < 20; $i++) {
            $template = EmailTemplate::factory()->create();
            $result = $this->emailTemplateService->renderTemplate($template->slug, [
                'userName' => "User {$i}",
                'appName' => 'Medroid',
            ]);

            $this->assertIsArray($result);
        }

        $finalMemory = memory_get_usage();
        $memoryUsed = $finalMemory - $initialMemory;

        // Should not use more than 10MB for 20 template renders
        $this->assertLessThan(10 * 1024 * 1024, $memoryUsed, "Memory usage too high: " . number_format($memoryUsed / 1024 / 1024, 2) . " MB");
    }

    #[Test]
    public function it_can_handle_error_scenarios_efficiently()
    {
        $startTime = microtime(true);

        // Try to render non-existent templates
        for ($i = 0; $i < 10; $i++) {
            $result = $this->emailTemplateService->renderTemplate("non-existent-{$i}");
            $this->assertIsArray($result);
            $this->assertEquals('Template Not Found', $result['subject']);
        }

        $endTime = microtime(true);
        $executionTime = $endTime - $startTime;

        // Error handling should be fast
        $this->assertLessThan(0.5, $executionTime, "Error handling took too long: {$executionTime} seconds");
    }

    #[Test]
    public function it_can_handle_html_templates_efficiently()
    {
        $template = EmailTemplate::factory()->withHtml()->create();

        $startTime = microtime(true);

        $result = $this->emailTemplateService->renderTemplate($template->slug, [
            'userName' => 'John Doe',
            'appName' => 'Medroid',
        ]);

        $endTime = microtime(true);
        $executionTime = $endTime - $startTime;

        $this->assertIsArray($result);
        $this->assertStringContainsString('<div', $result['content']);
        $this->assertStringContainsString('John Doe', $result['content']);

        // HTML template rendering should be fast
        $this->assertLessThan(0.3, $executionTime, "HTML template rendering took too long: {$executionTime} seconds");
    }

    #[Test]
    public function it_can_measure_template_seeding_performance()
    {
        $startTime = microtime(true);

        // Create 100 templates using factory
        EmailTemplate::factory()->count(100)->create();

        $endTime = microtime(true);
        $executionTime = $endTime - $startTime;

        $this->assertEquals(100, EmailTemplate::count());

        // Creating 100 templates should be reasonably fast
        $this->assertLessThan(5.0, $executionTime, "Template creation took too long: {$executionTime} seconds");
    }
}
