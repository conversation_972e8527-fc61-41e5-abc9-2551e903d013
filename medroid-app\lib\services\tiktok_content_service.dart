import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:medroid_app/models/social_post.dart';
import 'package:medroid_app/utils/health_content_filter.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// TikTok content service for fetching health-related content
class TikTokContentService {
  static const String apiBaseUrl = 'https://open-api.tiktok.com/api/v2';

  final HealthContentFilter _healthFilter = HealthContentFilter();

  /// Check if user is authenticated with TikTok
  Future<bool> isAuthenticated() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('tiktok_access_token');
      return token != null && token.isNotEmpty;
    } catch (e) {
      if (kDebugMode) {
        debugPrint('TikTok auth check error: $e');
      }
      return false;
    }
  }

  /// Get stored access token
  Future<String?> getAccessToken() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString('tiktok_access_token');
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error getting TikTok access token: $e');
      }
      return null;
    }
  }

  /// Get stored open ID
  Future<String?> getOpenId() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString('tiktok_open_id');
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error getting TikTok open ID: $e');
      }
      return null;
    }
  }

  /// Fetch user's videos from TikTok
  Future<List<SocialPost>> getUserVideos() async {
    final accessToken = await getAccessToken();
    final openId = await getOpenId();

    if (accessToken == null || openId == null) {
      if (kDebugMode) {
        debugPrint('TikTok: No access token or open ID available');
      }
      return [];
    }

    try {
      // Get user's videos
      final videoResponse = await http.get(
        Uri.parse(
            '$apiBaseUrl/video/list/?fields=id,video_description,embed_link,thumbnail_url,create_time&access_token=$accessToken&open_id=$openId'),
      );

      if (videoResponse.statusCode != 200) {
        if (kDebugMode) {
          debugPrint('Error fetching TikTok videos: ${videoResponse.body}');
        }
        return [];
      }

      final videoData = json.decode(videoResponse.body);

      // Check if the response has the expected structure
      if (!videoData.containsKey('data') ||
          !videoData['data'].containsKey('videos')) {
        if (kDebugMode) {
          debugPrint('Unexpected TikTok API response format: $videoData');
        }
        return [];
      }

      final videoItems = videoData['data']['videos'] as List;

      // Process each video to create SocialPost objects
      List<SocialPost> posts = [];
      for (var video in videoItems) {
        final description = video['video_description'] ?? '';

        // Filter for health-related content
        if (_healthFilter.isHealthRelated(description)) {
          posts.add(SocialPost(
            id: 'tiktok_${video['id']}',
            source: 'tiktok',
            sourceId: video['id'],
            contentType: 'video',
            mediaUrl: video['embed_link'] ?? video['share_url'] ?? '',
            caption: description,
            healthTopics: _healthFilter.extractHealthTopics(description),
            relevanceScore: _healthFilter.calculateRelevanceScore(description),
            engagementMetrics: {'likes': 0, 'shares': 0, 'saves': 0},
            filteredStatus: 'approved',
            createdAt: DateTime.parse(video['create_time']),
          ));
        }
      }

      return posts;
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error fetching TikTok videos: $e');
      }
      return [];
    }
  }

  /// Store authentication data
  Future<void> storeAuthData(String accessToken, String openId,
      {String? refreshToken}) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('tiktok_access_token', accessToken);
      await prefs.setString('tiktok_open_id', openId);
      if (refreshToken != null) {
        await prefs.setString('tiktok_refresh_token', refreshToken);
      }
      if (kDebugMode) {
        debugPrint('TikTok authentication data stored');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error storing TikTok auth data: $e');
      }
    }
  }

  /// Clear authentication data
  Future<void> clearAuthData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('tiktok_access_token');
      await prefs.remove('tiktok_open_id');
      await prefs.remove('tiktok_refresh_token');
      if (kDebugMode) {
        debugPrint('TikTok authentication data cleared');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error clearing TikTok auth data: $e');
      }
    }
  }
}
