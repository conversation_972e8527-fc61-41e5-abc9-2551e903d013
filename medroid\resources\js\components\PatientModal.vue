<template>
    <!-- <PERSON><PERSON> Backdrop with proper blur -->
    <div class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
        <!-- Background overlay with blur effect -->
        <div class="fixed inset-0 bg-gray-900 bg-opacity-50 backdrop-blur-sm transition-opacity" @click="$emit('close')"></div>
        
        <!-- Modal Container -->
        <div class="flex min-h-full items-center justify-center p-4">
            <div class="relative w-full max-w-2xl transform overflow-hidden rounded-lg bg-white shadow-xl transition-all" @click.stop>
                <!-- Header -->
                <div class="bg-white px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold text-gray-900">
                            {{ isEdit ? 'Edit Patient' : 'Create New Patient' }}
                        </h3>
                        <button @click="$emit('close')" class="text-gray-400 hover:text-gray-600 transition-colors">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- Form -->
                <form @submit.prevent="savePatient" class="px-6 py-4">
                    <div class="space-y-6">
                        <!-- User Information Section -->
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h4 class="text-md font-semibold text-gray-900 mb-4">User Information</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Full Name *</label>
                                    <input
                                        v-model="form.name"
                                        type="text"
                                        required
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="Enter full name"
                                    >
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Email *</label>
                                    <input
                                        v-model="form.email"
                                        type="email"
                                        required
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="Enter email address"
                                    >
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Phone Number</label>
                                    <input
                                        v-model="form.phone_number"
                                        type="tel"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="Enter phone number"
                                    >
                                </div>
                                <div v-if="!isEdit">
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Password *</label>
                                    <input
                                        v-model="form.password"
                                        type="password"
                                        :required="!isEdit"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="Enter password"
                                    >
                                </div>
                            </div>
                        </div>

                        <!-- Patient Information Section -->
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h4 class="text-md font-semibold text-gray-900 mb-4">Patient Information</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Date of Birth</label>
                                    <input
                                        v-model="form.date_of_birth"
                                        type="date"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                    >
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Gender</label>
                                    <select
                                        v-model="form.gender"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                    >
                                        <option value="">Select Gender</option>
                                        <option value="male">Male</option>
                                        <option value="female">Female</option>
                                        <option value="other">Other</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Emergency Contact</label>
                                    <input
                                        v-model="form.emergency_contact"
                                        type="text"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="Emergency contact name and phone"
                                    >
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Clinic</label>
                                    <select
                                        v-model="form.clinic_id"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                    >
                                        <option value="">Select Clinic</option>
                                        <option v-for="clinic in clinics" :key="clinic.id" :value="clinic.id">
                                            {{ clinic.name }}
                                        </option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Medical Information Section -->
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h4 class="text-md font-semibold text-gray-900 mb-4">Medical Information</h4>
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Medical History</label>
                                    <textarea
                                        v-model="form.medical_history"
                                        rows="3"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="Enter medical history, conditions, allergies, etc."
                                    ></textarea>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Current Medications</label>
                                    <textarea
                                        v-model="form.current_medications"
                                        rows="2"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="List current medications"
                                    ></textarea>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="flex justify-end space-x-3 pt-6 mt-6 border-t border-gray-200">
                        <button
                            type="button"
                            @click="$emit('close')"
                            class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
                        >
                            Cancel
                        </button>
                        <button
                            type="submit"
                            :disabled="saving"
                            class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors"
                        >
                            {{ saving ? 'Saving...' : (isEdit ? 'Update Patient' : 'Create Patient') }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue';
import axios from 'axios';

const props = defineProps({
    patient: Object,
    isEdit: Boolean
});

const emit = defineEmits(['close', 'saved']);

// Reactive data
const saving = ref(false);
const clinics = ref([]);

const form = ref({
    // User fields
    name: '',
    email: '',
    phone_number: '',
    password: '',
    // Patient fields
    date_of_birth: '',
    gender: '',
    emergency_contact: '',
    clinic_id: '',
    medical_history: '',
    current_medications: ''
});

// Methods
const savePatient = async () => {
    saving.value = true;
    try {
        if (props.isEdit) {
            // Update existing patient
            await axios.put(`/update-patient/${props.patient.id}`, form.value);
        } else {
            // Create new patient (user + patient)
            await axios.post('/save-patient', form.value);
        }
        emit('saved');
    } catch (error) {
        console.error('Error saving patient:', error);
        alert('Error saving patient. Please try again.');
    } finally {
        saving.value = false;
    }
};

const fetchClinics = async () => {
    try {
        const response = await axios.get('/clinics-list');
        clinics.value = response.data.data || response.data.clinics || [];
    } catch (error) {
        console.error('Error fetching clinics:', error);
    }
};

// Watchers
watch(() => props.patient, (newPatient) => {
    if (newPatient && props.isEdit) {
        Object.assign(form.value, {
            name: newPatient.user?.name || '',
            email: newPatient.user?.email || '',
            phone_number: newPatient.user?.phone_number || '',
            date_of_birth: newPatient.date_of_birth || '',
            gender: newPatient.gender || '',
            emergency_contact: newPatient.emergency_contact || '',
            clinic_id: newPatient.clinic_id || '',
            medical_history: newPatient.medical_history || '',
            current_medications: newPatient.current_medications || ''
        });
    }
}, { immediate: true });

// Lifecycle
onMounted(() => {
    fetchClinics();
});
</script>
