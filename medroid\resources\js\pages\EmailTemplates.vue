<script setup>
import AppLayout from '@/layouts/AppLayout.vue';
import { Head } from '@inertiajs/vue3';
import { ref, onMounted } from 'vue';

const breadcrumbs = [
    { title: 'Dashboard', href: '/dashboard' },
    { title: 'Email Templates', href: '/email-templates' },
];

const loading = ref(false);
const templates = ref([]);
const showCreateModal = ref(false);
const editingTemplate = ref(null);

const newTemplate = ref({
    name: '',
    subject: '',
    body: '',
    type: 'notification',
    is_active: true
});

const templateTypes = [
    { value: 'notification', label: 'Notification' },
    { value: 'appointment', label: 'Appointment' },
    { value: 'reminder', label: 'Reminder' },
    { value: 'welcome', label: 'Welcome' },
    { value: 'referral', label: 'Referral' },
    { value: 'payment', label: 'Payment' }
];

const fetchTemplates = async () => {
    loading.value = true;
    try {
        const response = await window.axios.get('/email-templates-list');
        const rawTemplates = response.data || [];

        // Add type field based on template name/slug for display purposes
        templates.value = rawTemplates.map(template => ({
            ...template,
            type: getTemplateType(template.slug || template.name)
        }));
    } catch (error) {
        console.error('Error fetching email templates:', error);
        // Fallback to empty array on error
        templates.value = [];
    } finally {
        loading.value = false;
    }
};

const openCreateModal = () => {
    editingTemplate.value = null;
    newTemplate.value = {
        name: '',
        subject: '',
        body: '',
        type: 'notification',
        is_active: true
    };
    showCreateModal.value = true;
};

const openEditModal = (template) => {
    editingTemplate.value = template;
    newTemplate.value = {
        ...template,
        body: template.content || template.body || '' // Map content to body for editing
    };
    showCreateModal.value = true;
};

const saveTemplate = async () => {
    try {
        const templateData = {
            ...newTemplate.value,
            content: newTemplate.value.body || newTemplate.value.content // Map body back to content
        };
        delete templateData.body; // Remove body field

        if (editingTemplate.value) {
            // Update existing template
            await window.axios.put(`/email-templates/${editingTemplate.value.id}`, templateData);
            alert('Template updated successfully!');
        } else {
            // Create new template
            await window.axios.post('/email-templates', templateData);
            alert('Template created successfully!');
        }

        showCreateModal.value = false;
        await fetchTemplates();
    } catch (error) {
        console.error('Error saving template:', error);
        alert('Error saving template: ' + (error.response?.data?.message || error.message));
    }
};

const deleteTemplate = async (id) => {
    if (confirm('Are you sure you want to delete this template?')) {
        try {
            await window.axios.delete(`/email-templates/${id}`);
            alert('Template deleted successfully!');
            await fetchTemplates();
        } catch (error) {
            console.error('Error deleting template:', error);
            alert('Error deleting template: ' + (error.response?.data?.message || error.message));
        }
    }
};

const toggleTemplateStatus = async (template) => {
    try {
        await window.axios.put(`/email-templates/${template.id}`, {
            is_active: !template.is_active
        });
        template.is_active = !template.is_active;
        alert('Template status updated successfully!');
    } catch (error) {
        console.error('Error toggling template status:', error);
        alert('Error updating template status: ' + (error.response?.data?.message || error.message));
    }
};

onMounted(() => {
    fetchTemplates();
});

const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
};

const getTemplateType = (slug) => {
    if (slug.includes('appointment')) return 'appointment';
    if (slug.includes('reminder')) return 'reminder';
    if (slug.includes('password') || slug.includes('registration')) return 'welcome';
    if (slug.includes('referral')) return 'referral';
    if (slug.includes('payment')) return 'payment';
    return 'notification';
};

const getTypeClass = (type) => {
    const classes = {
        notification: 'bg-blue-100 text-blue-800',
        appointment: 'bg-green-100 text-green-800',
        reminder: 'bg-yellow-100 text-yellow-800',
        welcome: 'bg-purple-100 text-purple-800',
        referral: 'bg-pink-100 text-pink-800',
        payment: 'bg-indigo-100 text-indigo-800'
    };
    return classes[type] || 'bg-gray-100 text-gray-800';
};
</script>

<template>
    <Head title="Email Templates" />

    <AppLayout :breadcrumbs="breadcrumbs">
        <div class="p-6">
            <div class="mb-6 flex justify-between items-center">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Email Templates</h1>
                    <p class="text-gray-600">Manage email templates for notifications and communications</p>
                </div>
                <button
                    @click="openCreateModal"
                    class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center"
                >
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                    </svg>
                    Create Template
                </button>
            </div>

            <!-- Stats Cards -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-2 bg-blue-100 rounded-lg">
                            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Total Templates</p>
                            <p class="text-2xl font-bold text-gray-900">{{ templates.length }}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-2 bg-green-100 rounded-lg">
                            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Active Templates</p>
                            <p class="text-2xl font-bold text-gray-900">{{ templates.filter(t => t.is_active).length }}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-2 bg-yellow-100 rounded-lg">
                            <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Appointment Templates</p>
                            <p class="text-2xl font-bold text-gray-900">{{ templates.filter(t => t.type === 'appointment').length }}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-2 bg-purple-100 rounded-lg">
                            <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Welcome Templates</p>
                            <p class="text-2xl font-bold text-gray-900">{{ templates.filter(t => t.type === 'welcome').length }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Templates Table -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-medium text-gray-900">Email Templates</h2>
                </div>
                <div v-if="loading" class="p-6">
                    <div class="animate-pulse">
                        <div class="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
                        <div class="h-4 bg-gray-200 rounded w-1/2 mb-4"></div>
                        <div class="h-4 bg-gray-200 rounded w-5/6"></div>
                    </div>
                </div>

                <div v-else-if="templates.length === 0" class="p-6 text-center text-gray-500">
                    No email templates found. Create your first template to get started.
                </div>

                <div v-else class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subject</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Updated</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr v-for="template in templates" :key="template.id">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">{{ template.name }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500">{{ template.subject }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span :class="getTypeClass(template.type)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                                        {{ template.type }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <button
                                        @click="toggleTemplateStatus(template)"
                                        :class="template.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'"
                                        class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                                    >
                                        {{ template.is_active ? 'Active' : 'Inactive' }}
                                    </button>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ formatDate(template.updated_at) }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <button
                                        @click="openEditModal(template)"
                                        class="text-blue-600 hover:text-blue-900 mr-3"
                                    >
                                        Edit
                                    </button>
                                    <button
                                        @click="deleteTemplate(template.id)"
                                        class="text-red-600 hover:text-red-900"
                                    >
                                        Delete
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Create/Edit Modal -->
        <div v-if="showCreateModal" class="fixed inset-0 bg-white bg-opacity-20 backdrop-blur-sm overflow-y-auto h-full w-full z-50">
            <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
                <div class="mt-3">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">
                        {{ editingTemplate ? 'Edit Template' : 'Create New Template' }}
                    </h3>
                    
                    <form @submit.prevent="saveTemplate" class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Name</label>
                            <input
                                v-model="newTemplate.name"
                                type="text"
                                required
                                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                            >
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Subject</label>
                            <input
                                v-model="newTemplate.subject"
                                type="text"
                                required
                                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                            >
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Type</label>
                            <select
                                v-model="newTemplate.type"
                                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                            >
                                <option v-for="type in templateTypes" :key="type.value" :value="type.value">
                                    {{ type.label }}
                                </option>
                            </select>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Body</label>
                            <textarea
                                v-model="newTemplate.body"
                                rows="6"
                                required
                                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                placeholder="Email template body content..."
                            ></textarea>
                        </div>
                        
                        <div class="flex items-center">
                            <input
                                v-model="newTemplate.is_active"
                                type="checkbox"
                                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                            >
                            <label class="ml-2 block text-sm text-gray-900">Active</label>
                        </div>
                        
                        <div class="flex justify-end space-x-3 pt-4">
                            <button
                                type="button"
                                @click="showCreateModal = false"
                                class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                            >
                                Cancel
                            </button>
                            <button
                                type="submit"
                                class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                            >
                                {{ editingTemplate ? 'Update' : 'Create' }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
