<template>
    <AppLayout :breadcrumbs="breadcrumbs">
        <div class="unified-dashboard p-6">
            <!-- Header -->
            <div class="flex justify-between items-center mb-6">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Clinic Management</h1>
                    <p class="text-gray-600">Manage healthcare clinics and their settings</p>
                </div>
                <button
                    @click="showCreateModal = true"
                    class="bg-medroid-orange hover:bg-medroid-orange-dark text-white px-4 py-2 rounded-lg flex items-center"
                >
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    Add Clinic
                </button>
            </div>

            <!-- Filters -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                        <input
                            v-model="filters.search"
                            type="text"
                            placeholder="Search clinics..."
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-medroid-orange"
                        />
                    </div>
                    <div>
                        <select
                            v-model="filters.active"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-medroid-orange"
                        >
                            <option value="">All Status</option>
                            <option value="1">Active</option>
                            <option value="0">Inactive</option>
                        </select>
                    </div>
                    <div>
                        <input
                            v-model="filters.city"
                            type="text"
                            placeholder="Filter by city..."
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-medroid-orange"
                        />
                    </div>
                    <div>
                        <input
                            v-model="filters.state"
                            type="text"
                            placeholder="Filter by state..."
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-medroid-orange"
                        />
                    </div>
                </div>
            </div>

            <!-- Loading State -->
            <div v-if="loading" class="flex justify-center items-center py-12">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-medroid-orange"></div>
            </div>

            <!-- Clinics Grid -->
            <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div
                    v-for="clinic in clinics.data"
                    :key="clinic.id"
                    class="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow"
                >
                    <div class="p-6">
                        <!-- Clinic Header -->
                        <div class="flex items-start justify-between mb-4">
                            <div class="flex-1">
                                <h3 class="text-lg font-semibold text-gray-900 mb-1">{{ clinic.name }}</h3>
                                <p v-if="clinic.description" class="text-sm text-gray-600 mb-2">{{ clinic.description }}</p>
                                <div class="flex items-center">
                                    <span
                                        :class="clinic.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'"
                                        class="px-2 py-1 text-xs font-medium rounded-full"
                                    >
                                        {{ clinic.is_active ? 'Active' : 'Inactive' }}
                                    </span>
                                </div>
                            </div>
                            <div class="flex space-x-2">
                                <button
                                    @click="editClinic(clinic)"
                                    class="text-gray-400 hover:text-medroid-orange"
                                    title="Edit clinic"
                                >
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                    </svg>
                                </button>
                                <button
                                    @click="deleteClinic(clinic)"
                                    class="text-gray-400 hover:text-red-600"
                                    title="Delete clinic"
                                >
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                    </svg>
                                </button>
                            </div>
                        </div>

                        <!-- Contact Info -->
                        <div class="space-y-2 mb-4">
                            <div v-if="clinic.email" class="flex items-center text-sm text-gray-600">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                </svg>
                                {{ clinic.email }}
                            </div>
                            <div v-if="clinic.phone" class="flex items-center text-sm text-gray-600">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                                </svg>
                                {{ clinic.phone }}
                            </div>
                            <div v-if="clinic.full_address" class="flex items-center text-sm text-gray-600">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                </svg>
                                {{ clinic.full_address }}
                            </div>
                        </div>

                        <!-- Statistics -->
                        <div v-if="clinic.stats" class="grid grid-cols-2 gap-4 pt-4 border-t border-gray-200">
                            <div class="text-center">
                                <div class="text-lg font-semibold text-medroid-orange">{{ clinic.stats.total_providers }}</div>
                                <div class="text-xs text-gray-600">Providers</div>
                            </div>
                            <div class="text-center">
                                <div class="text-lg font-semibold text-medroid-orange">{{ clinic.stats.total_patients }}</div>
                                <div class="text-xs text-gray-600">Patients</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Pagination -->
            <div v-if="clinics.last_page > 1" class="mt-6 flex justify-center">
                <nav class="flex items-center space-x-2">
                    <button
                        v-for="page in paginationPages"
                        :key="page"
                        @click="changePage(page)"
                        :class="[
                            'px-3 py-2 text-sm rounded-md',
                            page === clinics.current_page
                                ? 'bg-medroid-orange text-white'
                                : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
                        ]"
                    >
                        {{ page }}
                    </button>
                </nav>
            </div>

            <!-- Create/Edit Modal -->
            <ClinicModal
                v-if="showCreateModal || showEditModal"
                :clinic="selectedClinic"
                :is-edit="showEditModal"
                @close="closeModal"
                @saved="handleClinicSaved"
            />
        </div>
    </AppLayout>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { debounce } from 'lodash';
import AppLayout from '@/layouts/AppLayout.vue';
import ClinicModal from '@/components/ClinicModal.vue';

// Page setup
const breadcrumbs = [
    { name: 'Dashboard', href: '/dashboard' },
    { name: 'Clinics', href: '/clinics', current: true }
];

// Reactive data
const loading = ref(false);
const clinics = ref({ data: [], current_page: 1, last_page: 1 });
const showCreateModal = ref(false);
const showEditModal = ref(false);
const selectedClinic = ref(null);

const filters = ref({
    search: '',
    active: '',
    city: '',
    state: ''
});

// Computed
const paginationPages = computed(() => {
    const pages = [];
    const current = clinics.value.current_page;
    const last = clinics.value.last_page;
    
    for (let i = Math.max(1, current - 2); i <= Math.min(last, current + 2); i++) {
        pages.push(i);
    }
    
    return pages;
});

// Methods
const fetchClinics = async (page = 1) => {
    loading.value = true;
    try {
        const params = new URLSearchParams({
            page: page.toString(),
            ...Object.fromEntries(Object.entries(filters.value).filter(([_, v]) => v !== ''))
        });

        const response = await axios.get(`/clinics-list?${params}`);
        clinics.value = response.data;
    } catch (error) {
        console.error('Error fetching clinics:', error);
    } finally {
        loading.value = false;
    }
};

const debouncedFetch = debounce(() => fetchClinics(1), 300);

const changePage = (page) => {
    fetchClinics(page);
};

const editClinic = (clinic) => {
    selectedClinic.value = clinic;
    showEditModal.value = true;
};

const deleteClinic = async (clinic) => {
    if (!confirm(`Are you sure you want to delete "${clinic.name}"?`)) {
        return;
    }

    try {
        await axios.delete(`/delete-clinic/${clinic.id}`);
        fetchClinics(clinics.value.current_page);
    } catch (error) {
        console.error('Error deleting clinic:', error);
        alert('Error deleting clinic. Please try again.');
    }
};

const closeModal = () => {
    showCreateModal.value = false;
    showEditModal.value = false;
    selectedClinic.value = null;
};

const handleClinicSaved = () => {
    closeModal();
    fetchClinics(clinics.value.current_page);
};

// Watchers
watch(filters, debouncedFetch, { deep: true });

// Lifecycle
onMounted(() => {
    fetchClinics();
});
</script>
