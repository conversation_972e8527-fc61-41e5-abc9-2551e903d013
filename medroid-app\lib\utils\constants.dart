import 'package:medroid_app/utils/env_config.dart';

class Constants {
  // API Endpoints
  static String get baseUrl {
    // Use the environment configuration
    return EnvConfig.apiBaseUrl;
  }

  // Base URL without /api for accessing storage
  static String get baseStorageUrl {
    // Use the environment configuration
    return EnvConfig.apiStorageUrl;
  }

  // Authentication endpoints
  static const String loginEndpoint = 'login';
  static const String registerEndpoint = 'register';
  static const String logoutEndpoint = 'logout';
  static const String userEndpoint = 'user';

  // SSO Authentication endpoints
  static const String ssoLoginEndpoint = 'sso/login';
  static const String ssoCompleteRegistrationEndpoint =
      'sso/complete-registration';

  // Password Reset endpoints
  static const String passwordResetEmailEndpoint = 'auth/password/email';
  static const String passwordResetEndpoint = 'auth/password/reset';
  static const String passwordChangeEndpoint = 'auth/password/change';

  // Notification endpoints
  static const String notificationsEndpoint = 'notifications';
  static const String notificationsUnreadCountEndpoint =
      'notifications/unread-count';
  static const String deviceTokenEndpoint = 'notifications/device-token';

  // Social Feed endpoints
  static const String feedEndpoint = 'feed';
  static const String feedTopicsEndpoint = 'feed/topics';
  static const String saveFeedItemEndpoint = 'feed/save/';
  static const String likeFeedItemEndpoint = 'feed/like/';
  static const String createPostEndpoint = 'feed/create';

  // AI Chat endpoints
  static const String chatStartEndpoint = 'chat/start';
  static const String chatMessageEndpoint = 'chat/message';
  static const String chatMessageWithImageEndpoint = 'chat/message-with-image';
  static const String chatHistoryEndpoint = 'chat/history';
  static const String chatConversationEndpoint = 'chat/conversation/';
  static const String chatRecommendationsEndpoint = 'chat/recommendations';
  static const String chatAnalyzeSymptomsEndpoint = 'chat/analyze-symptoms';
  static const String chatMedicationInfoEndpoint = 'chat/medication-info';
  static const String chatRequestAppointmentEndpoint =
      'chat/request-appointment';

  // Anonymous Chat endpoints
  static const String anonymousChatStartEndpoint = 'anonymous/chat/start';
  static const String anonymousChatMessageEndpoint = 'anonymous/chat/message';
  static const String anonymousChatConversationEndpoint =
      'anonymous/chat/conversation/';

  // Provider endpoints
  static const String providersEndpoint = 'providers';
  static const String providerSpecializationsEndpoint =
      'providers/specializations';
  static const String appointmentsEndpoint = 'appointments';
  static const String userAppointmentsEndpoint = 'appointments-list';

  // Video consultation endpoints
  static const String videoStatusEndpoint = 'video/status/';
  static const String videoInitializeEndpoint = 'video/initialize/';
  static const String videoEndEndpoint = 'video/end/';
  static const String videoJoinEndpoint = 'video/join/';
  static const String videoSessionEndpoint = 'video/session/';
  static const String videoTokenEndpoint = 'video-consultations/token/';

  // Video recording endpoints
  static const String videoRecordingStartEndpoint = 'video/recording/start/';
  static const String videoRecordingSaveEndpoint = 'video/recording/save/';
  static const String videoRecordingGetEndpoint = 'video/recording/';
  static const String videoTranscriptionEndpoint =
      'video/recording/transcription/';

  // Provider availability endpoints
  static const String providerProfileEndpoint = 'providers/profile';

  // Availability endpoints - separate GET and POST/PUT
  static const String providerAvailabilityGetEndpoint =
      'providers/availability-data';
  static const String providerAvailabilityPostEndpoint =
      'providers/availability';

  // Absences endpoints - separate GET and POST/PUT
  static const String providerAbsencesGetEndpoint = 'providers/absences-data';
  static const String providerAbsencesPostEndpoint = 'providers/absences';

  static const String providerAvailableSlotsEndpoint =
      'providers/available-slots';
  static const String providerNextAvailableEndpoint =
      'providers/next-available';

  // Service endpoints
  static const String servicesEndpoint = 'services';
  static const String serviceCategoriesEndpoint = 'service-categories';

  // Payment endpoints
  static const String paymentIntentEndpoint = 'payments/create-intent';
  static const String paymentConfirmEndpoint = 'payments/confirm';
  static const String paymentHistoryEndpoint = 'payments/history';
  static const String appointmentWithPaymentEndpoint =
      'appointments/with-payment';

  // Comments endpoints
  static const String commentsEndpoint = 'social-content';
  static const String commentRepliesEndpoint = 'replies';

  // Stories endpoints
  static const String storiesEndpoint = 'stories';
  static const String storyViewEndpoint = 'view';
  static const String storyViewersEndpoint = 'viewers';

  // Local Storage keys
  static const String tokenKey = 'auth_token';
  static const String userDataKey = 'user_data';
  static const String anonymousChatKey = 'anonymous_chat_data';

  // App settings
  static const int chatMaxHistoryLength = 20;
  static const int feedPageSize = 10;
  static const int connectionTimeoutSeconds = 30;
  static const int chatTimeoutSeconds = 90; // Longer timeout for chat requests

  // Asset paths
  static const String logoPath = 'assets/logo.png';
  static const String placeholderImagePath = 'assets/placeholder.png';
  static const String doctorImagePath = 'assets/doctor.png';

  // No fallback images - we only use actual uploaded content

  // Default error messages
  static const String networkErrorMessage =
      'Network error. Please check your connection and try again.';
  static const String serverErrorMessage =
      'Server error. Please try again later.';
  static const String unauthorizedErrorMessage =
      'Your session has expired. Please log in again.';
}
