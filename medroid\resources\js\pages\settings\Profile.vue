<script setup lang="ts">
import { Head, Link, useForm, usePage } from '@inertiajs/vue3';
import { ref, onMounted, computed } from 'vue';
import axios from 'axios';

import DeleteUser from '@/components/DeleteUser.vue';
import HeadingSmall from '@/components/HeadingSmall.vue';
import InputError from '@/components/InputError.vue';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import AppLayout from '@/layouts/AppLayout.vue';
import SettingsLayout from '@/layouts/settings/Layout.vue';
import { type BreadcrumbItem, type SharedData, type User } from '@/types';

interface Props {
    mustVerifyEmail: boolean;
    status?: string;
}

defineProps<Props>();

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Profile settings',
        href: '/settings/profile',
    },
];

const page = usePage<SharedData>();
const user = page.props.auth.user as User;

const form = useForm({
    name: user.name,
    email: user.email,
});

// Provider-specific data
const isProvider = computed(() => user.role === 'provider');
const loading = ref(false);
const saving = ref(false);
const error = ref(null);
const successMessage = ref('');

const providerProfile = ref({
    specialization: '',
    bio: '',
    education: '',
    license_number: '',
    gender: '',
    languages: [],
    practice_locations: [],
    accepts_insurance: false,
    insurance_providers: [],
    pricing: {
        consultation: 0,
        follow_up: 0
    },
    certifications: []
});

const newLanguage = ref('');
const newCertification = ref('');
const newInsuranceProvider = ref('');
const newLocation = ref({
    address: '',
    city: '',
    state: '',
    zip_code: '',
    coordinates: [0, 0],
    is_primary: false
});

// Available options
const genderOptions = [
    { value: 'male', label: 'Male' },
    { value: 'female', label: 'Female' },
    { value: 'other', label: 'Other' }
];

const commonLanguages = [
    'English', 'Spanish', 'French', 'German', 'Italian', 'Portuguese',
    'Arabic', 'Chinese', 'Japanese', 'Korean', 'Hindi', 'Russian'
];

const submit = () => {
    form.patch(route('profile.update'), {
        preserveScroll: true,
    });
};

// Provider profile methods
const fetchProviderProfile = async () => {
    if (!isProvider.value) return;

    loading.value = true;
    try {
        const response = await axios.get('/provider/get-profile');
        if (response.data) {
            const providerData = response.data;

            providerProfile.value = {
                specialization: providerData.specialization || '',
                bio: providerData.bio || '',
                education: providerData.education || '',
                license_number: providerData.license_number || '',
                gender: providerData.gender || '',
                languages: providerData.languages || [],
                practice_locations: providerData.practice_locations || [],
                accepts_insurance: providerData.accepts_insurance || false,
                insurance_providers: providerData.insurance_providers || [],
                pricing: {
                    consultation: providerData.pricing?.consultation || providerData.consultation_fee || 0,
                    follow_up: providerData.pricing?.follow_up || 0
                },
                certifications: providerData.certifications || []
            };
        }
    } catch (err) {
        console.error('Error fetching provider profile:', err);
        error.value = 'Failed to load provider profile data';
    } finally {
        loading.value = false;
    }
};

const saveProviderProfile = async () => {
    saving.value = true;
    error.value = null;
    successMessage.value = '';

    try {
        await axios.post('/provider/profile', providerProfile.value);
        successMessage.value = 'Provider profile updated successfully!';
        setTimeout(() => {
            successMessage.value = '';
        }, 3000);
    } catch (err) {
        console.error('Error saving provider profile:', err);
        error.value = err.response?.data?.message || 'Failed to save provider profile';
    } finally {
        saving.value = false;
    }
};

// Language methods
const addLanguage = () => {
    if (newLanguage.value.trim() && !providerProfile.value.languages.includes(newLanguage.value.trim())) {
        providerProfile.value.languages.push(newLanguage.value.trim());
        newLanguage.value = '';
    }
};

const removeLanguage = (index) => {
    providerProfile.value.languages.splice(index, 1);
};

// Certification methods
const addCertification = () => {
    if (newCertification.value.trim()) {
        providerProfile.value.certifications.push(newCertification.value.trim());
        newCertification.value = '';
    }
};

const removeCertification = (index) => {
    providerProfile.value.certifications.splice(index, 1);
};

// Practice location methods
const addLocation = () => {
    if (newLocation.value.address.trim()) {
        providerProfile.value.practice_locations.push({
            address: newLocation.value.address.trim(),
            city: newLocation.value.city.trim(),
            state: newLocation.value.state.trim(),
            zip_code: newLocation.value.zip_code.trim(),
            coordinates: newLocation.value.coordinates,
            is_primary: newLocation.value.is_primary || providerProfile.value.practice_locations.length === 0
        });

        newLocation.value = {
            address: '',
            city: '',
            state: '',
            zip_code: '',
            coordinates: [0, 0],
            is_primary: false
        };
    }
};

const removeLocation = (index) => {
    providerProfile.value.practice_locations.splice(index, 1);
};

const setPrimaryLocation = (index) => {
    providerProfile.value.practice_locations.forEach((location, i) => {
        location.is_primary = i === index;
    });
};

// Insurance provider methods
const addInsuranceProvider = () => {
    if (newInsuranceProvider.value.trim() && !providerProfile.value.insurance_providers.includes(newInsuranceProvider.value.trim())) {
        providerProfile.value.insurance_providers.push(newInsuranceProvider.value.trim());
        newInsuranceProvider.value = '';
    }
};

const removeInsuranceProvider = (index) => {
    providerProfile.value.insurance_providers.splice(index, 1);
};

// Initialize on mount
onMounted(() => {
    if (isProvider.value) {
        fetchProviderProfile();
    }
    fetchFounderClubInfo();
});
</script>

<template>
    <AppLayout :breadcrumbs="breadcrumbs">
        <Head title="Profile settings" />

        <SettingsLayout>
            <div class="flex flex-col space-y-6">
                <HeadingSmall title="Profile information" description="Update your name and email address" />

                <form @submit.prevent="submit" class="space-y-6">
                    <div class="grid gap-2">
                        <Label for="name">Name</Label>
                        <Input id="name" class="mt-1 block w-full" v-model="form.name" required autocomplete="name" placeholder="Full name" />
                        <InputError class="mt-2" :message="form.errors.name" />
                    </div>

                    <div class="grid gap-2">
                        <Label for="email">Email address</Label>
                        <Input
                            id="email"
                            type="email"
                            class="mt-1 block w-full"
                            v-model="form.email"
                            required
                            autocomplete="username"
                            placeholder="Email address"
                        />
                        <InputError class="mt-2" :message="form.errors.email" />
                    </div>

                    <div v-if="mustVerifyEmail && !user.email_verified_at">
                        <p class="-mt-4 text-sm text-muted-foreground">
                            Your email address is unverified.
                            <Link
                                :href="route('verification.send')"
                                method="post"
                                as="button"
                                class="text-foreground underline decoration-neutral-300 underline-offset-4 transition-colors duration-300 ease-out hover:decoration-current! dark:decoration-neutral-500"
                            >
                                Click here to resend the verification email.
                            </Link>
                        </p>

                        <div v-if="status === 'verification-link-sent'" class="mt-2 text-sm font-medium text-green-600">
                            A new verification link has been sent to your email address.
                        </div>
                    </div>

                    <div class="flex items-center gap-4">
                        <Button :disabled="form.processing">Save</Button>

                        <Transition
                            enter-active-class="transition ease-in-out"
                            enter-from-class="opacity-0"
                            leave-active-class="transition ease-in-out"
                            leave-to-class="opacity-0"
                        >
                            <p v-show="form.recentlySuccessful" class="text-sm text-neutral-600">Saved.</p>
                        </Transition>
                    </div>
                </form>
            </div>
                <!-- Founder Club Badge -->
                <div v-if="user.is_founder_member" class="bg-gradient-to-r from-purple-50 to-indigo-50 border border-purple-200 rounded-lg p-6">
                    <div class="flex items-center space-x-4">
                        <div class="flex-shrink-0">
                            <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-indigo-600 rounded-full flex items-center justify-center">
                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                                </svg>
                            </div>
                        </div>
                        <div class="flex-1">
                            <h3 class="text-lg font-semibold text-purple-900">Medroid's Founders' Club</h3>
                            <p class="text-purple-700 text-sm">You're part of our exclusive founders community!</p>
                            <div v-if="founderClubInfo && !loadingClubInfo" class="mt-2 space-y-1">
                                <p class="text-sm text-purple-600">
                                    <span class="font-medium">Membership Level:</span> {{ founderClubInfo.membership_level }}
                                </p>
                                <p class="text-sm text-purple-600">
                                    <span class="font-medium">Founder Code:</span> {{ founderClubInfo.founder_code_used }}
                                </p>
                                <p class="text-sm text-purple-600">
                                    <span class="font-medium">Member Since:</span> {{ new Date(founderClubInfo.joined_at).toLocaleDateString() }}
                                </p>
                            </div>
                            <div v-else-if="loadingClubInfo" class="mt-2">
                                <div class="animate-pulse flex space-x-2">
                                    <div class="h-3 bg-purple-200 rounded w-24"></div>
                                    <div class="h-3 bg-purple-200 rounded w-16"></div>
                                </div>
                            </div>
                        </div>
                        <div class="flex-shrink-0">
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                Founder Member
                            </span>
                        </div>
                    </div>
                </div>

            <!-- Provider-specific settings -->
            <div v-if="isProvider" class="flex flex-col space-y-6">
                <!-- Loading state -->
                <div v-if="loading" class="flex items-center justify-center py-8">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                    <span class="ml-2 text-gray-600">Loading provider settings...</span>
                </div>

                <!-- Error message -->
                <div v-if="error" class="bg-red-50 border border-red-200 rounded-lg p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm text-red-800">{{ error }}</p>
                        </div>
                    </div>
                </div>

                <!-- Success message -->
                <div v-if="successMessage" class="bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm text-green-800">{{ successMessage }}</p>
                        </div>
                    </div>
                </div>

                <!-- Provider settings form -->
                <div v-if="!loading">
                    <HeadingSmall title="Provider Settings" description="Manage your professional profile and appointment preferences" />

                    <form @submit.prevent="saveProviderProfile" class="space-y-8 mt-6">
                        <!-- Professional Information -->
                        <div class="bg-white rounded-lg border border-gray-200 p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Professional Information</h3>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <Label for="specialization">Specialization *</Label>
                                    <Input
                                        id="specialization"
                                        v-model="providerProfile.specialization"
                                        placeholder="e.g., Cardiology, Dermatology"
                                        required
                                        class="mt-1"
                                    />
                                </div>

                                <div>
                                    <Label for="license_number">License Number</Label>
                                    <Input
                                        id="license_number"
                                        v-model="providerProfile.license_number"
                                        placeholder="Medical license number"
                                        class="mt-1"
                                    />
                                </div>



                                <div>
                                    <Label for="gender">Gender</Label>
                                    <select
                                        id="gender"
                                        v-model="providerProfile.gender"
                                        class="mt-1 block w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                                    >
                                        <option value="">Select Gender</option>
                                        <option v-for="option in genderOptions" :key="option.value" :value="option.value">
                                            {{ option.label }}
                                        </option>
                                    </select>
                                </div>
                            </div>

                            <div class="mt-4">
                                <Label for="education">Education</Label>
                                <textarea
                                    id="education"
                                    v-model="providerProfile.education"
                                    rows="3"
                                    placeholder="Medical school, residency, fellowships..."
                                    class="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                                ></textarea>
                            </div>

                            <div class="mt-4">
                                <Label for="bio">Bio</Label>
                                <textarea
                                    id="bio"
                                    v-model="providerProfile.bio"
                                    rows="4"
                                    placeholder="Tell patients about yourself, your approach to care, and your experience..."
                                    class="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                                ></textarea>
                            </div>
                        </div>

                        <!-- Languages -->
                        <div class="bg-white rounded-lg border border-gray-200 p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Languages</h3>

                            <div class="flex flex-wrap gap-2 mb-4">
                                <span
                                    v-for="(language, index) in providerProfile.languages"
                                    :key="index"
                                    class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800"
                                >
                                    {{ language }}
                                    <button
                                        @click="removeLanguage(index)"
                                        type="button"
                                        class="ml-2 text-blue-600 hover:text-blue-800"
                                    >
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                        </svg>
                                    </button>
                                </span>
                            </div>

                            <div class="flex gap-2">
                                <Input
                                    v-model="newLanguage"
                                    @keyup.enter="addLanguage"
                                    placeholder="Add a language"
                                    class="flex-1"
                                />
                                <Button
                                    @click="addLanguage"
                                    type="button"
                                    variant="outline"
                                >
                                    Add
                                </Button>
                            </div>
                        </div>

                        <!-- Practice Locations -->
                        <div class="bg-white rounded-lg border border-gray-200 p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Practice Locations</h3>

                            <!-- Add new location form -->
                            <div class="border border-gray-200 rounded-lg p-4 mb-4">
                                <h4 class="text-sm font-medium text-gray-700 mb-3">Add New Location</h4>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div class="md:col-span-2">
                                        <Label for="new_address">Address</Label>
                                        <Input
                                            id="new_address"
                                            v-model="newLocation.address"
                                            placeholder="Street address"
                                            class="mt-1"
                                        />
                                    </div>
                                    <div>
                                        <Label for="new_city">City</Label>
                                        <Input
                                            id="new_city"
                                            v-model="newLocation.city"
                                            placeholder="City"
                                            class="mt-1"
                                        />
                                    </div>
                                    <div>
                                        <Label for="new_state">State</Label>
                                        <Input
                                            id="new_state"
                                            v-model="newLocation.state"
                                            placeholder="State"
                                            class="mt-1"
                                        />
                                    </div>
                                    <div>
                                        <Label for="new_zip">Postcode</Label>
                                        <Input
                                            id="new_zip"
                                            v-model="newLocation.zip_code"
                                            placeholder="Postcode"
                                            class="mt-1"
                                        />
                                    </div>
                                    <div class="flex items-center">
                                        <input
                                            v-model="newLocation.is_primary"
                                            type="checkbox"
                                            id="is_primary_new"
                                            class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                        />
                                        <label for="is_primary_new" class="ml-2 block text-sm text-gray-700">
                                            Set as primary location
                                        </label>
                                    </div>
                                </div>
                                <Button
                                    @click="addLocation"
                                    type="button"
                                    variant="outline"
                                    class="mt-3"
                                >
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                    </svg>
                                    Add Location
                                </Button>
                            </div>

                            <!-- Existing locations -->
                            <div v-if="providerProfile.practice_locations.length > 0" class="space-y-3">
                                <div
                                    v-for="(location, index) in providerProfile.practice_locations"
                                    :key="index"
                                    class="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                                >
                                    <div class="flex-1">
                                        <div class="flex items-center">
                                            <span class="text-sm font-medium text-gray-900">
                                                {{ location.address }}
                                            </span>
                                            <span v-if="location.is_primary" class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                Primary
                                            </span>
                                        </div>
                                        <p class="text-sm text-gray-600">
                                            {{ location.city }}, {{ location.state }} {{ location.zip_code }}
                                        </p>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <button
                                            v-if="!location.is_primary"
                                            @click="setPrimaryLocation(index)"
                                            type="button"
                                            class="text-sm text-blue-600 hover:text-blue-800"
                                        >
                                            Set Primary
                                        </button>
                                        <button
                                            @click="removeLocation(index)"
                                            type="button"
                                            class="text-red-600 hover:text-red-800"
                                        >
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div v-else class="text-center py-4 text-gray-500">
                                No practice locations added yet
                            </div>
                        </div>

                        <!-- Pricing & Insurance -->
                        <div class="bg-white rounded-lg border border-gray-200 p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Pricing & Insurance</h3>

                            <!-- Pricing -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                                <div>
                                    <Label for="consultation_fee">Consultation Fee ($)</Label>
                                    <Input
                                        id="consultation_fee"
                                        v-model.number="providerProfile.pricing.consultation"
                                        type="number"
                                        min="0"
                                        step="0.01"
                                        placeholder="0.00"
                                        class="mt-1"
                                    />
                                </div>
                                <div>
                                    <Label for="follow_up_fee">Follow-up Fee ($)</Label>
                                    <Input
                                        id="follow_up_fee"
                                        v-model.number="providerProfile.pricing.follow_up"
                                        type="number"
                                        min="0"
                                        step="0.01"
                                        placeholder="0.00"
                                        class="mt-1"
                                    />
                                </div>
                            </div>

                            <!-- Insurance -->
                            <div class="mb-4">
                                <div class="flex items-center mb-3">
                                    <input
                                        v-model="providerProfile.accepts_insurance"
                                        type="checkbox"
                                        id="accepts_insurance"
                                        class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                    />
                                    <label for="accepts_insurance" class="ml-2 block text-sm font-medium text-gray-700">
                                        Accept Insurance
                                    </label>
                                </div>
                            </div>

                            <!-- Insurance Providers -->
                            <div v-if="providerProfile.accepts_insurance">
                                <Label class="block text-sm font-medium text-gray-700 mb-2">Insurance Providers</Label>
                                <div class="flex gap-2 mb-3">
                                    <Input
                                        v-model="newInsuranceProvider"
                                        placeholder="Add insurance provider"
                                        class="flex-1"
                                        @keyup.enter="addInsuranceProvider"
                                    />
                                    <Button
                                        @click="addInsuranceProvider"
                                        type="button"
                                        variant="outline"
                                    >
                                        Add
                                    </Button>
                                </div>
                                <div v-if="providerProfile.insurance_providers.length > 0" class="flex flex-wrap gap-2">
                                    <span
                                        v-for="(provider, index) in providerProfile.insurance_providers"
                                        :key="index"
                                        class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800"
                                    >
                                        {{ provider }}
                                        <button
                                            @click="removeInsuranceProvider(index)"
                                            type="button"
                                            class="ml-2 text-blue-600 hover:text-blue-800"
                                        >
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                            </svg>
                                        </button>
                                    </span>
                                </div>
                            </div>
                        </div>

                        <!-- Certifications -->
                        <div class="bg-white rounded-lg border border-gray-200 p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Certifications</h3>

                            <div class="space-y-2 mb-4">
                                <div
                                    v-for="(certification, index) in providerProfile.certifications"
                                    :key="index"
                                    class="flex justify-between items-center p-3 bg-gray-50 rounded-lg"
                                >
                                    <span class="text-sm">{{ certification }}</span>
                                    <button
                                        @click="removeCertification(index)"
                                        type="button"
                                        class="text-red-600 hover:text-red-800"
                                    >
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                        </svg>
                                    </button>
                                </div>
                            </div>

                            <div class="flex gap-2">
                                <Input
                                    v-model="newCertification"
                                    @keyup.enter="addCertification"
                                    placeholder="Add a certification"
                                    class="flex-1"
                                />
                                <Button
                                    @click="addCertification"
                                    type="button"
                                    variant="outline"
                                >
                                    Add
                                </Button>
                            </div>
                        </div>

                        <!-- Save Button -->
                        <div class="flex justify-end pt-6 border-t">
                            <Button
                                type="submit"
                                :disabled="saving"
                                class="min-w-[120px]"
                            >
                                <div v-if="saving" class="flex items-center">
                                    <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                                    Saving...
                                </div>
                                <span v-else>Save Provider Settings</span>
                            </Button>
                        </div>
                    </form>
                </div>
            </div>

            <DeleteUser />
        </SettingsLayout>
    </AppLayout>
</template>
