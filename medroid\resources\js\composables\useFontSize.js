import { ref, computed, watch } from 'vue'

// Font size levels
const FONT_SIZES = {
  small: {
    name: 'Small',
    scale: 0.875,
    description: 'Smaller text for better screen space'
  },
  normal: {
    name: 'Normal',
    scale: 1,
    description: 'Default text size'
  },
  large: {
    name: 'Large',
    scale: 1.125,
    description: 'Larger text for better readability'
  },
  xlarge: {
    name: 'Extra Large',
    scale: 1.25,
    description: 'Extra large text for accessibility'
  }
}

// Global font size state
const currentFontSize = ref('normal')

// Load saved font size from localStorage
const loadFontSize = () => {
  const saved = localStorage.getItem('medroid-font-size')
  if (saved && FONT_SIZES[saved]) {
    currentFontSize.value = saved
  }
}

// Save font size to localStorage
const saveFontSize = (size) => {
  localStorage.setItem('medroid-font-size', size)
}

// Initialize font size on first load
loadFontSize()

export function useFontSize() {
  // Computed properties
  const fontSizeScale = computed(() => FONT_SIZES[currentFontSize.value]?.scale || 1)
  const fontSizeName = computed(() => FONT_SIZES[currentFontSize.value]?.name || 'Normal')
  const fontSizeDescription = computed(() => FONT_SIZES[currentFontSize.value]?.description || '')
  
  // Available font sizes
  const availableFontSizes = computed(() => Object.entries(FONT_SIZES).map(([key, value]) => ({
    key,
    ...value
  })))

  // CSS custom properties for font scaling
  const fontSizeStyles = computed(() => ({
    '--font-scale': fontSizeScale.value,
    '--text-xs': `${0.75 * fontSizeScale.value}rem`,
    '--text-sm': `${0.875 * fontSizeScale.value}rem`,
    '--text-base': `${1 * fontSizeScale.value}rem`,
    '--text-lg': `${1.125 * fontSizeScale.value}rem`,
    '--text-xl': `${1.25 * fontSizeScale.value}rem`,
    '--text-2xl': `${1.5 * fontSizeScale.value}rem`,
    '--text-3xl': `${1.875 * fontSizeScale.value}rem`,
    '--text-4xl': `${2.25 * fontSizeScale.value}rem`,
    '--text-5xl': `${3 * fontSizeScale.value}rem`,
    '--text-6xl': `${3.75 * fontSizeScale.value}rem`,
  }))

  // Methods
  const setFontSize = (size) => {
    if (FONT_SIZES[size]) {
      currentFontSize.value = size
      saveFontSize(size)
      applyFontSizeToDocument()
    }
  }

  const increaseFontSize = () => {
    const sizes = Object.keys(FONT_SIZES)
    const currentIndex = sizes.indexOf(currentFontSize.value)
    if (currentIndex < sizes.length - 1) {
      setFontSize(sizes[currentIndex + 1])
    }
  }

  const decreaseFontSize = () => {
    const sizes = Object.keys(FONT_SIZES)
    const currentIndex = sizes.indexOf(currentFontSize.value)
    if (currentIndex > 0) {
      setFontSize(sizes[currentIndex - 1])
    }
  }

  const resetFontSize = () => {
    setFontSize('normal')
  }

  // Apply font size to document root
  const applyFontSizeToDocument = () => {
    const root = document.documentElement
    Object.entries(fontSizeStyles.value).forEach(([property, value]) => {
      root.style.setProperty(property, value)
    })
    
    // Add a class to the body for CSS targeting
    document.body.className = document.body.className.replace(/font-size-\w+/g, '')
    document.body.classList.add(`font-size-${currentFontSize.value}`)
  }

  // Watch for font size changes and apply them
  watch(currentFontSize, () => {
    applyFontSizeToDocument()
  }, { immediate: true })

  return {
    // State
    currentFontSize,
    fontSizeScale,
    fontSizeName,
    fontSizeDescription,
    availableFontSizes,
    fontSizeStyles,
    
    // Methods
    setFontSize,
    increaseFontSize,
    decreaseFontSize,
    resetFontSize,
    applyFontSizeToDocument,
    
    // Constants
    FONT_SIZES
  }
}
