<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('referrals', function (Blueprint $table) {
            // Drop the unique constraint on referral_code if it exists
            try {
                $table->dropUnique(['referral_code']);
            } catch (\Exception $e) {
                // Constraint might not exist, continue
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('referrals', function (Blueprint $table) {
            // Re-add the unique constraint on referral_code
            $table->unique('referral_code');
        });
    }
};
