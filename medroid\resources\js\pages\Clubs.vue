<script setup>
import AppLayout from '@/layouts/AppLayout.vue';
import { Head } from '@inertiajs/vue3';
import { ref, onMounted, computed } from 'vue';
import axios from 'axios';

const breadcrumbs = [
    { title: 'Dashboard', href: '/dashboard' },
    { title: 'Clubs', href: '/clubs' },
];

const loading = ref(false);
const clubMembers = ref([]);
const founderCodes = ref([]);
const stats = ref({
    total_members: 0,
    active_codes: 0,
    total_revenue: 0
});

// Invitation form
const showInviteModal = ref(false);
const inviteForm = ref({
    email: '',
    club_type: 'regular'
});

const bulkInviteForm = ref({
    emails: '',
    club_type: 'regular'
});

const showBulkInviteModal = ref(false);

// Computed property for founder link
const founderLink = computed(() => {
    if (typeof window !== 'undefined') {
        return `${window.location.origin}/join-founders`;
    }
    return '/join-founders';
});
const isSubmittingInvite = ref(false);
const inviteSuccess = ref(false);
const inviteError = ref('');

const fetchClubMembers = async () => {
    loading.value = true;
    try {
        const response = await axios.get('/clubs-list');
        clubMembers.value = response.data.data || [];
    } catch (error) {
        console.error('Error fetching club members:', error);
    } finally {
        loading.value = false;
    }
};

const fetchFounderCodes = async () => {
    try {
        const response = await axios.get('/clubs-codes');
        founderCodes.value = response.data.data || [];
    } catch (error) {
        console.error('Error fetching founder codes:', error);
    }
};

const fetchStats = async () => {
    try {
        const response = await axios.get('/clubs-stats');
        stats.value = response.data;
    } catch (error) {
        console.error('Error fetching club stats:', error);
    }
};

onMounted(() => {
    fetchClubMembers();
    fetchFounderCodes();
    fetchStats();
});

const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
};

const getMembershipTypeClass = (type) => {
    switch (type) {
        case 'founder':
            return 'bg-purple-100 text-purple-800';
        case 'premium':
            return 'bg-gold-100 text-gold-800';
        case 'basic':
            return 'bg-blue-100 text-blue-800';
        default:
            return 'bg-gray-100 text-gray-800';
    }
};

const getStatusClass = (status) => {
    switch (status) {
        case 'active':
            return 'bg-green-100 text-green-800';
        case 'expired':
            return 'bg-red-100 text-red-800';
        case 'pending':
            return 'bg-yellow-100 text-yellow-800';
        default:
            return 'bg-gray-100 text-gray-800';
    }
};

// Invitation functions
const openInviteModal = () => {
    showInviteModal.value = true;
    inviteForm.value = {
        email: '',
        club_type: 'regular'
    };
    inviteSuccess.value = false;
    inviteError.value = '';
};

const closeInviteModal = () => {
    showInviteModal.value = false;
    inviteForm.value = {
        email: '',
        club_type: 'regular'
    };
    inviteSuccess.value = false;
    inviteError.value = '';
};

// Bulk invitation functions
const openBulkInviteModal = () => {
    showBulkInviteModal.value = true;
    bulkInviteForm.value = {
        emails: '',
        club_type: 'regular'
    };
    inviteSuccess.value = false;
    inviteError.value = '';
};

const closeBulkInviteModal = () => {
    showBulkInviteModal.value = false;
    bulkInviteForm.value = {
        emails: '',
        club_type: 'regular'
    };
    inviteSuccess.value = false;
    inviteError.value = '';
};

const sendInvitation = async () => {
    if (!inviteForm.value.email.trim()) {
        inviteError.value = 'Email is required';
        return;
    }

    try {
        isSubmittingInvite.value = true;
        inviteError.value = '';

        const response = await axios.post('/send-club-invitation', {
            email: inviteForm.value.email.trim(),
            club_type: inviteForm.value.club_type
        });

        if (response.data.success) {
            inviteSuccess.value = true;
            inviteForm.value.email = '';
            setTimeout(() => {
                closeInviteModal();
            }, 2000);
        } else {
            inviteError.value = response.data.message || 'Failed to send invitation';
        }
    } catch (error) {
        console.error('Error sending invitation:', error);
        inviteError.value = error.response?.data?.message || 'Failed to send invitation. Please try again.';
    } finally {
        isSubmittingInvite.value = false;
    }
};

// Send bulk invitations
const sendBulkInvitations = async () => {
    if (!bulkInviteForm.value.emails.trim()) {
        inviteError.value = 'Please enter at least one email address';
        return;
    }

    try {
        isSubmittingInvite.value = true;
        inviteError.value = '';

        // Parse emails from comma-separated string
        const emails = bulkInviteForm.value.emails
            .split(',')
            .map(email => email.trim())
            .filter(email => email.length > 0);

        if (emails.length === 0) {
            inviteError.value = 'Please enter valid email addresses';
            return;
        }

        const response = await axios.post('/send-bulk-club-invitations', {
            emails: emails,
            club_type: bulkInviteForm.value.club_type
        });

        if (response.data.success) {
            inviteSuccess.value = true;
            setTimeout(() => {
                closeBulkInviteModal();
            }, 3000);

            // Refresh data
            await fetchClubMembers();
        } else {
            inviteError.value = response.data.message || 'Failed to send bulk invitations';
        }
    } catch (error) {
        console.error('Error sending bulk invitations:', error);
        inviteError.value = error.response?.data?.message || 'Failed to send bulk invitations';
    } finally {
        isSubmittingInvite.value = false;
    }
};

const getClubTypeInfo = (clubType) => {
    const clubTypes = {
        founder: {
            name: 'Medroid Founders Club',
            description: 'Exclusive founder membership with all premium benefits',
            color: 'text-purple-600',
            bgColor: 'bg-purple-50',
            borderColor: 'border-purple-200'
        },
        premium: {
            name: 'Premium Club',
            description: 'Enhanced features and priority support',
            color: 'text-orange-600',
            bgColor: 'bg-orange-50',
            borderColor: 'border-orange-200'
        },
        regular: {
            name: 'Regular Club',
            description: 'Standard membership with basic benefits',
            color: 'text-blue-600',
            bgColor: 'bg-blue-50',
            borderColor: 'border-blue-200'
        }
    };
    return clubTypes[clubType] || clubTypes.regular;
};

// Shareable founder link functions
const copyFounderLink = async () => {
    const link = founderLink.value;
    try {
        await navigator.clipboard.writeText(link);
        alert('Founder link copied to clipboard!');
    } catch (err) {
        console.error('Failed to copy: ', err);
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = link;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        alert('Founder link copied to clipboard!');
    }
};

const shareFounderLinkLinkedIn = () => {
    const link = founderLink.value;
    const text = encodeURIComponent(`🤖 EXCLUSIVE: Welcome to the Medroid Founders' Club

Meet your new AI Doctor!

Get instant medical advice 24/7 with Medroid - powered by advanced medical AI that understands symptoms, suggests treatments, and provides evidence-based care guidance.

Get exclusive access to Medroid with:

✨ Founders Club badge & priority support
🔥 Early access to advanced AI features
💰 $10 welcome bonus
💬 Private Slack access
👩‍⚕️ Direct access to our founders

Join now: ${link}

Be among the first to have an AI Doctor in your pocket 📱⚕️

#AIDoctor #Health #HealthTech #Innovation`);

    const url = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(link)}&summary=${text}`;
    window.open(url, '_blank');
};

const shareFounderLinkWhatsApp = () => {
    const link = founderLink.value;
    const text = encodeURIComponent(`🤖 EXCLUSIVE: Welcome to the Medroid Founders' Club

Meet your new AI Doctor!

Get instant medical advice 24/7 with Medroid - powered by advanced medical AI that understands symptoms, suggests treatments, and provides evidence-based care guidance.

Get exclusive access to Medroid with:

✨ Founders' Club badge & priority support
🔥 Early access to advanced AI features
💰 $10 welcome bonus
💬 Private Slack access
👩‍⚕️ Direct access to our founders

Join now: ${link}

Be among the first to have an AI Doctor in your pocket 📱⚕️

#AIDoctor #Health #HealthTech #Innovation`);

    const url = `https://wa.me/?text=${text}`;
    window.open(url, '_blank');
};
</script>

<template>
    <Head title="Clubs Management" />

    <AppLayout :breadcrumbs="breadcrumbs">
        <div class="p-6">
            <div class="mb-6">
                <h1 class="text-2xl font-bold text-gray-900">Clubs Management</h1>
                <p class="text-gray-600">Manage club memberships and founder codes</p>
            </div>

            <!-- Stats Cards -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-2 bg-purple-100 rounded-lg">
                            <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Total Members</p>
                            <p class="text-2xl font-bold text-gray-900">{{ stats.total_members }}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-2 bg-blue-100 rounded-lg">
                            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.99 1.99 0 013 12V7a4 4 0 014-4z" />
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Active Codes</p>
                            <p class="text-2xl font-bold text-gray-900">{{ stats.active_codes }}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-2 bg-green-100 rounded-lg">
                            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Total Revenue</p>
                            <p class="text-2xl font-bold text-gray-900">${{ stats.total_revenue }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Shareable Founder Link -->
            <div class="bg-gradient-to-r from-medroid-sage to-medroid-cream border border-medroid-orange/30 rounded-lg shadow mb-6">
                <div class="px-6 py-4 border-b border-medroid-orange/30">
                    <div class="flex justify-between items-center">
                        <div>
                            <h2 class="text-lg font-medium text-medroid-navy">Medroid's Founders' Club - Shareable Link</h2>
                            <p class="text-sm text-medroid-slate">Share this link on social media to invite people to join the founders club</p>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-medroid-orange/20 text-medroid-orange">
                                Public Link
                            </span>
                        </div>
                    </div>
                </div>
                <div class="p-6">
                    <div class="flex items-center space-x-4">
                        <div class="flex-1">
                            <div class="flex items-center space-x-3 p-3 bg-white border border-medroid-orange/30 rounded-lg">
                                <svg class="w-5 h-5 text-medroid-orange" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.102m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
                                </svg>
                                <code class="flex-1 text-sm font-mono text-medroid-navy">{{ founderLink }}</code>
                                <button
                                    @click="copyFounderLink"
                                    class="text-medroid-orange hover:text-medroid-orange/80 transition-colors"
                                    title="Copy link"
                                >
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                        <div class="flex space-x-2">
                            <button
                                @click="shareFounderLinkLinkedIn"
                                class="bg-medroid-teal hover:bg-medroid-teal/90 text-white px-3 py-2 rounded-lg transition-colors flex items-center space-x-1"
                            >
                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                                </svg>
                                <span class="text-xs">LinkedIn</span>
                            </button>
                            <button
                                @click="shareFounderLinkWhatsApp"
                                class="bg-medroid-orange hover:bg-medroid-orange/90 text-white px-3 py-2 rounded-lg transition-colors flex items-center space-x-1"
                            >
                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
                                </svg>
                                <span class="text-xs">WhatsApp</span>
                            </button>
                        </div>
                    </div>
                    <div class="mt-4 text-xs text-medroid-slate">
                        <strong>Note:</strong> Anyone who signs up through this link will automatically become a Medroid Founder with platinum membership and receive a $10 welcome bonus.
                    </div>
                </div>
            </div>

            <!-- Send Invitation Section -->
            <div class="bg-white rounded-lg shadow mb-6">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex justify-between items-center">
                        <div>
                            <h2 class="text-lg font-medium text-gray-900">Send Club Invitation</h2>
                            <p class="text-sm text-gray-500">Invite users to join specific club types</p>
                        </div>
                        <div class="flex space-x-3">
                            <button
                                @click="openBulkInviteModal"
                                class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center space-x-2"
                            >
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                </svg>
                                <span>Bulk Invite</span>
                            </button>
                            <button
                                @click="openInviteModal"
                                class="bg-medroid-orange hover:bg-medroid-orange/90 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center space-x-2"
                            >
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                                <span>Send Invitation</span>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <!-- Club Type Cards -->
                        <div v-for="(info, type) in {
                            founder: getClubTypeInfo('founder'),
                            premium: getClubTypeInfo('premium'),
                            regular: getClubTypeInfo('regular')
                        }" :key="type" :class="[
                            'p-4 rounded-lg border-2 cursor-pointer transition-all duration-200',
                            info.bgColor, info.borderColor
                        ]">
                            <div class="flex items-center space-x-3 mb-2">
                                <div :class="['w-3 h-3 rounded-full', type === 'founder' ? 'bg-purple-500' : type === 'premium' ? 'bg-orange-500' : 'bg-blue-500']"></div>
                                <h3 :class="['font-semibold', info.color]">{{ info.name }}</h3>
                            </div>
                            <p class="text-sm text-gray-600">{{ info.description }}</p>
                            <div class="mt-3">
                                <span class="text-xs font-medium text-gray-500">Benefits include:</span>
                                <ul class="text-xs text-gray-600 mt-1 space-y-1">
                                    <li v-if="type === 'founder'">• Verified founder badge</li>
                                    <li v-if="type === 'founder' || type === 'premium'">• Priority support</li>
                                    <li v-if="type === 'founder'">• Exclusive features access</li>
                                    <li>• Welcome bonus credits</li>
                                    <li v-if="type === 'founder'">• Early feature access</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Club Members Table -->
            <div class="bg-white rounded-lg shadow mb-6">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-medium text-gray-900">Club Members</h2>
                </div>
                
                <div v-if="loading" class="p-6">
                    <div class="animate-pulse">
                        <div class="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
                        <div class="h-4 bg-gray-200 rounded w-1/2 mb-4"></div>
                        <div class="h-4 bg-gray-200 rounded w-5/6"></div>
                    </div>
                </div>

                <div v-else-if="clubMembers.length === 0" class="p-6 text-center text-gray-500">
                    No club members found.
                </div>

                <div v-else class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Member</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Membership Type</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Joined Date</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Expiry Date</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr v-for="member in clubMembers" :key="member.id">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">{{ member.user?.name || 'N/A' }}</div>
                                    <div class="text-sm text-gray-500">{{ member.user?.email || 'N/A' }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span :class="getMembershipTypeClass(member.membership_type)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                                        {{ member.membership_type }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span :class="getStatusClass(member.status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                                        {{ member.status }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ formatDate(member.joined_at || member.created_at) }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ member.expires_at ? formatDate(member.expires_at) : 'Never' }}
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Founder Codes Table -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-medium text-gray-900">Founder Codes</h2>
                </div>
                
                <div v-if="founderCodes.length === 0" class="p-6 text-center text-gray-500">
                    No founder codes found.
                </div>

                <div v-else class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Owner</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Uses</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Max Uses</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr v-for="code in founderCodes" :key="code.id">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-mono font-medium text-gray-900">{{ code.code }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">{{ code.user?.name || 'N/A' }}</div>
                                    <div class="text-sm text-gray-500">{{ code.user?.email || 'N/A' }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ code.uses_count || 0 }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ code.max_uses || 'Unlimited' }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span :class="getStatusClass(code.status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                                        {{ code.status }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ formatDate(code.created_at) }}
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Invitation Modal -->
        <div v-if="showInviteModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex justify-between items-center">
                        <h3 class="text-lg font-medium text-gray-900">Send Club Invitation</h3>
                        <button
                            @click="closeInviteModal"
                            class="text-gray-400 hover:text-gray-600 transition-colors duration-200"
                        >
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                </div>

                <div class="p-6">
                    <!-- Success Message -->
                    <div v-if="inviteSuccess" class="mb-4 p-3 bg-green-50 border border-green-200 rounded-lg">
                        <div class="flex items-center space-x-2">
                            <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            <span class="text-green-700 font-medium">Invitation sent successfully!</span>
                        </div>
                    </div>

                    <!-- Error Message -->
                    <div v-if="inviteError" class="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                        <span class="text-red-700 text-sm">{{ inviteError }}</span>
                    </div>

                    <form @submit.prevent="sendInvitation" class="space-y-4">
                        <!-- Email Input -->
                        <div>
                            <label for="invite_email" class="block text-sm font-medium text-gray-700 mb-2">
                                Email Address
                            </label>
                            <input
                                id="invite_email"
                                v-model="inviteForm.email"
                                type="email"
                                required
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-medroid-orange"
                                placeholder="Enter email address"
                            />
                        </div>

                        <!-- Club Type Selection -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                Club Type
                            </label>
                            <div class="space-y-2">
                                <label v-for="(info, type) in {
                                    founder: getClubTypeInfo('founder'),
                                    premium: getClubTypeInfo('premium'),
                                    regular: getClubTypeInfo('regular')
                                }" :key="type" class="flex items-center p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors duration-200">
                                    <input
                                        v-model="inviteForm.club_type"
                                        :value="type"
                                        type="radio"
                                        class="w-4 h-4 text-medroid-orange border-gray-300 focus:ring-medroid-orange"
                                    />
                                    <div class="ml-3 flex-1">
                                        <div class="flex items-center space-x-2">
                                            <div :class="['w-2 h-2 rounded-full', type === 'founder' ? 'bg-purple-500' : type === 'premium' ? 'bg-orange-500' : 'bg-blue-500']"></div>
                                            <span class="font-medium text-gray-900">{{ info.name }}</span>
                                        </div>
                                        <p class="text-sm text-gray-500 mt-1">{{ info.description }}</p>
                                    </div>
                                </label>
                            </div>
                        </div>



                        <!-- Submit Button -->
                        <div class="flex space-x-3 pt-4">
                            <button
                                type="button"
                                @click="closeInviteModal"
                                class="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors duration-200"
                            >
                                Cancel
                            </button>
                            <button
                                type="submit"
                                :disabled="isSubmittingInvite || !inviteForm.email.trim()"
                                class="flex-1 px-4 py-2 bg-medroid-orange hover:bg-medroid-orange/90 disabled:bg-gray-400 text-white rounded-lg font-medium transition-colors duration-200"
                            >
                                {{ isSubmittingInvite ? 'Sending...' : 'Send Invitation' }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Bulk Invitation Modal -->
        <div v-if="showBulkInviteModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex justify-between items-center">
                        <h3 class="text-lg font-medium text-gray-900">Send Bulk Club Invitations</h3>
                        <button
                            @click="closeBulkInviteModal"
                            class="text-gray-400 hover:text-gray-600 transition-colors duration-200"
                        >
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                </div>

                <div class="p-6">
                    <!-- Success Message -->
                    <div v-if="inviteSuccess" class="mb-4 p-3 bg-green-50 border border-green-200 rounded-lg">
                        <div class="flex items-center space-x-2">
                            <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            <span class="text-green-700 font-medium">Bulk invitations sent successfully!</span>
                        </div>
                    </div>

                    <!-- Error Message -->
                    <div v-if="inviteError" class="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                        <span class="text-red-700 text-sm">{{ inviteError }}</span>
                    </div>

                    <form @submit.prevent="sendBulkInvitations" class="space-y-4">
                        <!-- Email Input -->
                        <div>
                            <label for="bulk_emails" class="block text-sm font-medium text-gray-700 mb-2">
                                Email Addresses (comma-separated)
                            </label>
                            <textarea
                                id="bulk_emails"
                                v-model="bulkInviteForm.emails"
                                rows="6"
                                placeholder="<EMAIL>, <EMAIL>, <EMAIL>"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                            ></textarea>
                            <p class="text-xs text-gray-500 mt-1">Enter multiple email addresses separated by commas</p>
                        </div>

                        <!-- Club Type Selection -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                Club Type
                            </label>
                            <div class="space-y-2">
                                <label v-for="(info, type) in {
                                    founder: getClubTypeInfo('founder'),
                                    premium: getClubTypeInfo('premium'),
                                    regular: getClubTypeInfo('regular')
                                }" :key="type" class="flex items-center p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors duration-200">
                                    <input
                                        v-model="bulkInviteForm.club_type"
                                        :value="type"
                                        type="radio"
                                        class="w-4 h-4 text-purple-600 border-gray-300 focus:ring-purple-500"
                                    />
                                    <div class="ml-3 flex-1">
                                        <div class="flex items-center space-x-2">
                                            <div :class="['w-2 h-2 rounded-full', type === 'founder' ? 'bg-purple-500' : type === 'premium' ? 'bg-orange-500' : 'bg-blue-500']"></div>
                                            <span class="font-medium text-gray-900">{{ info.name }}</span>
                                        </div>
                                        <p class="text-sm text-gray-500 mt-1">{{ info.description }}</p>
                                    </div>
                                </label>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="flex space-x-3 pt-4">
                            <button
                                type="button"
                                @click="closeBulkInviteModal"
                                class="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors duration-200"
                            >
                                Cancel
                            </button>
                            <button
                                type="submit"
                                :disabled="isSubmittingInvite || !bulkInviteForm.emails.trim()"
                                class="flex-1 px-4 py-2 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-400 text-white rounded-lg font-medium transition-colors duration-200"
                            >
                                {{ isSubmittingInvite ? 'Sending...' : 'Send Bulk Invitations' }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
