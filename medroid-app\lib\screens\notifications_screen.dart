import 'package:flutter/material.dart';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:medroid_app/models/notification_model.dart';
import 'package:medroid_app/services/notification_service.dart';

class NotificationsScreen extends StatefulWidget {
  const NotificationsScreen({Key? key}) : super(key: key);

  @override
  State<NotificationsScreen> createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends State<NotificationsScreen> {
  final List<NotificationModel> _notifications = [];
  bool _isLoading = true;
  bool _hasMore = true;
  int _offset = 0;
  final int _limit = 20;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();

    // Load notifications on all platforms
    _loadNotifications();
    _scrollController.addListener(_scrollListener);

    // Mobile-only notification setup
  }

  @override
  void dispose() {
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    super.dispose();
  }

  void _scrollListener() {
    if (_scrollController.position.pixels ==
        _scrollController.position.maxScrollExtent) {
      if (!_isLoading && _hasMore) {
        _loadMoreNotifications();
      }
    }
  }

  // Get the notification service for mobile platform
  NotificationService? _getNotificationService() {
    try {
      // For mobile, use NotificationService
      return RepositoryProvider.of<NotificationService>(context);
    } catch (e) {
      // If service is unavailable, show an error
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Notification service unavailable'),
          backgroundColor: Colors.red,
        ),
      );
      return null;
    }
  }

  Future<void> _loadNotifications() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final notificationService = _getNotificationService();
      if (notificationService == null) {
        setState(() {
          _isLoading = false;
        });
        return;
      }

      final notifications = await notificationService.getNotifications(
        limit: _limit,
        offset: 0,
      );

      setState(() {
        _notifications.clear();
        _notifications.addAll(notifications);
        _isLoading = false;
        _offset = notifications.length;
        _hasMore = notifications.length == _limit;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _loadMoreNotifications() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final notificationService = _getNotificationService();
      if (notificationService == null) {
        setState(() {
          _isLoading = false;
        });
        return;
      }

      final notifications = await notificationService.getNotifications(
        limit: _limit,
        offset: _offset,
      );

      setState(() {
        _notifications.addAll(notifications);
        _isLoading = false;
        _offset += notifications.length as int;
        _hasMore = notifications.length == _limit;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _markAllAsRead() async {
    final notificationService = _getNotificationService();
    if (notificationService == null) return;

    final success = await notificationService.markAllAsRead();

    if (success) {
      setState(() {
        for (final notification in _notifications) {
          if (notification.readAt == null) {
            // This is a bit of a hack since we can't modify the readAt field directly
            // We're creating a new notification with the same data but with readAt set to now
            final index = _notifications.indexOf(notification);
            _notifications[index] = NotificationModel(
              id: notification.id,
              userId: notification.userId,
              title: notification.title,
              body: notification.body,
              type: notification.type,
              data: notification.data,
              createdAt: notification.createdAt,
              readAt: DateTime.now(),
            );
          }
        }
      });
    }
  }

  Future<void> _markAsRead(NotificationModel notification) async {
    if (notification.isRead) return;

    final notificationService = _getNotificationService();
    if (notificationService == null) return;

    final success = await notificationService.markAsRead(notification.id);

    if (success) {
      setState(() {
        final index = _notifications.indexOf(notification);
        _notifications[index] = NotificationModel(
          id: notification.id,
          userId: notification.userId,
          title: notification.title,
          body: notification.body,
          type: notification.type,
          data: notification.data,
          createdAt: notification.createdAt,
          readAt: DateTime.now(),
        );
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: const Text(
          'Notifications',
          style: TextStyle(color: Colors.black87),
        ),
        iconTheme: const IconThemeData(color: Colors.black87),
        actions: [
          if (_notifications.isNotEmpty)
            TextButton(
              onPressed: _markAllAsRead,
              child: const Text(
                'Mark all as read',
                style: TextStyle(
                  color: Color(0xFFEC4899),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _loadNotifications,
        color: const Color(0xFFEC4899),
        child: _isLoading && _notifications.isEmpty
            ? const Center(
                child: CircularProgressIndicator(
                  color: Color(0xFFEC4899),
                ),
              )
            : _notifications.isEmpty
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.notifications_none,
                          size: 64,
                          color: Colors.grey[300],
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'No notifications yet',
                          style: TextStyle(
                            fontSize: 18,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  )
                : ListView.builder(
                    controller: _scrollController,
                    padding: const EdgeInsets.all(8),
                    itemCount: _notifications.length + (_hasMore ? 1 : 0),
                    itemBuilder: (context, index) {
                      if (index == _notifications.length) {
                        return const Center(
                          child: Padding(
                            padding: EdgeInsets.all(8.0),
                            child: CircularProgressIndicator(
                              color: Color(0xFFEC4899),
                            ),
                          ),
                        );
                      }

                      final notification = _notifications[index];
                      return _buildNotificationItem(notification);
                    },
                  ),
      ),
    );
  }

  Widget _buildNotificationItem(NotificationModel notification) {
    final formattedDate =
        DateFormat.yMMMd().add_jm().format(notification.createdAt);

    return InkWell(
      onTap: () => _markAsRead(notification),
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 4),
        decoration: BoxDecoration(
          color: notification.isRead ? Colors.white : const Color(0xFFFFF5F8),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: Colors.grey.shade200,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _getNotificationIcon(notification.type),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          notification.title,
                          style: TextStyle(
                            fontWeight: notification.isRead
                                ? FontWeight.normal
                                : FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          notification.body,
                          style: TextStyle(
                            color: Colors.grey[700],
                            fontSize: 14,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          formattedDate,
                          style: TextStyle(
                            color: Colors.grey[500],
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _getNotificationIcon(String type) {
    IconData iconData;
    Color iconColor;

    switch (type) {
      case 'appointment_reminder':
      case 'appointment_booked':
      case 'appointment_confirmed':
        iconData = Icons.calendar_today;
        iconColor = Colors.blue;
        break;
      case 'appointment_cancelled':
        iconData = Icons.event_busy;
        iconColor = Colors.red;
        break;
      case 'payment_success':
        iconData = Icons.payment;
        iconColor = Colors.green;
        break;
      case 'user_registration':
      case 'provider_registration':
        iconData = Icons.person;
        iconColor = const Color(0xFFEC4899);
        break;
      default:
        iconData = Icons.notifications;
        iconColor = Colors.orange;
        break;
    }

    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: iconColor.withAlpha(25),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Icon(
        iconData,
        color: iconColor,
        size: 24,
      ),
    );
  }
}
