<template>
    <AppLayout>
        <div class="p-6">
            <div class="max-w-7xl mx-auto">
                <h1 class="text-3xl font-bold text-gray-900 mb-8">System Verification Dashboard</h1>
                
                <!-- Email Testing Section -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
                    <div class="p-6">
                        <h2 class="text-xl font-semibold text-gray-900 mb-4">Email Configuration Test</h2>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Test Email Address</label>
                                <input 
                                    v-model="testEmail" 
                                    type="email" 
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    placeholder="Enter email to test"
                                >
                                <button 
                                    @click="testEmailConfiguration" 
                                    :disabled="emailTesting"
                                    class="mt-3 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
                                >
                                    {{ emailTesting ? 'Testing...' : 'Test Email Configuration' }}
                                </button>
                            </div>
                            <div v-if="emailTestResult" class="p-4 rounded-md" :class="emailTestResult.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'">
                                <h3 class="font-medium" :class="emailTestResult.success ? 'text-green-800' : 'text-red-800'">
                                    {{ emailTestResult.success ? 'Email Test Successful' : 'Email Test Failed' }}
                                </h3>
                                <p class="text-sm mt-1" :class="emailTestResult.success ? 'text-green-600' : 'text-red-600'">
                                    {{ emailTestResult.message }}
                                </p>
                                <div v-if="emailTestResult.config" class="mt-2 text-xs text-gray-600">
                                    <p>Mailer: {{ emailTestResult.config.mailer }}</p>
                                    <p>Host: {{ emailTestResult.config.host }}</p>
                                    <p>Port: {{ emailTestResult.config.port }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Transaction System Verification -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
                    <div class="p-6">
                        <h2 class="text-xl font-semibold text-gray-900 mb-4">Transaction System Verification</h2>
                        <button 
                            @click="verifyTransactionSystem" 
                            :disabled="transactionTesting"
                            class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 mb-4"
                        >
                            {{ transactionTesting ? 'Verifying...' : 'Verify Transaction System' }}
                        </button>
                        
                        <div v-if="transactionResults" class="space-y-4">
                            <div v-for="(result, key) in transactionResults.verification_results" :key="key" 
                                 class="p-4 rounded-md border" 
                                 :class="getResultClass(result.status)">
                                <h3 class="font-medium capitalize">{{ key.replace('_', ' ') }}</h3>
                                <p class="text-sm mt-1">Status: {{ result.status }}</p>
                                <div v-if="result.total_payments !== undefined" class="text-xs mt-2">
                                    <p>Total Payments: {{ result.total_payments }}</p>
                                    <p>Recent Payments: {{ result.recent_payments }}</p>
                                </div>
                                <div v-if="result.total_transactions !== undefined" class="text-xs mt-2">
                                    <p>Total Transactions: {{ result.total_transactions }}</p>
                                    <p>Recent Transactions: {{ result.recent_transactions }}</p>
                                </div>
                            </div>
                            
                            <div class="mt-4 p-4 bg-gray-50 rounded-md">
                                <h3 class="font-medium text-gray-900">Summary</h3>
                                <p class="text-sm text-gray-600 mt-1">Overall Status: {{ transactionResults.summary.overall_status }}</p>
                                <div v-if="transactionResults.summary.working_features.length" class="mt-2">
                                    <p class="text-sm text-green-600">Working Features: {{ transactionResults.summary.working_features.join(', ') }}</p>
                                </div>
                                <div v-if="transactionResults.summary.issues_found.length" class="mt-2">
                                    <p class="text-sm text-red-600">Issues Found: {{ transactionResults.summary.issues_found.join(', ') }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Anonymous Chat Verification -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
                    <div class="p-6">
                        <h2 class="text-xl font-semibold text-gray-900 mb-4">Anonymous Chat Mapping Verification</h2>
                        <button 
                            @click="verifyAnonymousChatMapping" 
                            :disabled="chatTesting"
                            class="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:opacity-50 mb-4"
                        >
                            {{ chatTesting ? 'Verifying...' : 'Verify Anonymous Chat Mapping' }}
                        </button>
                        
                        <div v-if="chatResults" class="space-y-4">
                            <div v-for="(result, key) in chatResults.verification_results" :key="key" 
                                 class="p-4 rounded-md border" 
                                 :class="getResultClass(result.status)">
                                <h3 class="font-medium capitalize">{{ key.replace('_', ' ') }}</h3>
                                <p class="text-sm mt-1">Status: {{ result.status }}</p>
                                <div v-if="result.anonymous_chats_count !== undefined" class="text-xs mt-2">
                                    <p>Anonymous Chats: {{ result.anonymous_chats_count }}</p>
                                    <p>Total Chats: {{ result.total_chats_count }}</p>
                                    <p>Anonymous Percentage: {{ result.anonymous_chat_percentage }}%</p>
                                </div>
                                <div v-if="result.transferred_chats_count !== undefined" class="text-xs mt-2">
                                    <p>Transferred Chats: {{ result.transferred_chats_count }}</p>
                                    <p>Transfer Method Exists: {{ result.transfer_method_exists ? 'Yes' : 'No' }}</p>
                                </div>
                            </div>
                            
                            <div class="mt-4 p-4 bg-gray-50 rounded-md">
                                <h3 class="font-medium text-gray-900">Summary</h3>
                                <p class="text-sm text-gray-600 mt-1">Overall Status: {{ chatResults.summary.overall_status }}</p>
                                <div v-if="chatResults.summary.working_features.length" class="mt-2">
                                    <p class="text-sm text-green-600">Working Features: {{ chatResults.summary.working_features.join(', ') }}</p>
                                </div>
                                <div v-if="chatResults.summary.issues_found.length" class="mt-2">
                                    <p class="text-sm text-red-600">Issues Found: {{ chatResults.summary.issues_found.join(', ') }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- System Status Overview -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                    <div class="p-6">
                        <h2 class="text-xl font-semibold text-gray-900 mb-4">System Status Overview</h2>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div class="p-4 rounded-md" :class="getOverallStatusClass('email')">
                                <h3 class="font-medium">Email System</h3>
                                <p class="text-sm mt-1">{{ getOverallStatus('email') }}</p>
                            </div>
                            <div class="p-4 rounded-md" :class="getOverallStatusClass('transactions')">
                                <h3 class="font-medium">Transaction System</h3>
                                <p class="text-sm mt-1">{{ getOverallStatus('transactions') }}</p>
                            </div>
                            <div class="p-4 rounded-md" :class="getOverallStatusClass('chat')">
                                <h3 class="font-medium">Chat System</h3>
                                <p class="text-sm mt-1">{{ getOverallStatus('chat') }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>

<script setup>
import { ref, computed } from 'vue'
import AppLayout from '@/layouts/AppLayout.vue'
import axios from 'axios'

// Reactive data
const testEmail = ref('')
const emailTesting = ref(false)
const emailTestResult = ref(null)
const transactionTesting = ref(false)
const transactionResults = ref(null)
const chatTesting = ref(false)
const chatResults = ref(null)

// Methods
const testEmailConfiguration = async () => {
    if (!testEmail.value) {
        alert('Please enter an email address')
        return
    }
    
    emailTesting.value = true
    emailTestResult.value = null
    
    try {
        const response = await axios.post('/test-email-config', {
            email: testEmail.value
        })
        emailTestResult.value = response.data
    } catch (error) {
        emailTestResult.value = {
            success: false,
            message: error.response?.data?.message || 'Email test failed'
        }
    } finally {
        emailTesting.value = false
    }
}

const verifyTransactionSystem = async () => {
    transactionTesting.value = true
    transactionResults.value = null
    
    try {
        const response = await axios.post('/verify-transaction-system')
        transactionResults.value = response.data
    } catch (error) {
        console.error('Transaction verification failed:', error)
    } finally {
        transactionTesting.value = false
    }
}

const verifyAnonymousChatMapping = async () => {
    chatTesting.value = true
    chatResults.value = null
    
    try {
        const response = await axios.post('/verify-anonymous-chat-mapping')
        chatResults.value = response.data
    } catch (error) {
        console.error('Chat verification failed:', error)
    } finally {
        chatTesting.value = false
    }
}

const getResultClass = (status) => {
    switch (status) {
        case 'working':
            return 'bg-green-50 border-green-200'
        case 'no_data':
            return 'bg-yellow-50 border-yellow-200'
        default:
            return 'bg-red-50 border-red-200'
    }
}

const getOverallStatus = (system) => {
    switch (system) {
        case 'email':
            return emailTestResult.value ? (emailTestResult.value.success ? 'Working' : 'Issues Found') : 'Not Tested'
        case 'transactions':
            return transactionResults.value ? transactionResults.value.summary.overall_status : 'Not Tested'
        case 'chat':
            return chatResults.value ? chatResults.value.summary.overall_status : 'Not Tested'
        default:
            return 'Unknown'
    }
}

const getOverallStatusClass = (system) => {
    const status = getOverallStatus(system)
    switch (status) {
        case 'Working':
        case 'all_working':
            return 'bg-green-50 border border-green-200'
        case 'Issues Found':
        case 'issues_found':
            return 'bg-red-50 border border-red-200'
        default:
            return 'bg-gray-50 border border-gray-200'
    }
}
</script>
