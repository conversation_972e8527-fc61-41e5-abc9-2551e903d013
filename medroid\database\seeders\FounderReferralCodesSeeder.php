<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\FounderReferralCode;

class FounderReferralCodesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $codes = [
            [
                'code' => 'FOUNDER2025',
                'description' => 'Medroid Founders Club - Early Access Members',
                'is_active' => true,
                'max_uses' => 100,
                'current_uses' => 0,
            ],
            [
                'code' => 'MEDROID100',
                'description' => 'Medroid Founders Club - First 100 Members',
                'is_active' => true,
                'max_uses' => 100,
                'current_uses' => 0,
            ],
            [
                'code' => 'HEALTHCARE',
                'description' => 'Medroid Founders Club - Healthcare Professionals',
                'is_active' => true,
                'max_uses' => 200,
                'current_uses' => 0,
            ],
            [
                'code' => 'MEDTEAM',
                'description' => 'Medroid Founders Club - Medical Team Members',
                'is_active' => true,
                'max_uses' => 50,
                'current_uses' => 0,
            ],
            [
                'code' => 'BETA2025',
                'description' => 'Medroid Founders Club - Beta Testers',
                'is_active' => true,
                'max_uses' => 150,
                'current_uses' => 0,
            ],
        ];

        foreach ($codes as $codeData) {
            FounderReferralCode::updateOrCreate(
                ['code' => $codeData['code']],
                $codeData
            );
        }

        $this->command->info('Founder referral codes seeded successfully!');
    }
}
