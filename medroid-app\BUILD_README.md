# Medroid Mobile App Build System

## 🚀 Mobile App Build Guide

This project is a Flutter mobile application for Android and iOS platforms.

### Build Commands

#### Android APK:
```bash
flutter build apk --release
```

#### Android App Bundle (for Play Store):
```bash
flutter build appbundle --release
```

#### iOS (requires macOS):
```bash
flutter build ios --release
```

### Development Setup

1. **Install Flutter SDK**
2. **Install dependencies**:
   ```bash
   flutter pub get
   ```
3. **Run in development**:
   ```bash
   flutter run
   ```

### Build Scripts

- `build_apk.sh` - Build Android APK with dependency fixes
- `build_minimal_apk.sh` - Build minimal APK for testing
- `fix_android_build.sh` - Fix Android build issues

### API Configuration

The app connects to the Laravel backend API:
- **Development**: `http://localhost:8000/api`
- **Production**: `https://backend.medroid.ai/api`

### Version Management

Version information is managed in `pubspec.yaml` and displayed in the app for tracking deployments.
