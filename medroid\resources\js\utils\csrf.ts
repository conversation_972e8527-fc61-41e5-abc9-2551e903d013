/**
 * CSRF Token Utilities
 * 
 * This module provides utilities for handling CSRF tokens consistently
 * across the application. The global axios interceptors should handle
 * most cases automatically, but these utilities are available for
 * special cases or non-axios requests.
 */

/**
 * Get the current CSRF token from the meta tag
 */
export const getCsrfToken = (): string | null => {
    const csrfToken = document.head.querySelector('meta[name="csrf-token"]') as HTMLMetaElement;
    return csrfToken ? csrfToken.content : null;
};

/**
 * Refresh the CSRF token by calling the sanctum endpoint
 */
export const refreshCsrfToken = async (): Promise<string | null> => {
    try {
        const response = await fetch('/sanctum/csrf-cookie', {
            method: 'GET',
            credentials: 'same-origin',
            headers: {
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
            }
        });
        
        if (response.ok) {
            // Wait a bit for the cookie to be set and meta tag to be updated
            await new Promise(resolve => setTimeout(resolve, 200));
            
            // Check if meta tag was updated, if not try to reload
            let newToken = getCsrfToken();
            if (!newToken) {
                // Wait a bit longer and try again
                await new Promise(resolve => setTimeout(resolve, 300));
                newToken = getCsrfToken();
            }
            
            return newToken;
        } else {
            console.error('CSRF refresh request failed:', response.status, response.statusText);
        }
    } catch (error) {
        console.error('Failed to refresh CSRF token:', error);
    }
    return null;
};

/**
 * Get headers with CSRF token for fetch requests
 * Use this for non-axios requests that need CSRF protection
 */
export const getCsrfHeaders = (): Record<string, string> => {
    const token = getCsrfToken();
    const headers: Record<string, string> = {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest',
    };
    
    if (token) {
        headers['X-CSRF-TOKEN'] = token;
    }
    
    return headers;
};

/**
 * Make a fetch request with automatic CSRF token handling
 * This is a wrapper around fetch that automatically includes CSRF tokens
 */
export const csrfFetch = async (url: string, options: RequestInit = {}): Promise<Response> => {
    const headers = {
        ...getCsrfHeaders(),
        ...options.headers,
    };
    
    const config: RequestInit = {
        ...options,
        headers,
        credentials: 'same-origin',
    };
    
    try {
        let response = await fetch(url, config);
        
        // If we get a 419 (CSRF token mismatch), try to refresh and retry once
        if (response.status === 419 && !options.headers?.['X-Retry']) {
            console.warn('CSRF token mismatch, refreshing token and retrying...');
            const newToken = await refreshCsrfToken();
            
            if (newToken) {
                const retryHeaders = {
                    ...headers,
                    'X-CSRF-TOKEN': newToken,
                    'X-Retry': 'true', // Prevent infinite retry loops
                };
                
                const retryConfig = {
                    ...config,
                    headers: retryHeaders,
                };
                
                // Wait a moment before retry
                await new Promise(resolve => setTimeout(resolve, 100));
                response = await fetch(url, retryConfig);
            }
        }
        
        // If still getting 500 errors, add retry logic for server errors (but not for logout)
        if (response.status === 500 && !options.headers?.['X-Server-Retry'] && !url.includes('/logout')) {
            console.warn('Server error encountered, retrying once...');
            
            const retryHeaders = {
                ...headers,
                'X-Server-Retry': 'true',
            };
            
            // Wait before retry
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            response = await fetch(url, {
                ...config,
                headers: retryHeaders,
            });
        }
        
        // For logout requests, always treat as successful even if there are errors
        if (url.includes('/logout') && (response.status >= 400)) {
            console.log('Logout request completed (may have already been logged out)');
            // Return a mock successful response
            return new Response(JSON.stringify({ message: 'Logged out successfully' }), {
                status: 200,
                headers: { 'Content-Type': 'application/json' }
            });
        }
        
        return response;
    } catch (error) {
        console.error('CSRF fetch error:', error);
        throw error;
    }
};

/**
 * Check if the current CSRF token is valid
 */
export const validateCsrfToken = async (): Promise<boolean> => {
    try {
        const response = await csrfFetch('/api/user', { method: 'GET' });
        return response.ok;
    } catch (error) {
        return false;
    }
};
