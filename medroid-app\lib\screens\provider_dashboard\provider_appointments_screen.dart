import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:medroid_app/models/appointment.dart';
import 'package:medroid_app/screens/provider_dashboard/patient_details_screen.dart';
import 'package:medroid_app/screens/reschedule_appointment_screen.dart';
import 'package:medroid_app/screens/main_video_consultation_screen.dart';
import 'package:medroid_app/services/api_service.dart';
import 'package:medroid_app/utils/constants.dart';
import 'package:medroid_app/utils/theme.dart';
import 'package:medroid_app/utils/responsive_utils.dart';

class ProviderAppointmentsScreen extends StatefulWidget {
  const ProviderAppointmentsScreen({Key? key}) : super(key: key);

  @override
  _ProviderAppointmentsScreenState createState() =>
      _ProviderAppointmentsScreenState();
}

class _ProviderAppointmentsScreenState
    extends State<ProviderAppointmentsScreen> {
  bool _isLoading = true;
  List<Appointment> _appointments = [];
  String _filter = 'upcoming'; // 'upcoming', 'past', 'all', 'telemedicine'

  @override
  void initState() {
    super.initState();
    _loadAppointments();
  }

  Future<void> _loadAppointments() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final apiService = RepositoryProvider.of<ApiService>(context);
      final response = await apiService.get(Constants.userAppointmentsEndpoint);

      if (response != null) {
        final List<Appointment> appointments = [];
        for (final appointmentData in response) {
          appointments.add(Appointment.fromJson(appointmentData));
        }

        setState(() {
          _appointments = appointments;
          _isLoading = false;
        });
      } else {
        setState(() {
          _appointments = [];
          _isLoading = false;
        });
      }
    } catch (e) {
      print('Error loading appointments: $e');
      setState(() {
        _isLoading = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error loading appointments: $e'),
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  List<Appointment> get _filteredAppointments {
    final now = DateTime.now();

    switch (_filter) {
      case 'upcoming':
        return _appointments
            .where((appointment) =>
                appointment.date.isAfter(now) ||
                (appointment.date.day == now.day &&
                    appointment.date.month == now.month &&
                    appointment.date.year == now.year))
            .toList();
      case 'past':
        return _appointments
            .where((appointment) =>
                appointment.date.isBefore(now) &&
                !(appointment.date.day == now.day &&
                    appointment.date.month == now.month &&
                    appointment.date.year == now.year))
            .toList();
      case 'telemedicine':
        return _appointments
            .where((appointment) => appointment.isTelemedicine)
            .toList();
      case 'all':
      default:
        return _appointments;
    }
  }

  void _joinVideoConsultation(Appointment appointment) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => MainVideoConsultationScreen(
          appointmentId: appointment.id.toString(),
        ),
      ),
    );
  }

  void _viewPatientDetails(Appointment appointment) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PatientDetailsScreen(
          appointment: appointment,
        ),
      ),
    );
  }

  Future<void> _rescheduleAppointment(Appointment appointment) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => RescheduleAppointmentScreen(
          appointment: appointment,
        ),
      ),
    );

    if (result == true) {
      // Refresh appointments if rescheduled successfully
      _loadAppointments();
    }
  }

  Future<void> _cancelAppointment(Appointment appointment) async {
    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Cancel Appointment'),
        content:
            const Text('Are you sure you want to cancel this appointment?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('No'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Yes'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    try {
      final apiService = RepositoryProvider.of<ApiService>(context);

      // First try to update status to cancelled
      await apiService.updateAppointment(
        appointmentId: appointment.id.toString(),
        status: 'cancelled',
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Appointment cancelled successfully'),
            behavior: SnackBarBehavior.floating,
          ),
        );

        // Refresh appointments
        _loadAppointments();
      }
    } catch (e) {
      debugPrint('Error cancelling appointment: $e');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error cancelling appointment: $e'),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  Future<void> _deleteAppointment(Appointment appointment) async {
    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Appointment'),
        content: const Text(
          'Are you sure you want to delete this appointment? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('No'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Yes'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    try {
      final apiService = RepositoryProvider.of<ApiService>(context);

      // Delete the appointment
      final success = await apiService.deleteAppointment(
        appointment.id.toString(),
      );

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Appointment deleted successfully'),
            behavior: SnackBarBehavior.floating,
          ),
        );

        // Refresh appointments
        _loadAppointments();
      } else if (!success) {
        throw Exception('Failed to delete appointment');
      }
    } catch (e) {
      debugPrint('Error deleting appointment: $e');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error deleting appointment: $e'),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDesktop = ResponsiveUtils.isDesktop(context);
    final isTablet = ResponsiveUtils.isTablet(context);

    // For desktop and tablet, we don't show the AppBar since it's handled by the parent
    if (isDesktop || isTablet) {
      return _buildResponsiveContent(context, isDesktop);
    }

    // Mobile layout with AppBar
    return Scaffold(
      appBar: AppBar(
        title: const Text('My Appointments'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadAppointments,
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: Column(
        children: [
          _buildFilterChips(),
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _filteredAppointments.isEmpty
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Icon(
                              Icons.calendar_today,
                              size: 64,
                              color: Colors.grey,
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'No ${_filter == "all" ? "" : _filter} appointments found',
                              style: const TextStyle(
                                fontSize: 16,
                                color: Colors.grey,
                              ),
                            ),
                          ],
                        ),
                      )
                    : RefreshIndicator(
                        onRefresh: _loadAppointments,
                        child: ListView.builder(
                          itemCount: _filteredAppointments.length,
                          itemBuilder: (context, index) {
                            final appointment = _filteredAppointments[index];
                            return _buildAppointmentCard(appointment);
                          },
                        ),
                      ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChips() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: [
            const SizedBox(width: 16),
            _buildFilterChip('Upcoming', 'upcoming'),
            const SizedBox(width: 8),
            _buildFilterChip('Past', 'past'),
            const SizedBox(width: 8),
            _buildFilterChip('Telemedicine', 'telemedicine'),
            const SizedBox(width: 8),
            _buildFilterChip('All', 'all'),
            const SizedBox(width: 16),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterChip(String label, String filterValue) {
    final isSelected = _filter == filterValue;

    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          _filter = filterValue;
        });
      },
      backgroundColor: Colors.grey[200],
      selectedColor: AppTheme.primaryColor.withOpacity(0.2),
      checkmarkColor: AppTheme.primaryColor,
      labelStyle: TextStyle(
        color: isSelected ? AppTheme.primaryColor : Colors.black87,
        fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
      ),
    );
  }

  Widget _buildAppointmentCard(Appointment appointment) {
    final dateFormat = DateFormat('EEEE, MMMM d, y');
    final formattedDate = dateFormat.format(appointment.date);

    // Determine if this is a telemedicine appointment that can be joined
    final bool canJoinVideo = appointment.canJoinVideoConsultation;

    // Parse the status color
    final Color statusColor = Color(int.parse(
      appointment.statusColor.replaceFirst('#', '0xFF'),
    ));

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: statusColor.withAlpha(128), // 0.5 opacity = 128/255
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Status bar
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: statusColor.withAlpha(26), // 0.1 opacity = 26/255
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      appointment.status.toUpperCase(),
                      style: TextStyle(
                        color: statusColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    if (canJoinVideo)
                      ElevatedButton.icon(
                        icon: const Icon(Icons.videocam),
                        label: const Text('Start Consultation'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(horizontal: 12),
                        ),
                        onPressed: () => _joinVideoConsultation(appointment),
                      ),
                  ],
                ),

                // Action buttons
                Padding(
                  padding: const EdgeInsets.only(top: 8),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      // View patient details button
                      TextButton.icon(
                        icon: const Icon(Icons.person, size: 16),
                        label: const Text('Patient Details'),
                        onPressed: () => _viewPatientDetails(appointment),
                        style: TextButton.styleFrom(
                          foregroundColor: Colors.blue,
                        ),
                      ),
                      const SizedBox(width: 8),

                      // Only show reschedule and cancel for upcoming appointments
                      if (appointment.status != 'cancelled' &&
                          (appointment.date.isAfter(DateTime.now()) ||
                              (appointment.date.day == DateTime.now().day &&
                                  appointment.date.month ==
                                      DateTime.now().month &&
                                  appointment.date.year ==
                                      DateTime.now().year))) ...[
                        // Reschedule button
                        TextButton.icon(
                          icon: const Icon(Icons.calendar_today, size: 16),
                          label: const Text('Reschedule'),
                          onPressed: () => _rescheduleAppointment(appointment),
                          style: TextButton.styleFrom(
                            foregroundColor: Colors.orange,
                          ),
                        ),
                        const SizedBox(width: 8),
                        // Cancel button
                        TextButton.icon(
                          icon: const Icon(Icons.cancel_outlined, size: 16),
                          label: const Text('Cancel'),
                          onPressed: () => _cancelAppointment(appointment),
                          style: TextButton.styleFrom(
                            foregroundColor: Colors.red,
                          ),
                        ),
                      ],

                      // Show delete button for cancelled or past appointments
                      if (appointment.status == 'cancelled' ||
                          (appointment.date.isBefore(DateTime.now()) &&
                              !(appointment.date.day == DateTime.now().day &&
                                  appointment.date.month ==
                                      DateTime.now().month &&
                                  appointment.date.year ==
                                      DateTime.now().year)))
                        TextButton.icon(
                          icon: const Icon(Icons.delete_outline, size: 16),
                          label: const Text('Delete'),
                          onPressed: () => _deleteAppointment(appointment),
                          style: TextButton.styleFrom(
                            foregroundColor: Colors.red,
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Appointment details
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Icon(Icons.calendar_today,
                        size: 16, color: Colors.grey),
                    const SizedBox(width: 8),
                    Text(
                      formattedDate,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    const Icon(Icons.access_time, size: 16, color: Colors.grey),
                    const SizedBox(width: 8),
                    Text(
                      '${appointment.timeSlot['start_time']} - ${appointment.timeSlot['end_time']}',
                      style: const TextStyle(fontSize: 14),
                    ),
                    if (appointment.isTelemedicine)
                      Container(
                        margin: const EdgeInsets.only(left: 8),
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 2),
                        decoration: BoxDecoration(
                          color:
                              Colors.blue.withAlpha(26), // 0.1 opacity = 26/255
                          borderRadius: BorderRadius.circular(4),
                          border: Border.all(
                              color: Colors.blue
                                  .withAlpha(128)), // 0.5 opacity = 128/255
                        ),
                        child: const Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(Icons.videocam, size: 12, color: Colors.blue),
                            SizedBox(width: 4),
                            Text(
                              'Telemedicine',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.blue,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    const Icon(Icons.person, size: 16, color: Colors.grey),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'Patient: ${appointment.patientName}',
                        style: const TextStyle(fontSize: 14),
                      ),
                    ),
                  ],
                ),
                if (appointment.serviceName != null) ...[
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      const Icon(Icons.medical_services,
                          size: 16, color: Colors.grey),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'Service: ${appointment.serviceName}',
                          style: const TextStyle(fontSize: 14),
                        ),
                      ),
                    ],
                  ),
                ],
                const SizedBox(height: 8),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Icon(Icons.note, size: 16, color: Colors.grey),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'Reason: ${appointment.reason}',
                        style: const TextStyle(fontSize: 14),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildResponsiveContent(BuildContext context, bool isDesktop) {
    final theme = Theme.of(context);

    return Container(
      padding: EdgeInsets.all(isDesktop ? 24.0 : 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with title and refresh button
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'My Appointments',
                style: theme.textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              ElevatedButton.icon(
                onPressed: _loadAppointments,
                icon: const Icon(Icons.refresh, size: 18),
                label: const Text('Refresh'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: theme.colorScheme.primary,
                  foregroundColor: Colors.white,
                  elevation: 2,
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),

          // Filter chips
          _buildFilterChips(),
          const SizedBox(height: 16),

          // Appointments content
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _filteredAppointments.isEmpty
                    ? _buildEmptyState(theme)
                    : _buildAppointmentsList(isDesktop),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(ThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withAlpha(25),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.calendar_today,
              size: 64,
              color: theme.colorScheme.primary,
            ),
          ),
          const SizedBox(height: 32),
          Text(
            'No ${_filter == "all" ? "" : _filter} appointments found',
            style: theme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'Your appointments will appear here once patients book with you.',
            textAlign: TextAlign.center,
            style: theme.textTheme.bodyLarge?.copyWith(
              color: Colors.grey.shade600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAppointmentsList(bool isDesktop) {
    if (isDesktop) {
      // Grid layout for desktop
      return GridView.builder(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          childAspectRatio: 1.8,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
        ),
        itemCount: _filteredAppointments.length,
        itemBuilder: (context, index) {
          final appointment = _filteredAppointments[index];
          return _buildCompactAppointmentCard(appointment);
        },
      );
    } else {
      // List layout for tablet
      return RefreshIndicator(
        onRefresh: _loadAppointments,
        child: ListView.builder(
          itemCount: _filteredAppointments.length,
          itemBuilder: (context, index) {
            final appointment = _filteredAppointments[index];
            return _buildAppointmentCard(appointment);
          },
        ),
      );
    }
  }

  Widget _buildCompactAppointmentCard(Appointment appointment) {
    final dateFormat = DateFormat('MMM d, y');
    final formattedDate = dateFormat.format(appointment.date);
    final theme = Theme.of(context);

    // Parse the status color
    final Color statusColor = Color(int.parse(
      appointment.statusColor.replaceFirst('#', '0xFF'),
    ));

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: statusColor.withAlpha(128),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Status header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: statusColor.withAlpha(26),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  appointment.status.toUpperCase(),
                  style: TextStyle(
                    color: statusColor,
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
                if (appointment.canJoinVideoConsultation)
                  ElevatedButton(
                    onPressed: () => _joinVideoConsultation(appointment),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      minimumSize: Size.zero,
                    ),
                    child: const Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(Icons.videocam, size: 14),
                        SizedBox(width: 4),
                        Text('Join', style: TextStyle(fontSize: 12)),
                      ],
                    ),
                  ),
              ],
            ),
          ),

          // Content
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    appointment.patientName,
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Icon(Icons.calendar_today,
                          size: 14, color: Colors.grey.shade600),
                      const SizedBox(width: 4),
                      Text(
                        formattedDate,
                        style: theme.textTheme.bodySmall,
                      ),
                      if (appointment.isTelemedicine) ...[
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 4, vertical: 2),
                          decoration: BoxDecoration(
                            color: Colors.blue.withAlpha(25),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: const Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(Icons.videocam,
                                  size: 10, color: Colors.blue),
                              SizedBox(width: 2),
                              Text(
                                'Online',
                                style: TextStyle(
                                  fontSize: 10,
                                  color: Colors.blue,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ],
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Icon(Icons.access_time,
                          size: 14, color: Colors.grey.shade600),
                      const SizedBox(width: 4),
                      Text(
                        '${appointment.timeSlot['start_time']} - ${appointment.timeSlot['end_time']}',
                        style: theme.textTheme.bodySmall,
                      ),
                    ],
                  ),
                  const Spacer(),

                  // Action buttons
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      PopupMenuButton<String>(
                        onSelected: (value) {
                          switch (value) {
                            case 'details':
                              _viewPatientDetails(appointment);
                              break;
                            case 'reschedule':
                              _rescheduleAppointment(appointment);
                              break;
                            case 'cancel':
                              _cancelAppointment(appointment);
                              break;
                            case 'delete':
                              _deleteAppointment(appointment);
                              break;
                          }
                        },
                        itemBuilder: (context) => [
                          const PopupMenuItem(
                            value: 'details',
                            child: Row(
                              children: [
                                Icon(Icons.person, size: 16),
                                SizedBox(width: 8),
                                Text('Patient Details'),
                              ],
                            ),
                          ),
                          if (appointment.status != 'cancelled' &&
                              appointment.date.isAfter(DateTime.now()))
                            const PopupMenuItem(
                              value: 'reschedule',
                              child: Row(
                                children: [
                                  Icon(Icons.calendar_today, size: 16),
                                  SizedBox(width: 8),
                                  Text('Reschedule'),
                                ],
                              ),
                            ),
                          if (appointment.status != 'cancelled' &&
                              appointment.date.isAfter(DateTime.now()))
                            const PopupMenuItem(
                              value: 'cancel',
                              child: Row(
                                children: [
                                  Icon(Icons.cancel_outlined,
                                      size: 16, color: Colors.red),
                                  SizedBox(width: 8),
                                  Text('Cancel',
                                      style: TextStyle(color: Colors.red)),
                                ],
                              ),
                            ),
                          if (appointment.status == 'cancelled' ||
                              appointment.date.isBefore(DateTime.now()))
                            const PopupMenuItem(
                              value: 'delete',
                              child: Row(
                                children: [
                                  Icon(Icons.delete_outline,
                                      size: 16, color: Colors.red),
                                  SizedBox(width: 8),
                                  Text('Delete',
                                      style: TextStyle(color: Colors.red)),
                                ],
                              ),
                            ),
                        ],
                        child: const Icon(Icons.more_vert, size: 20),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
