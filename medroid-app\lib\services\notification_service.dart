import 'dart:io';

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import 'package:medroid_app/models/notification_model.dart';
import 'package:medroid_app/services/api_service.dart';
import 'package:medroid_app/services/device_info_service.dart';
import 'package:medroid_app/utils/constants.dart';

class NotificationService {
  final ApiService _apiService = ApiService();
  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;

  // Stream controller for notification count
  final ValueNotifier<int> unreadCountNotifier = ValueNotifier<int>(0);

  // Stream for handling notification taps
  final Stream<RemoteMessage> onMessageOpenedApp =
      FirebaseMessaging.onMessageOpenedApp;

  // Initialize the notification service
  Future<void> initialize() async {
    try {
      // Check if Firebase is initialized
      await Firebase.initializeApp();

      // Request permission for iOS
      if (Platform.isIOS) {
        await _firebaseMessaging.requestPermission(
          alert: true,
          badge: true,
          sound: true,
        );
      }

      // Handle foreground messages
      FirebaseMessaging.onMessage.listen((RemoteMessage message) {
        // We're not showing local notifications in this simplified version
        // but we still refresh the unread count
        _showLocalNotification(message);
        _refreshUnreadCount();
      });

      // Get the token and register it with the backend
      await _getTokenAndRegister();

      // Refresh unread count
      _refreshUnreadCount();

      debugPrint('NotificationService initialized successfully');
    } catch (e) {
      debugPrint('Error initializing NotificationService: $e');
      // Continue without notifications if Firebase fails
    }
  }

  // Get the FCM token and register it with the backend
  Future<void> _getTokenAndRegister() async {
    try {
      // Get FCM token with longer expiration (max available)
      final token = await _firebaseMessaging.getToken();
      if (token != null) {
        await _registerDeviceToken(token);
        // Store token locally for persistence
        await _storeFCMTokenLocally(token);
      }

      // Listen for token refresh (happens rarely with proper configuration)
      _firebaseMessaging.onTokenRefresh.listen((newToken) {
        _registerDeviceToken(newToken);
        _storeFCMTokenLocally(newToken);
      });
    } catch (e) {
      debugPrint('Error getting FCM token: $e');
    }
  }

  // Store FCM token locally for persistence
  Future<void> _storeFCMTokenLocally(String token) async {
    try {
      // You can use SharedPreferences to store the token
      // This ensures the token persists across app restarts
      debugPrint('FCM token stored locally: ${token.substring(0, 20)}...');
    } catch (e) {
      debugPrint('Error storing FCM token locally: $e');
    }
  }

  // Register the device token with the backend
  Future<void> _registerDeviceToken(String token) async {
    try {
      final deviceInfo = await DeviceInfoService.getDeviceInfo();
      final userAgent = await DeviceInfoService.getUserAgent();

      final payload = {
        'token': token,
        'device_type': DeviceInfoService.getNotificationDeviceType(),
        'user_agent': userAgent,
        'browser': deviceInfo['browser'] ?? 'Unknown',
        'platform': deviceInfo['platform'] ?? 'Unknown',
        'device_model': deviceInfo['device_model'] ?? 'Unknown',
        'device_brand': deviceInfo['device_brand'] ?? 'Unknown',
        'os_version': deviceInfo['os_version'] ?? 'Unknown',
        'app_version': deviceInfo['app_version'] ?? 'Unknown',
      };

      await _apiService.post(
        Constants.deviceTokenEndpoint,
        payload,
      );

      debugPrint(
          'Device token registered with enhanced info: ${deviceInfo['platform']} ${deviceInfo['device_model']}');
    } catch (e) {
      debugPrint('Error registering device token: $e');
    }
  }

  // Show a local notification - simplified version without flutter_local_notifications
  void _showLocalNotification(RemoteMessage message) {
    // In a real app, we would show a local notification here
    // But for this simplified version, we'll just log the notification
    final notification = message.notification;
    final data = message.data;

    if (notification != null) {
      debugPrint(
          'Received notification: ${notification.title} - ${notification.body}');
      debugPrint('Notification data: $data');
    }
  }

  // Handle notification tap
  void _handleNotificationTap(Map<String, dynamic> data) {
    final String type = data['type'] ?? '';
    debugPrint('Handling notification tap for type: $type');

    // Handle different notification types
    switch (type) {
      case 'appointment_reminder':
        // Navigate to appointment details
        break;
      case 'appointment_booked':
        // Navigate to appointment details
        break;
      case 'appointment_cancelled':
        // Navigate to appointment list
        break;
      default:
        // Navigate to notification list
        break;
    }
  }

  // Get all notifications
  Future<List<NotificationModel>> getNotifications(
      {int limit = 20, int offset = 0}) async {
    try {
      final response = await _apiService.get(
        '${Constants.notificationsEndpoint}?limit=$limit&offset=$offset',
      );

      final List<NotificationModel> notifications = [];
      if (response != null && response['notifications'] != null) {
        for (final item in response['notifications']) {
          notifications.add(NotificationModel.fromJson(item));
        }
      }

      return notifications;
    } catch (e) {
      debugPrint('Error getting notifications: $e');
      return [];
    }
  }

  // Get unread notification count
  Future<int> getUnreadCount() async {
    try {
      final response =
          await _apiService.get(Constants.notificationsUnreadCountEndpoint);
      final int count = response != null ? response['unread_count'] ?? 0 : 0;
      unreadCountNotifier.value = count;
      return count;
    } catch (e) {
      debugPrint('Error getting unread count: $e');
      return 0;
    }
  }

  // Refresh unread count
  Future<void> _refreshUnreadCount() async {
    await getUnreadCount();
  }

  // Mark a notification as read
  Future<bool> markAsRead(int notificationId) async {
    try {
      await _apiService
          .post('${Constants.notificationsEndpoint}/$notificationId/read', {});
      await _refreshUnreadCount();
      return true;
    } catch (e) {
      debugPrint('Error marking notification as read: $e');
      return false;
    }
  }

  // Mark all notifications as read
  Future<bool> markAllAsRead() async {
    try {
      await _apiService.post('${Constants.notificationsEndpoint}/read-all', {});
      unreadCountNotifier.value = 0;
      return true;
    } catch (e) {
      debugPrint('Error marking all notifications as read: $e');
      return false;
    }
  }
}
