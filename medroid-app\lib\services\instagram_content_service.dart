import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:medroid_app/models/social_post.dart';
import 'package:medroid_app/utils/health_content_filter.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Instagram content service for fetching health-related content
class InstagramContentService {
  static const String apiBaseUrl = 'https://graph.instagram.com';

  final HealthContentFilter _healthFilter = HealthContentFilter();

  /// Check if user is authenticated with Instagram
  Future<bool> isAuthenticated() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('instagram_access_token');
      return token != null && token.isNotEmpty;
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Instagram auth check error: $e');
      }
      return false;
    }
  }

  /// Get stored access token
  Future<String?> getAccessToken() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString('instagram_access_token');
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error getting Instagram access token: $e');
      }
      return null;
    }
  }

  /// Fetch user's media from Instagram
  Future<List<SocialPost>> getUserMedia() async {
    final accessToken = await getAccessToken();
    if (accessToken == null) {
      if (kDebugMode) {
        debugPrint('Instagram: No access token available');
      }
      return [];
    }

    try {
      // First get the user's media IDs
      final mediaResponse = await http.get(
        Uri.parse(
            '$apiBaseUrl/me/media?fields=id,caption&access_token=$accessToken'),
      );

      if (mediaResponse.statusCode != 200) {
        if (kDebugMode) {
          debugPrint(
              'Failed to fetch Instagram media: ${mediaResponse.statusCode}');
        }
        return [];
      }

      final mediaData = json.decode(mediaResponse.body);
      final mediaItems = mediaData['data'] as List;

      // Process each media item to create SocialPost objects
      List<SocialPost> posts = [];
      for (var item in mediaItems) {
        // Get detailed media information
        final detailResponse = await http.get(
          Uri.parse(
              '$apiBaseUrl/${item['id']}?fields=id,caption,media_type,media_url,permalink,timestamp&access_token=$accessToken'),
        );

        if (detailResponse.statusCode == 200) {
          final detailData = json.decode(detailResponse.body);
          final caption = detailData['caption'] ?? '';

          // Filter for health-related content
          if (_healthFilter.isHealthRelated(caption)) {
            final post = SocialPost(
              id: 'instagram_${detailData['id']}',
              source: 'instagram',
              sourceId: detailData['id'],
              contentType: _mapInstagramMediaType(detailData['media_type']),
              mediaUrl:
                  detailData['media_url'] ?? detailData['permalink'] ?? '',
              caption: caption,
              healthTopics: _healthFilter.extractHealthTopics(caption),
              relevanceScore: _healthFilter.calculateRelevanceScore(caption),
              engagementMetrics: {'likes': 0, 'shares': 0, 'saves': 0},
              filteredStatus: 'approved',
              createdAt: DateTime.parse(detailData['timestamp']),
            );
            posts.add(post);
          }
        }
      }

      return posts;
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error fetching Instagram media: $e');
      }
      return [];
    }
  }

  /// Map Instagram media type to our app's content type
  String _mapInstagramMediaType(String instagramType) {
    switch (instagramType.toLowerCase()) {
      case 'image':
        return 'image';
      case 'video':
        return 'video';
      case 'carousel_album':
        return 'image'; // Default to image for carousel
      default:
        return 'text';
    }
  }

  /// Store authentication data
  Future<void> storeAuthData(String accessToken, String userId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('instagram_access_token', accessToken);
      await prefs.setString('instagram_user_id', userId);
      if (kDebugMode) {
        debugPrint('Instagram authentication data stored');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error storing Instagram auth data: $e');
      }
    }
  }

  /// Clear authentication data
  Future<void> clearAuthData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('instagram_access_token');
      await prefs.remove('instagram_user_id');
      if (kDebugMode) {
        debugPrint('Instagram authentication data cleared');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error clearing Instagram auth data: $e');
      }
    }
  }
}
