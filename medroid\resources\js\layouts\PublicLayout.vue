<script setup lang="ts">
import { Link } from '@inertiajs/vue3';
import MedroidLogo from '@/components/MedroidLogo.vue';

defineProps<{
    title?: string;
    showHeader?: boolean;
}>();
</script>

<template>
    <div class="min-h-screen bg-gray-50">
        <!-- Header -->
        <header v-if="showHeader !== false" class="bg-white shadow-sm border-b border-gray-200">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center h-16">
                    <!-- Logo -->
                    <Link :href="route('home')" class="flex items-center space-x-3">
                        <MedroidLogo :size="32" />
                        <span class="text-xl font-bold text-medroid-navy">Medroid</span>
                    </Link>

                    <!-- Navigation -->
                    <div class="flex items-center space-x-4">
                        <Link
                            :href="route('login')"
                            class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium transition-colors"
                        >
                            Sign In
                        </Link>
                        <Link
                            :href="route('register')"
                            class="bg-medroid-primary text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-medroid-primary/90 transition-colors"
                        >
                            Sign Up
                        </Link>
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="flex-1">
            <slot />
        </main>

        <!-- Footer -->
        <footer class="bg-white border-t border-gray-200 mt-auto">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
                    <div class="flex items-center space-x-3">
                        <MedroidLogo :size="24" />
                        <span class="text-sm text-gray-600">© 2024 Medroid. All rights reserved.</span>
                    </div>
                    <div class="flex space-x-6 text-sm text-gray-600">
                        <a href="/privacy-policy" target="_blank" class="hover:text-gray-900 transition-colors">Privacy Policy</a>
                        <a href="/terms-and-conditions" target="_blank" class="hover:text-gray-900 transition-colors">Terms of Service</a>
                        <a href="#" class="hover:text-gray-900 transition-colors">Contact</a>
                    </div>
                </div>
            </div>
        </footer>
    </div>
</template>
