<?php

namespace Tests\Feature;

use App\Models\EmailTemplate;
use App\Services\EmailTemplateService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Validator;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class EmailTemplateValidationTest extends TestCase
{
    use RefreshDatabase;

    private EmailTemplateService $emailTemplateService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->emailTemplateService = app(EmailTemplateService::class);
    }

    #[Test]
    public function it_validates_required_fields()
    {
        $validator = Validator::make([], [
            'name' => 'required|string|max:255',
            'slug' => 'required|string|max:255|unique:email_templates',
            'subject' => 'required|string|max:255',
            'content' => 'required|string',
        ]);

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('name', $validator->errors()->toArray());
        $this->assertArrayHasKey('slug', $validator->errors()->toArray());
        $this->assertArrayHasKey('subject', $validator->errors()->toArray());
        $this->assertArrayHasKey('content', $validator->errors()->toArray());
    }

    #[Test]
    public function it_validates_unique_name_constraint()
    {
        EmailTemplate::create([
            'name' => 'Existing Template',
            'slug' => 'existing-template',
            'subject' => 'Subject',
            'content' => 'Content',
        ]);

        $this->expectException(\Illuminate\Database\QueryException::class);

        EmailTemplate::create([
            'name' => 'Existing Template',
            'slug' => 'different-slug',
            'subject' => 'Subject',
            'content' => 'Content',
        ]);
    }

    #[Test]
    public function it_validates_unique_slug_constraint()
    {
        EmailTemplate::create([
            'name' => 'Template One',
            'slug' => 'existing-slug',
            'subject' => 'Subject',
            'content' => 'Content',
        ]);

        $this->expectException(\Illuminate\Database\QueryException::class);

        EmailTemplate::create([
            'name' => 'Template Two',
            'slug' => 'existing-slug',
            'subject' => 'Subject',
            'content' => 'Content',
        ]);
    }

    #[Test]
    public function it_validates_maximum_field_lengths()
    {
        $longString = str_repeat('a', 300);

        $validator = Validator::make([
            'name' => $longString,
            'slug' => $longString,
            'subject' => $longString,
            'content' => 'Valid content',
        ], [
            'name' => 'required|string|max:255',
            'slug' => 'required|string|max:255',
            'subject' => 'required|string|max:255',
            'content' => 'required|string',
        ]);

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('name', $validator->errors()->toArray());
        $this->assertArrayHasKey('slug', $validator->errors()->toArray());
        $this->assertArrayHasKey('subject', $validator->errors()->toArray());
    }

    #[Test]
    public function it_allows_nullable_description()
    {
        $template = EmailTemplate::create([
            'name' => 'Test Template',
            'slug' => 'test-template',
            'subject' => 'Subject',
            'content' => 'Content',
            'description' => null,
        ]);

        $this->assertNull($template->description);
    }

    #[Test]
    public function it_validates_boolean_is_active_field()
    {
        $validator = Validator::make([
            'is_active' => 'invalid_boolean',
        ], [
            'is_active' => 'boolean',
        ]);

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('is_active', $validator->errors()->toArray());
    }

    #[Test]
    public function it_accepts_valid_boolean_values_for_is_active()
    {
        $validValues = [true, false, 1, 0, '1', '0'];

        foreach ($validValues as $value) {
            $validator = Validator::make([
                'is_active' => $value,
            ], [
                'is_active' => 'boolean',
            ]);

            $this->assertFalse($validator->fails(), "Failed for value: " . var_export($value, true));
        }
    }

    #[Test]
    public function it_handles_empty_content_gracefully()
    {
        $template = EmailTemplate::create([
            'name' => 'Empty Content Template',
            'slug' => 'empty-content',
            'subject' => 'Subject',
            'content' => '',
        ]);

        $result = $this->emailTemplateService->renderTemplate($template->slug);

        $this->assertIsArray($result);
        $this->assertEquals('Subject', $result['subject']);
        $this->assertIsString($result['content']);
    }

    #[Test]
    public function it_handles_special_characters_in_fields()
    {
        $template = EmailTemplate::create([
            'name' => 'Special Chars: @#$%^&*()',
            'slug' => 'special-chars-test',
            'subject' => 'Subject with émojis 🎉 and spëcial chars',
            'content' => 'Content with special characters: @#$%^&*()_+-=[]{}|;:,.<>? and émojis: 😊 🏥 💊',
        ]);

        $this->assertStringContainsString('@#$%^&*()', $template->name);
        $this->assertStringContainsString('🎉', $template->subject);
        $this->assertStringContainsString('😊', $template->content);
    }

    #[Test]
    public function it_handles_html_content_validation()
    {
        $htmlContent = '
            <div style="color: red;">
                <h1>Title</h1>
                <p>Paragraph with <strong>bold</strong> and <em>italic</em> text.</p>
                <a href="https://example.com">Link</a>
                <img src="https://example.com/image.jpg" alt="Image">
            </div>
        ';

        $template = EmailTemplate::create([
            'name' => 'HTML Template',
            'slug' => 'html-template',
            'subject' => 'HTML Subject',
            'content' => $htmlContent,
        ]);

        $this->assertEquals($htmlContent, $template->content);
        $this->assertStringContainsString('<div style="color: red;">', $template->content);
        $this->assertStringContainsString('<strong>bold</strong>', $template->content);
    }

    #[Test]
    public function it_handles_malformed_blade_syntax()
    {
        $template = EmailTemplate::create([
            'name' => 'Malformed Template',
            'slug' => 'malformed-template',
            'subject' => 'Subject',
            'content' => 'Hello {{ $userName, this is malformed blade syntax @if($invalid',
        ]);

        $result = $this->emailTemplateService->renderTemplate($template->slug, ['userName' => 'John']);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('subject', $result);
        $this->assertArrayHasKey('content', $result);
        // Should handle errors gracefully
        $this->assertStringContainsString('Error rendering template', $result['content']);
    }

    #[Test]
    public function it_handles_missing_variables_gracefully()
    {
        $template = EmailTemplate::create([
            'name' => 'Missing Variables Template',
            'slug' => 'missing-variables',
            'subject' => 'Hello {{ $userName }}',
            'content' => 'Welcome {{ $userName }} to {{ $appName }}! Your ID is {{ $userId }}.',
        ]);

        // Provide only partial data
        $result = $this->emailTemplateService->renderTemplate($template->slug, [
            'userName' => 'John',
            // Missing appName and userId
        ]);

        $this->assertIsArray($result);
        $this->assertStringContainsString('John', $result['content']);
        // Should handle missing variables gracefully
    }

    #[Test]
    public function it_validates_slug_format()
    {
        $invalidSlugs = [
            'Invalid Slug With Spaces',
            'invalid-slug-with-UPPERCASE',
            'invalid_slug_with_underscores',
            'invalid.slug.with.dots',
            'invalid@slug#with$symbols',
        ];

        foreach ($invalidSlugs as $slug) {
            $validator = Validator::make([
                'slug' => $slug,
            ], [
                'slug' => 'regex:/^[a-z0-9-]+$/',
            ]);

            $this->assertTrue($validator->fails(), "Slug '{$slug}' should be invalid");
        }
    }

    #[Test]
    public function it_accepts_valid_slug_formats()
    {
        $validSlugs = [
            'valid-slug',
            'valid-slug-123',
            'user-registration',
            'appointment-booked-patient',
            'password-reset',
        ];

        foreach ($validSlugs as $slug) {
            $validator = Validator::make([
                'slug' => $slug,
            ], [
                'slug' => 'regex:/^[a-z0-9-]+$/',
            ]);

            $this->assertFalse($validator->fails(), "Slug '{$slug}' should be valid");
        }
    }

    #[Test]
    public function it_handles_extremely_long_content()
    {
        $veryLongContent = str_repeat('This is a very long content string. ', 10000);

        $template = EmailTemplate::create([
            'name' => 'Very Long Template',
            'slug' => 'very-long-template',
            'subject' => 'Subject',
            'content' => $veryLongContent,
        ]);

        $result = $this->emailTemplateService->renderTemplate($template->slug);

        $this->assertIsArray($result);
        $this->assertGreaterThan(100000, strlen($result['content']));
    }

    #[Test]
    public function it_handles_unicode_and_multibyte_characters()
    {
        $unicodeContent = '
            Hello {{ $userName }}!
            Unicode characters: áéíóú ñ ç ü ß
            Emojis: 😀 😃 😄 😁 😆 😅 😂 🤣
            Mathematical symbols: ∑ ∏ ∫ ∂ ∇ ∞ ≠ ≤ ≥
            Currency: € £ ¥ ₹ ₽ ₩ ₪
            Chinese: 你好世界
            Arabic: مرحبا بالعالم
            Russian: Привет мир
            Japanese: こんにちは世界
        ';

        $template = EmailTemplate::create([
            'name' => 'Unicode Template',
            'slug' => 'unicode-template',
            'subject' => 'Unicode Subject 🌍',
            'content' => $unicodeContent,
        ]);

        $result = $this->emailTemplateService->renderTemplate($template->slug, ['userName' => 'José']);

        $this->assertIsArray($result);
        $this->assertStringContainsString('🌍', $result['subject']);
        $this->assertStringContainsString('José', $result['content']);
        $this->assertStringContainsString('😀', $result['content']);
        $this->assertStringContainsString('你好世界', $result['content']);
    }

    #[Test]
    public function it_validates_template_data_types()
    {
        $template = EmailTemplate::create([
            'name' => 'Data Types Template',
            'slug' => 'data-types-template',
            'subject' => 'Subject',
            'content' => 'String: {{ $stringVar }}, Number: {{ $numberVar }}, Boolean: {{ $boolVar }}',
        ]);

        $data = [
            'stringVar' => 'Hello World',
            'numberVar' => 12345,
            'boolVar' => true,
        ];

        $result = $this->emailTemplateService->renderTemplate($template->slug, $data);

        $this->assertIsArray($result);
        $this->assertStringContainsString('Hello World', $result['content']);
        $this->assertStringContainsString('12345', $result['content']);
        $this->assertStringContainsString('1', $result['content']); // true converts to '1'
    }
}
