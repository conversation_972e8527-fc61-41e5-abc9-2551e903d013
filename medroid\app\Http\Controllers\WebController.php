<?php

namespace App\Http\Controllers;

use Inertia\Inertia;
use Inertia\Response;

class WebController extends Controller
{
    /**
     * Display the home page.
     */
    public function index(): Response
    {
        return Inertia::render('Welcome');
    }

    /**
     * Display the terms and conditions page.
     */
    public function termsAndConditions()
    {
        return view('legal.terms-and-conditions');
    }

    /**
     * Display the privacy policy page.
     */
    public function privacyPolicy()
    {
        return view('legal.privacy-policy');
    }

    /**
     * Display the discover page.
     */
    public function discover(): Response
    {
        return Inertia::render('Discover');
    }

    /**
     * Display the shop page.
     */
    public function shop(): Response
    {
        return Inertia::render('Shop');
    }

    /**
     * Display the chat history page.
     */
    public function chatHistory(): Response
    {
        return Inertia::render('ChatHistory');
    }

    /**
     * Display the credit history page.
     */
    public function creditHistory(): Response
    {
        return Inertia::render('CreditHistory');
    }
}
