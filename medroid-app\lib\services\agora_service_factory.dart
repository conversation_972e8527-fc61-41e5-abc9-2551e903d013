import 'package:medroid_app/services/api_service.dart';
import 'package:medroid_app/services/agora_service.dart';

/// Factory for creating Agora service for mobile platforms
class AgoraServiceFactory {
  /// Create an Agora service for mobile platforms
  static AgoraService createAgoraService(ApiService apiService) {
    // Return mobile service for mobile platforms
    return AgoraService(apiService);
  }
}
