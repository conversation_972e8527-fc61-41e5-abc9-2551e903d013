import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:medroid_app/utils/env_config.dart';

/// Service for managing Agora tokens
/// This service can fetch tokens from the backend or generate them locally if needed
class AgoraTokenService {
  /// Get Agora token from the backend token service
  /// Falls back to using the token from the main API if token service is not configured
  static Future<Map<String, dynamic>> getAgoraToken({
    required String channelName,
    required int uid,
    required String role, // 'publisher' or 'subscriber'
  }) async {
    try {
      // Check if we have a token service URL configured
      final tokenServiceUrl = EnvConfig.agoraTokenServiceUrl;
      
      if (tokenServiceUrl != null) {
        return await _getTokenFromService(
          tokenServiceUrl,
          channelName,
          uid,
          role,
        );
      } else {
        // Fallback: The main video session API should provide the token
        debugPrint('No token service URL configured, relying on main API for token');
        return {
          'success': false,
          'message': 'Token service not configured, use main API',
        };
      }
    } catch (e) {
      debugPrint('Error getting Agora token: $e');
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// Fetch token from the dedicated token service
  static Future<Map<String, dynamic>> _getTokenFromService(
    String serviceUrl,
    String channelName,
    int uid,
    String role,
  ) async {
    try {
      final response = await http.post(
        Uri.parse(serviceUrl),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: jsonEncode({
          'channel_name': channelName,
          'uid': uid,
          'role': role,
          'expiry_time': 3600, // 1 hour
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return {
          'success': true,
          'token': data['token'],
          'expires_at': data['expires_at'],
        };
      } else {
        throw Exception('Token service returned ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      debugPrint('Error fetching token from service: $e');
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// Refresh an existing token if it's about to expire
  static Future<Map<String, dynamic>> refreshToken({
    required String channelName,
    required int uid,
    required String role,
  }) async {
    try {
      debugPrint('Refreshing Agora token for channel: $channelName, uid: $uid');
      return await getAgoraToken(
        channelName: channelName,
        uid: uid,
        role: role,
      );
    } catch (e) {
      debugPrint('Error refreshing token: $e');
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// Get Agora configuration for debugging
  static Map<String, dynamic> getAgoraConfig() {
    return {
      'app_id': EnvConfig.agoraAppId,
      'app_certificate_configured': EnvConfig.agoraAppCertificate.isNotEmpty,
      'token_service_url': EnvConfig.agoraTokenServiceUrl,
      'has_token_service': EnvConfig.agoraTokenServiceUrl != null,
    };
  }

  /// Validate if Agora is properly configured
  static bool isAgoraConfigValid() {
    final appId = EnvConfig.agoraAppId;
    final certificate = EnvConfig.agoraAppCertificate;
    
    return appId.isNotEmpty && certificate.isNotEmpty;
  }
}