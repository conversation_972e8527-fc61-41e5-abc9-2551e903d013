<script setup>
import AppLayout from '@/layouts/AppLayout.vue';
import ProviderModal from '@/components/ProviderModal.vue';
import { Head, Link } from '@inertiajs/vue3';
import { ref, onMounted, computed } from 'vue';

const breadcrumbs = [
    { title: 'Dashboard', href: '/dashboard' },
    { title: 'Providers', href: '/providers' },
];

const loading = ref(false);
const providers = ref([]);
const searchQuery = ref('');
const selectedProvider = ref(null);
const showProviderModal = ref(false);
const showCreateModal = ref(false);
const showEditModal = ref(false);

const fetchProviders = async () => {
    loading.value = true;
    try {
        const response = await window.axios.get('/providers-list');
        console.log('Providers API response:', response.data);
        // Handle paginated response - extract the data array
        providers.value = response.data.data || response.data || [];
        console.log('Processed providers:', providers.value);
    } catch (error) {
        console.error('Error fetching providers:', error);
        providers.value = [];
    } finally {
        loading.value = false;
    }
};

// Helper functions
const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
};

const parseJsonField = (field) => {
    if (!field) return [];
    if (typeof field === 'string') {
        try {
            return JSON.parse(field);
        } catch (e) {
            return [];
        }
    }
    return Array.isArray(field) ? field : [];
};

const parseJsonObject = (field) => {
    if (!field) return {};
    if (typeof field === 'string') {
        try {
            return JSON.parse(field);
        } catch (e) {
            return {};
        }
    }
    return typeof field === 'object' ? field : {};
};

const getStatusBadgeClass = (status) => {
    const classes = {
        verified: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
        pending: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
        rejected: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
        suspended: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
    };
    return classes[status] || 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
};

const viewProviderDetails = (provider) => {
    selectedProvider.value = provider;
    showProviderModal.value = true;
};

const closeModal = () => {
    showProviderModal.value = false;
    selectedProvider.value = null;
};

const openCreateModal = () => {
    selectedProvider.value = null;
    showCreateModal.value = true;
};

const openEditModal = (provider) => {
    selectedProvider.value = provider;
    showEditModal.value = true;
};

const closeCreateEditModal = () => {
    showCreateModal.value = false;
    showEditModal.value = false;
    selectedProvider.value = null;
};

const handleProviderSaved = () => {
    closeCreateEditModal();
    fetchProviders(); // Refresh the list
};

// Computed properties
const filteredProviders = computed(() => {
    if (!searchQuery.value) return providers.value;
    return providers.value.filter(provider =>
        provider.user?.name?.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
        provider.user?.email?.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
        provider.specialization?.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
        provider.license_number?.includes(searchQuery.value)
    );
});

onMounted(() => {
    fetchProviders();
});
</script>

<template>
    <Head title="Provider Management" />

    <AppLayout>
        <template #header>
            <div class="flex items-center justify-between">
                <div>
                    <h2 class="text-xl font-semibold leading-tight text-gray-800 dark:text-gray-200">
                        Provider Management
                    </h2>
                    <nav class="flex mt-2" aria-label="Breadcrumb">
                        <ol class="inline-flex items-center space-x-1 md:space-x-3">
                            <li v-for="(breadcrumb, index) in breadcrumbs" :key="index" class="inline-flex items-center">
                                <Link v-if="index < breadcrumbs.length - 1" 
                                    :href="breadcrumb.href" 
                                    class="text-sm font-medium text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                                    {{ breadcrumb.title }}
                                </Link>
                                <span v-else class="text-sm font-medium text-gray-700 dark:text-gray-400">
                                    {{ breadcrumb.title }}
                                </span>
                                <svg v-if="index < breadcrumbs.length - 1" class="w-3 h-3 mx-1 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                </svg>
                            </li>
                        </ol>
                    </nav>
                </div>
                <button @click="openCreateModal" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    Add Provider
                </button>
            </div>
        </template>

        <div class="py-12">
            <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
                <!-- Search and Filter Bar -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg mb-6">
                    <div class="p-6">
                        <div class="flex flex-col sm:flex-row gap-4">
                            <div class="flex-1">
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <i class="fas fa-search text-gray-400"></i>
                                    </div>
                                    <input
                                        v-model="searchQuery"
                                        type="text"
                                        placeholder="Search providers by name, email, specialization, or license..."
                                        class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                    >
                                </div>
                            </div>
                            <button @click="openCreateModal" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                <i class="fas fa-plus mr-2"></i>Add Provider
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Stats Cards -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-user-md text-2xl text-blue-500"></i>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Providers</p>
                                    <p class="text-2xl font-semibold text-gray-900 dark:text-gray-100">{{ providers.length }}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-check-circle text-2xl text-green-500"></i>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Verified</p>
                                    <p class="text-2xl font-semibold text-gray-900 dark:text-gray-100">
                                        {{ providers.filter(p => p.verification_status === 'verified').length }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-clock text-2xl text-yellow-500"></i>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Pending</p>
                                    <p class="text-2xl font-semibold text-gray-900 dark:text-gray-100">
                                        {{ providers.filter(p => p.verification_status === 'pending').length }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-stethoscope text-2xl text-purple-500"></i>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Specializations</p>
                                    <p class="text-2xl font-semibold text-gray-900 dark:text-gray-100">
                                        {{ [...new Set(providers.map(p => p.specialization).filter(Boolean))].length }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Providers Table -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 text-gray-900 dark:text-gray-100">
                        <div v-if="loading" class="text-center py-8">
                            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
                        </div>

                        <div v-else-if="filteredProviders.length === 0" class="text-center py-8">
                            <i class="fas fa-user-md text-4xl text-gray-400 mb-4"></i>
                            <p class="text-gray-500">No providers found</p>
                        </div>

                        <div v-else class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                                <thead class="bg-gray-50 dark:bg-gray-700">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Provider
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Clinic
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Specialization
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Status
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Experience
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Rating
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Actions
                                        </th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                    <tr v-for="provider in filteredProviders" :key="provider.id" class="hover:bg-gray-50 dark:hover:bg-gray-700">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="flex-shrink-0 h-10 w-10">
                                                    <div class="h-10 w-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                                                        <i class="fas fa-user-md text-blue-600 dark:text-blue-400"></i>
                                                    </div>
                                                </div>
                                                <div class="ml-4">
                                                    <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                                        {{ provider.user?.name || 'Unknown Provider' }}
                                                    </div>
                                                    <div class="text-sm text-gray-500 dark:text-gray-400">
                                                        {{ provider.user?.email || 'No Email' }}
                                                    </div>
                                                    <div class="text-xs text-gray-400">
                                                        ID: {{ provider.id }}
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900 dark:text-gray-100">
                                                {{ provider.clinic?.name || 'No Clinic' }}
                                            </div>
                                            <div class="text-sm text-gray-500 dark:text-gray-400">
                                                {{ provider.clinic?.city || 'N/A' }}
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900 dark:text-gray-100">{{ provider.specialization || 'N/A' }}</div>
                                            <div class="text-sm text-gray-500 dark:text-gray-400">{{ provider.license_number || 'No License' }}</div>
                                            <div class="text-xs text-gray-400">{{ provider.gender || 'N/A' }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span :class="getStatusBadgeClass(provider.verification_status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full capitalize">
                                                {{ provider.verification_status || 'Unknown' }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900 dark:text-gray-100">
                                                {{ provider.specialization || 'N/A' }}
                                            </div>
                                            <div class="text-xs text-gray-500 dark:text-gray-400">
                                                {{ formatDate(provider.user?.created_at) }}
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <i class="fas fa-star text-yellow-400 mr-1"></i>
                                                <span class="text-sm text-gray-900 dark:text-gray-100">{{ provider.rating || '0.0' }}</span>
                                            </div>
                                            <div class="text-xs text-gray-500 dark:text-gray-400">
                                                {{ provider.accepts_insurance ? 'Accepts Insurance' : 'Cash Only' }}
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <button @click="viewProviderDetails(provider)" class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3">
                                                <i class="fas fa-eye mr-1"></i>View
                                            </button>
                                            <button @click="openEditModal(provider)" class="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300 mr-3">
                                                <i class="fas fa-edit mr-1"></i>Edit
                                            </button>
                                            <button v-if="provider.verification_status === 'pending'" class="text-purple-600 hover:text-purple-900 dark:text-purple-400 dark:hover:text-purple-300">
                                                <i class="fas fa-check mr-1"></i>Verify
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Provider Details Modal -->
        <div v-if="showProviderModal" class="fixed inset-0 bg-white bg-opacity-20 backdrop-blur-sm overflow-y-auto h-full w-full z-50">
            <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-2/3 shadow-lg rounded-md bg-white dark:bg-gray-800">
                <div class="mt-3">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
                            Provider Details - {{ selectedProvider?.user?.name }}
                        </h3>
                        <button @click="closeModal" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>

                    <div v-if="selectedProvider" class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Personal Information -->
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                            <h4 class="text-md font-semibold text-gray-900 dark:text-gray-100 mb-3">
                                <i class="fas fa-user mr-2"></i>Personal Information
                            </h4>
                            <div class="space-y-2 text-sm">
                                <div><strong>Name:</strong> {{ selectedProvider.user?.name }}</div>
                                <div><strong>Email:</strong> {{ selectedProvider.user?.email }}</div>
                                <div><strong>Phone:</strong> {{ selectedProvider.user?.phone_number || 'N/A' }}</div>
                                <div><strong>Gender:</strong> {{ selectedProvider.gender || 'N/A' }}</div>
                                <div><strong>Date of Birth:</strong> {{ formatDate(selectedProvider.date_of_birth) }}</div>
                                <div><strong>Status:</strong>
                                    <span :class="selectedProvider.user?.is_active ? 'text-green-600' : 'text-red-600'">
                                        {{ selectedProvider.user?.is_active ? 'Active' : 'Inactive' }}
                                    </span>
                                </div>
                            </div>
                        </div>

                        <!-- Professional Information -->
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                            <h4 class="text-md font-semibold text-gray-900 dark:text-gray-100 mb-3">
                                <i class="fas fa-stethoscope mr-2"></i>Professional Information
                            </h4>
                            <div class="space-y-2 text-sm">
                                <div><strong>Specialization:</strong> {{ selectedProvider.specialization || 'N/A' }}</div>
                                <div><strong>License Number:</strong> {{ selectedProvider.license_number || 'N/A' }}</div>
                                <div><strong>Education:</strong> {{ selectedProvider.education || 'N/A' }}</div>
                                <div><strong>Verification Status:</strong>
                                    <span :class="getStatusBadgeClass(selectedProvider.verification_status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full capitalize ml-2">
                                        {{ selectedProvider.verification_status || 'Unknown' }}
                                    </span>
                                </div>
                            </div>
                        </div>

                        <!-- Clinic Information -->
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                            <h4 class="text-md font-semibold text-gray-900 dark:text-gray-100 mb-3">
                                <i class="fas fa-clinic-medical mr-2"></i>Clinic Information
                            </h4>
                            <div class="space-y-2 text-sm">
                                <div><strong>Clinic Name:</strong> {{ selectedProvider.clinic?.name || 'No Clinic Assigned' }}</div>
                                <div><strong>Clinic Address:</strong> {{ selectedProvider.clinic?.full_address || 'N/A' }}</div>
                                <div><strong>Clinic Phone:</strong> {{ selectedProvider.clinic?.phone || 'N/A' }}</div>
                                <div><strong>Clinic Email:</strong> {{ selectedProvider.clinic?.email || 'N/A' }}</div>
                                <div><strong>Accepts Insurance:</strong> {{ selectedProvider.accepts_insurance ? 'Yes' : 'No' }}</div>
                                <div><strong>Rating:</strong>
                                    <span class="flex items-center">
                                        <i class="fas fa-star text-yellow-400 mr-1"></i>
                                        {{ selectedProvider.rating || '0.0' }}
                                    </span>
                                </div>
                            </div>
                        </div>

                        <!-- Availability & Services -->
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                            <h4 class="text-md font-semibold text-gray-900 dark:text-gray-100 mb-3">
                                <i class="fas fa-calendar mr-2"></i>Availability & Services
                            </h4>
                            <div class="space-y-2 text-sm">
                                <div><strong>Available Days:</strong> {{ parseJsonField(selectedProvider.available_days).join(', ') || 'N/A' }}</div>
                                <div><strong>Available Hours:</strong> {{ selectedProvider.available_hours || 'N/A' }}</div>
                                <div><strong>Services:</strong> {{ parseJsonField(selectedProvider.services).join(', ') || 'N/A' }}</div>
                                <div><strong>Languages:</strong> {{ parseJsonField(selectedProvider.languages).join(', ') || 'N/A' }}</div>
                            </div>
                        </div>

                        <!-- Bio & Additional Info -->
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg md:col-span-2">
                            <h4 class="text-md font-semibold text-gray-900 dark:text-gray-100 mb-3">
                                <i class="fas fa-info-circle mr-2"></i>Biography & Additional Information
                            </h4>
                            <div class="space-y-2 text-sm">
                                <div><strong>Bio:</strong></div>
                                <p class="text-gray-600 dark:text-gray-400 bg-white dark:bg-gray-800 p-3 rounded">
                                    {{ selectedProvider.bio || 'No biography provided.' }}
                                </p>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                                    <div><strong>Joined:</strong> {{ formatDate(selectedProvider.user?.created_at) }}</div>
                                    <div><strong>Last Updated:</strong> {{ formatDate(selectedProvider.updated_at) }}</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="flex justify-end mt-6 space-x-3">
                        <button @click="closeModal" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                            Close
                        </button>
                        <button class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                            <i class="fas fa-edit mr-2"></i>Edit Provider
                        </button>
                        <button v-if="selectedProvider?.verification_status === 'pending'" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                            <i class="fas fa-check mr-2"></i>Verify Provider
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Provider Create/Edit Modal -->
        <ProviderModal
            v-if="showCreateModal || showEditModal"
            :provider="selectedProvider"
            :isEdit="showEditModal"
            @close="closeCreateEditModal"
            @saved="handleProviderSaved"
        />
    </AppLayout>
</template>
