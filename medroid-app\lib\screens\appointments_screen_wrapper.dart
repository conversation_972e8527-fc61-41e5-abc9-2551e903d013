import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:medroid_app/models/appointment.dart';
import 'package:medroid_app/services/api_service.dart';
import 'package:medroid_app/utils/app_colors.dart';
import 'package:medroid_app/utils/constants.dart';
import 'package:medroid_app/utils/responsive_utils.dart';
import 'package:medroid_app/widgets/appointment_card.dart';
import 'package:medroid_app/widgets/main_navigation_sidebar.dart';

/// A standalone appointments screen with better UI and responsive design
class AppointmentsScreenWrapper extends StatefulWidget {
  const AppointmentsScreenWrapper({Key? key}) : super(key: key);

  @override
  State<AppointmentsScreenWrapper> createState() =>
      _AppointmentsScreenWrapperState();
}

class _AppointmentsScreenWrapperState extends State<AppointmentsScreenWrapper> {
  List<Appointment> _appointments = [];
  bool _isLoading = false;
  String _filter = 'upcoming';

  @override
  void initState() {
    super.initState();
    _loadAppointments();
  }

  Future<void> _loadAppointments() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final apiService = RepositoryProvider.of<ApiService>(context);
      final response = await apiService.get(Constants.userAppointmentsEndpoint);

      final List<Appointment> appointments = [];
      if (response != null) {
        for (final appointmentData in response) {
          appointments.add(Appointment.fromJson(appointmentData));
        }
      }

      setState(() {
        _appointments = appointments;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading appointments: $e')),
        );
      }
    }
  }

  List<Appointment> get _filteredAppointments {
    final now = DateTime.now();

    switch (_filter) {
      case 'upcoming':
        return _appointments.where((appointment) {
          return appointment.date.isAfter(now) ||
              (appointment.date.day == now.day &&
                  appointment.date.month == now.month &&
                  appointment.date.year == now.year);
        }).toList();
      case 'past':
        return _appointments.where((appointment) {
          return appointment.date.isBefore(now) &&
              !(appointment.date.day == now.day &&
                  appointment.date.month == now.month &&
                  appointment.date.year == now.year);
        }).toList();
      case 'all':
      default:
        return _appointments;
    }
  }

  // Navigation methods for sidebar
  void _navigateToTab(int index) {
    // Navigate to different tabs based on index
    switch (index) {
      case 0: // Chat
        Navigator.pushReplacementNamed(context, '/main');
        break;
      case 1: // Discover
        Navigator.pushReplacementNamed(context, '/main',
            arguments: {'tabIndex': 1});
        break;
      case 3: // Shop
        Navigator.pushReplacementNamed(context, '/main',
            arguments: {'tabIndex': 3});
        break;
      case 4: // Profile
        Navigator.pushReplacementNamed(context, '/main',
            arguments: {'tabIndex': 4});
        break;
    }
  }

  void _openSidebarConversation(String conversationId) {
    // Navigate to the main screen with the chat tab selected and the conversation ID
    Navigator.pushReplacementNamed(
      context,
      '/main',
      arguments: {
        'tabIndex': 0, // Chat tab
        'conversationId': conversationId,
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final isDesktop = ResponsiveUtils.isDesktop(context);
    final isTablet = ResponsiveUtils.isTablet(context);

    // For desktop and tablet layouts, use sidebar navigation
    if (isDesktop || isTablet) {
      return Scaffold(
        backgroundColor: AppColors.backgroundLight,
        body: Row(
          children: [
            // Use the reusable sidebar widget
            MainNavigationSidebar(
              currentIndex: -2, // Special index for appointments screen
              onNavigationChanged: _navigateToTab,
              onConversationSelected: _openSidebarConversation,
            ),
            // Main content area
            Expanded(
              child: _buildAppointmentsContent(context, isDesktop: true),
            ),
          ],
        ),
      );
    }

    // Mobile layout
    return Scaffold(
      backgroundColor: AppColors.backgroundLight,
      appBar: AppBar(
        backgroundColor: AppColors.backgroundLight,
        title: const Text('My Appointments'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.pushReplacementNamed(context, '/main'),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadAppointments,
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: _buildAppointmentsContent(context, isDesktop: false),
    );
  }

  Widget _buildAppointmentsContent(BuildContext context,
      {required bool isDesktop}) {
    return Column(
      children: [
        // Add title and refresh button at the top for desktop
        if (isDesktop)
          Padding(
            padding: const EdgeInsets.fromLTRB(32, 24, 32, 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'My Appointments',
                  style: TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  icon: const Icon(
                    Icons.refresh_outlined,
                    size: 24,
                  ),
                  onPressed: _loadAppointments,
                  padding: const EdgeInsets.all(8),
                  constraints: const BoxConstraints(),
                ),
              ],
            ),
          ),

        // Filter chips
        Padding(
          padding: EdgeInsets.symmetric(
            horizontal: isDesktop ? 32 : 16,
            vertical: 12,
          ),
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                _buildFilterChip('upcoming', 'Upcoming'),
                const SizedBox(width: 8),
                _buildFilterChip('past', 'Past'),
                const SizedBox(width: 8),
                _buildFilterChip('all', 'All'),
              ],
            ),
          ),
        ),

        // Appointments list
        Expanded(
          child: _isLoading
              ? const Center(
                  child: CircularProgressIndicator(color: AppColors.tealSurge),
                )
              : _filteredAppointments.isEmpty
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.event_busy,
                              size: 64, color: Colors.grey[300]),
                          const SizedBox(height: 16),
                          Text(
                            'No $_filter appointments',
                            style: TextStyle(
                                fontSize: 18, color: Colors.grey[600]),
                          ),
                        ],
                      ),
                    )
                  : RefreshIndicator(
                      onRefresh: _loadAppointments,
                      color: AppColors.tealSurge,
                      child: isDesktop
                          ? _buildDesktopAppointmentsList()
                          : _buildMobileAppointmentsList(),
                    ),
        ),
      ],
    );
  }

  Widget _buildDesktopAppointmentsList() {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Determine layout based on available width
        final availableWidth = constraints.maxWidth - 64; // Account for padding
        const cardWidth = 400.0; // Optimal card width
        final crossAxisCount = (availableWidth / cardWidth).floor().clamp(1, 3);

        // Use a more flexible approach for desktop
        if (crossAxisCount == 1) {
          // Single column layout for narrow screens
          return ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
            itemCount: _filteredAppointments.length,
            itemBuilder: (context, index) {
              return Container(
                constraints: const BoxConstraints(maxWidth: 600),
                margin: const EdgeInsets.only(bottom: 16),
                child: AppointmentCard(
                  appointment: _filteredAppointments[index],
                  onActionCompleted: _loadAppointments,
                ),
              );
            },
          );
        }

        // Multi-column grid layout with flexible height
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
          child: GridView.builder(
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: crossAxisCount,
              childAspectRatio: 0.75, // Reduced to minimize white space
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
            ),
            itemCount: _filteredAppointments.length,
            itemBuilder: (context, index) {
              return AppointmentCard(
                appointment: _filteredAppointments[index],
                onActionCompleted: _loadAppointments,
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildMobileAppointmentsList() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _filteredAppointments.length,
      itemBuilder: (context, index) {
        return AppointmentCard(
          appointment: _filteredAppointments[index],
          onActionCompleted: _loadAppointments,
        );
      },
    );
  }

  Widget _buildFilterChip(String value, String label) {
    final isSelected = _filter == value;

    return GestureDetector(
      onTap: () {
        setState(() {
          _filter = value;
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected
              ? AppColors.tealSurge.withAlpha(25)
              : Colors.transparent,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected ? AppColors.tealSurge : Colors.grey.shade300,
            width: 1,
          ),
        ),
        child: Text(
          label,
          style: TextStyle(
            color: isSelected ? AppColors.tealSurge : Colors.black87,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          ),
        ),
      ),
    );
  }
}
