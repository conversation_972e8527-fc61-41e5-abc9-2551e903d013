<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Appointment extends Model
{
    use HasFactory;

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * The primary key type.
     *
     * @var string
     */
    protected $keyType = 'int';

    protected $fillable = [
        'patient_id',
        'provider_id',
        'service_id',
        'date',
        'time_slot',
        'scheduled_at',
        'reason',
        'status',
        'notes',
        'is_telemedicine',
        'video_session_id',
        'consultation_started_at',
        'consultation_ended_at',
        'has_audio_recording',
        'audio_recording_path',
        'recording_started_at',
        'recording_ended_at',
        'transcription',
        'payment_status',
        'payment_intent_id',
        'amount',
        'payment_due_by',
        'paid_at',
    ];

    protected $casts = [
        'date' => 'datetime',
        'time_slot' => 'array',
        'scheduled_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'is_telemedicine' => 'boolean',
        'has_audio_recording' => 'boolean',
        'consultation_started_at' => 'datetime',
        'consultation_ended_at' => 'datetime',
        'recording_started_at' => 'datetime',
        'recording_ended_at' => 'datetime',
        'amount' => 'decimal:2',
        'payment_due_by' => 'datetime',
        'paid_at' => 'datetime',
    ];

    public function patient()
    {
        return $this->belongsTo(Patient::class, 'patient_id', 'id');
    }

    public function provider()
    {
        return $this->belongsTo(Provider::class, 'provider_id', 'id');
    }

    public function service()
    {
        return $this->belongsTo(Service::class, 'service_id', 'id');
    }

    /**
     * Get the video consultation associated with the appointment.
     */
    public function videoConsultation()
    {
        return $this->hasOne(VideoConsultation::class);
    }

    /**
     * Get the active video consultation for this appointment.
     */
    public function activeVideoConsultation()
    {
        return $this->hasOne(VideoConsultation::class)->where(function($query) {
            $query->where('status', 'created')->orWhere('status', 'active');
        });
    }

    /**
     * Get the duration of the appointment in minutes
     *
     * @return int
     */
    public function getDuration()
    {
        if ($this->service) {
            return $this->service->duration;
        }

        // Default duration is 30 minutes
        return 30;
    }

    /**
     * Check if the appointment conflicts with another appointment
     *
     * @param Appointment $appointment
     * @return bool
     */
    public function conflictsWith(Appointment $appointment)
    {
        // Different dates don't conflict
        if ($this->date->format('Y-m-d') !== $appointment->date->format('Y-m-d')) {
            return false;
        }

        // Different providers don't conflict
        if ($this->provider_id !== $appointment->provider_id) {
            return false;
        }

        $thisStart = strtotime($this->time_slot['start_time']);
        $thisEnd = strtotime($this->time_slot['end_time']);

        $otherStart = strtotime($appointment->time_slot['start_time']);
        $otherEnd = strtotime($appointment->time_slot['end_time']);

        // Check if the time slots overlap
        return $thisStart < $otherEnd && $thisEnd > $otherStart;
    }

    /**
     * Check if this is a telemedicine appointment
     *
     * @return bool
     */
    public function isTelemedicine()
    {
        // First check the appointment's is_telemedicine flag
        if (isset($this->is_telemedicine) && $this->is_telemedicine) {
            return true;
        }

        // If not set on the appointment, check the associated service
        if ($this->service && isset($this->service->is_telemedicine)) {
            return $this->service->is_telemedicine;
        }

        return false;
    }

    /**
     * Check if the video session is ready
     *
     * @return bool
     */
    public function isVideoSessionReady()
    {
        return $this->isTelemedicine() &&
               !empty($this->video_session_id) &&
               $this->activeVideoConsultation()->exists();
    }

    /**
     * Check if audio recording is in progress
     *
     * @return bool
     */
    public function isRecordingInProgress()
    {
        return $this->recording_started_at !== null && $this->recording_ended_at === null;
    }

    /**
     * Check if audio recording is completed
     *
     * @return bool
     */
    public function hasCompletedRecording()
    {
        return $this->has_audio_recording &&
               !empty($this->audio_recording_path) &&
               $this->recording_started_at !== null &&
               $this->recording_ended_at !== null;
    }

    /**
     * Start audio recording
     *
     * @return bool
     */
    public function startRecording()
    {
        if ($this->isRecordingInProgress()) {
            return false;
        }

        return $this->update([
            'has_audio_recording' => true,
            'recording_started_at' => now(),
            'recording_ended_at' => null,
        ]);
    }

    /**
     * End audio recording
     *
     * @param string|null $audioPath
     * @return bool
     */
    public function endRecording($audioPath = null)
    {
        if (!$this->isRecordingInProgress()) {
            return false;
        }

        $data = [
            'recording_ended_at' => now(),
        ];

        if ($audioPath) {
            $data['audio_recording_path'] = $audioPath;
        }

        return $this->update($data);
    }

    /**
     * Save transcription
     *
     * @param string $transcription
     * @return bool
     */
    public function saveTranscription($transcription)
    {
        if (!$this->hasCompletedRecording()) {
            return false;
        }

        return $this->update([
            'transcription' => $transcription,
        ]);
    }

    /**
     * Start consultation tracking
     *
     * @return bool
     */
    public function startConsultation()
    {
        if ($this->consultation_started_at !== null) {
            return false;
        }

        return $this->update([
            'consultation_started_at' => now(),
        ]);
    }

    /**
     * End consultation tracking
     *
     * @return bool
     */
    public function endConsultation()
    {
        if ($this->consultation_started_at === null) {
            return false;
        }

        return $this->update([
            'consultation_ended_at' => now(),
        ]);
    }

    /**
     * Get consultation duration in minutes
     *
     * @return int|null
     */
    public function getConsultationDuration()
    {
        if ($this->consultation_started_at === null) {
            return null;
        }

        $endTime = $this->consultation_ended_at ?? now();
        return $this->consultation_started_at->diffInMinutes($endTime);
    }

    /**
     * Format consultation duration as a string (e.g., "45 minutes")
     *
     * @return string|null
     */
    public function getFormattedConsultationDuration()
    {
        $duration = $this->getConsultationDuration();

        if ($duration === null) {
            return null;
        }

        if ($duration < 60) {
            return $duration . ' minute' . ($duration == 1 ? '' : 's');
        }

        $hours = floor($duration / 60);
        $minutes = $duration % 60;

        if ($minutes == 0) {
            return $hours . ' hour' . ($hours == 1 ? '' : 's');
        }

        return $hours . ' hour' . ($hours == 1 ? '' : 's') . ' ' . $minutes . ' minute' . ($minutes == 1 ? '' : 's');
    }

    /**
     * Get the payments associated with this appointment.
     */
    public function payments()
    {
        return $this->hasMany(Payment::class);
    }

    /**
     * Check if the appointment has been paid for.
     *
     * @return bool
     */
    public function isPaid()
    {
        return $this->payment_status === 'paid';
    }

    /**
     * Get the formatted amount with currency symbol.
     *
     * @return string|null
     */
    public function getFormattedAmountAttribute()
    {
        if ($this->amount === null) {
            return null;
        }

        return '$' . number_format($this->amount, 2);
    }

    /**
     * Get a human-readable payment status.
     *
     * @return string
     */
    public function getPaymentStatusLabelAttribute()
    {
        return match($this->payment_status) {
            'paid' => 'Paid',
            'pending' => 'Payment Pending',
            'unpaid' => 'Unpaid',
            'refunded' => 'Refunded',
            default => ucfirst($this->payment_status ?? 'Unknown'),
        };
    }

    /**
     * Boot the model.
     *
     * @return void
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($appointment) {
            // Set scheduled_at based on date and time_slot if not already set
            if (!$appointment->scheduled_at && $appointment->date && isset($appointment->time_slot['start_time'])) {
                $appointment->scheduled_at = Carbon::parse($appointment->date->format('Y-m-d') . ' ' . $appointment->time_slot['start_time']);
            }
        });

        static::updating(function ($appointment) {
            // Update scheduled_at if date or time_slot has changed
            if ($appointment->isDirty('date') || $appointment->isDirty('time_slot')) {
                if ($appointment->date && isset($appointment->time_slot['start_time'])) {
                    $appointment->scheduled_at = Carbon::parse($appointment->date->format('Y-m-d') . ' ' . $appointment->time_slot['start_time']);
                }
            }
        });
    }
}