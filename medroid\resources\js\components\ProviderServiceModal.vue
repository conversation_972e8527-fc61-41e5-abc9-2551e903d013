<template>
  <div class="fixed inset-0 bg-white bg-opacity-20 backdrop-blur-sm overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
      <div class="mt-3">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-medium text-gray-900">
            {{ service ? 'Edit Service' : 'Create New Service' }}
          </h3>
          <button @click="$emit('close')" class="text-gray-400 hover:text-gray-600">
            <i class="fas fa-times"></i>
          </button>
        </div>

        <form @submit.prevent="saveService" class="space-y-4">
          <!-- Service Name -->
          <div>
            <label class="block text-sm font-medium text-gray-700">Service Name *</label>
            <input
              v-model="form.name"
              type="text"
              required
              class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              placeholder="e.g., General Consultation, Cardiology Check-up"
            />
          </div>

          <!-- Description -->
          <div>
            <label class="block text-sm font-medium text-gray-700">Description</label>
            <textarea
              v-model="form.description"
              rows="3"
              class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              placeholder="Describe what this service includes..."
            ></textarea>
          </div>

          <!-- Category -->
          <div>
            <label class="block text-sm font-medium text-gray-700">Category</label>
            <input
              v-model="form.category"
              type="text"
              class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              placeholder="e.g., Consultation, Therapy, Diagnostic"
            />
          </div>

          <!-- Price and Duration -->
          <div class="grid grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700">Price ($) *</label>
              <input
                v-model.number="form.price"
                type="number"
                step="0.01"
                min="0"
                required
                class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="0.00"
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700">Duration (minutes) *</label>
              <input
                v-model.number="form.duration"
                type="number"
                min="5"
                step="5"
                required
                class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="30"
              />
            </div>
          </div>

          <!-- Telemedicine Options -->
          <div class="border border-gray-200 rounded-md p-4">
            <div class="flex items-center mb-3">
              <input
                v-model="form.is_telemedicine"
                type="checkbox"
                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label class="ml-2 block text-sm font-medium text-gray-900">
                Offer as Telemedicine Service
              </label>
            </div>

            <div v-if="form.is_telemedicine" class="ml-6 space-y-2">
              <div class="text-sm text-gray-600 mb-2">Select supported communication methods:</div>
              <div class="flex items-center">
                <input
                  v-model="form.supports_video"
                  type="checkbox"
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label class="ml-2 block text-sm text-gray-900">
                  Video Calls
                </label>
              </div>
              <div class="flex items-center">
                <input
                  v-model="form.supports_audio"
                  type="checkbox"
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label class="ml-2 block text-sm text-gray-900">
                  Audio Calls
                </label>
              </div>
              <div class="flex items-center">
                <input
                  v-model="form.supports_chat"
                  type="checkbox"
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label class="ml-2 block text-sm text-gray-900">
                  Text Chat
                </label>
              </div>
            </div>
          </div>

          <!-- Discount Options -->
          <div class="border border-gray-200 rounded-md p-4">
            <h4 class="text-sm font-medium text-gray-900 mb-3">Promotional Pricing (Optional)</h4>
            <div class="grid grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700">Discount (%)</label>
                <input
                  v-model.number="form.discount_percentage"
                  type="number"
                  step="0.01"
                  min="0"
                  max="100"
                  class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder="0.00"
                />
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700">Valid Until</label>
                <input
                  v-model="form.discount_valid_until"
                  type="datetime-local"
                  class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
          </div>

          <!-- Active Status -->
          <div class="flex items-center">
            <input
              v-model="form.active"
              type="checkbox"
              class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label class="ml-2 block text-sm text-gray-900">
              Make this service active and available for booking
            </label>
          </div>

          <!-- Form Actions -->
          <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200">
            <button
              type="button"
              @click="$emit('close')"
              class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Cancel
            </button>
            <button
              type="submit"
              :disabled="loading"
              class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            >
              {{ loading ? 'Saving...' : (service ? 'Update Service' : 'Create Service') }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';

const props = defineProps({
  service: {
    type: Object,
    default: null
  }
});

const emit = defineEmits(['close', 'saved']);

const loading = ref(false);
const form = ref({
  name: '',
  description: '',
  category: '',
  price: 0,
  duration: 30,
  is_telemedicine: false,
  supports_video: false,
  supports_audio: false,
  supports_chat: false,
  active: true,
  discount_percentage: null,
  discount_valid_until: ''
});

const saveService = async () => {
  loading.value = true;
  try {
    const url = props.service ? `/save-service/${props.service.id}` : '/save-service';
    const method = props.service ? 'put' : 'post';

    console.log('Saving service with data:', form.value);
    console.log('Using URL:', url, 'Method:', method);

    const response = await window.axios[method](url, form.value);
    console.log('Service saved successfully:', response.data);

    emit('saved');
  } catch (error) {
    console.error('Error saving service:', error);
    console.error('Error response:', error.response);

    if (error.response?.status === 419) {
      alert('Session expired. Please refresh the page and try again.');
      window.location.reload();
    } else if (error.response?.data?.errors) {
      const errors = Object.values(error.response.data.errors).flat();
      alert('Validation errors:\n' + errors.join('\n'));
    } else if (error.response?.data?.message) {
      alert('Error: ' + error.response.data.message);
    } else {
      alert('Error saving service. Please try again.');
    }
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  if (props.service) {
    // Populate form with existing service data
    Object.keys(form.value).forEach(key => {
      if (props.service[key] !== undefined) {
        form.value[key] = props.service[key];
      }
    });
    
    // Handle datetime formatting for discount_valid_until
    if (props.service.discount_valid_until) {
      const date = new Date(props.service.discount_valid_until);
      form.value.discount_valid_until = date.toISOString().slice(0, 16);
    }
  }
});
</script>
