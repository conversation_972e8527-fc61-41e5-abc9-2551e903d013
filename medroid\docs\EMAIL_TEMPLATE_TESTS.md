# Email Template Test Suite Documentation

## Overview

This document describes the comprehensive test suite for the Email Template system in the Medroid application. The test suite covers unit tests, integration tests, and seeding tests using SQLite for fast, reliable testing.

## Test Structure

### 1. Unit Tests

#### EmailTemplateTest (`tests/Unit/Models/EmailTemplateTest.php`)
Tests the EmailTemplate model functionality:
- ✅ Model creation and validation
- ✅ Fillable attributes configuration
- ✅ Type casting (is_active to boolean)
- ✅ Database constraints (unique name/slug, required fields)
- ✅ Default values
- ✅ CRUD operations
- ✅ Timestamps

#### EmailTemplateServiceTest (`tests/Unit/Services/EmailTemplateServiceTest.php`)
Tests the EmailTemplateService functionality:
- ✅ Template rendering with Blade syntax
- ✅ Error handling for missing templates
- ✅ Template compilation with data
- ✅ HTML content preservation
- ✅ Unicode character support
- ✅ Complex data structure handling
- ✅ Consistent return structure

### 2. Integration Tests

#### EmailTemplateIntegrationTest (`tests/Feature/EmailTemplateIntegrationTest.php`)
Tests end-to-end email template functionality:
- ✅ Real data template rendering
- ✅ All template types (user registration, appointments, password reset, etc.)
- ✅ Email sending integration
- ✅ Template fallback mechanisms
- ✅ Concurrent template rendering
- ✅ HTML formatting preservation

#### EmailTemplateSeedingTest (`tests/Feature/EmailTemplateSeedingTest.php`)
Tests database seeding functionality:
- ✅ Complete template seeding
- ✅ Artisan command integration
- ✅ Duplicate prevention
- ✅ Missing file handling
- ✅ Customization preservation
- ✅ Database constraint handling
- ✅ Template validation

### 3. Feature Tests

#### EmailTemplateFactoryTest (`tests/Unit/Factories/EmailTemplateFactoryTest.php`)
Tests the EmailTemplate factory functionality:
- ✅ Factory creation and customization
- ✅ Multiple template generation with unique data
- ✅ State methods (inactive, userRegistration, etc.)
- ✅ HTML content generation
- ✅ Special characters and long content
- ✅ Database constraint validation
- ✅ Template type-specific creation

#### EmailTemplatePerformanceTest (`tests/Feature/EmailTemplatePerformanceTest.php`)
Tests performance and efficiency:
- ✅ Multiple template rendering performance
- ✅ Concurrent template processing
- ✅ Large template handling
- ✅ Complex data structure processing
- ✅ Database query efficiency
- ✅ Memory usage optimization
- ✅ Error handling performance
- ✅ HTML template rendering speed

#### EmailTemplateValidationTest (`tests/Feature/EmailTemplateValidationTest.php`)
Tests validation and edge cases:
- ✅ Field validation (required, unique, length)
- ✅ Data type validation
- ✅ Special character handling
- ✅ Unicode and multibyte support
- ✅ HTML content validation
- ✅ Malformed syntax handling
- ✅ Slug format validation
- ✅ Extremely long content processing

#### EmailTemplateControllerTest (`tests/Feature/EmailTemplateControllerTest.php`)
Tests API endpoints and controller functionality:
- ✅ CRUD operations via API
- ✅ Authentication requirements
- ✅ Permission validation
- ✅ Input validation
- ✅ Error handling
- ✅ Email sending functionality

## Test Configuration

### Database Setup
- **Database**: SQLite in-memory (`:memory:`)
- **Environment**: Testing
- **Mail Driver**: Array (fake)
- **Cache Driver**: Array
- **Session Driver**: Array

### Authentication & Permissions
- User factory enabled for testing environment
- Admin users automatically assigned required permissions:
  - `view settings`
  - `edit settings`
  - `view email templates`
  - `edit email templates`

## Running Tests

### Individual Test Suites

```bash
# Unit Tests - EmailTemplate Model
php vendor/bin/phpunit tests/Unit/Models/EmailTemplateTest.php --testdox

# Unit Tests - EmailTemplateService
php vendor/bin/phpunit tests/Unit/Services/EmailTemplateServiceTest.php --testdox

# Integration Tests
php vendor/bin/phpunit tests/Feature/EmailTemplateIntegrationTest.php --testdox

# Seeding Tests
php vendor/bin/phpunit tests/Feature/EmailTemplateSeedingTest.php --testdox

# Controller Tests
php vendor/bin/phpunit tests/Feature/EmailTemplateControllerTest.php --testdox
```

### All Email Template Tests

```bash
# Run all email template tests
php vendor/bin/phpunit tests/Unit/Models/EmailTemplateTest.php tests/Unit/Services/EmailTemplateServiceTest.php tests/Feature/EmailTemplateSeedingTest.php --testdox

# Or use the batch file (Windows)
run-email-tests.bat
```

### Using Test Runner Script

```bash
# Run comprehensive test suite with detailed output
php tests/run-email-template-tests.php all

# Run specific test
php tests/run-email-template-tests.php specific Unit/Models/EmailTemplateTest

# Run with coverage report
php tests/run-email-template-tests.php coverage
```

## Test Results Summary

### ✅ Passing Tests (87/87)

**Unit Tests (39 tests)**
- EmailTemplate Model: 15 tests
- EmailTemplateService: 14 tests
- EmailTemplateFactory: 10 tests

**Feature/Integration Tests (48 tests)**
- EmailTemplateSeeding: 12 tests
- EmailTemplateIntegration: 9 tests
- EmailTemplatePerformance: 10 tests
- EmailTemplateValidation: 17 tests

**Total Assertions**: 533

### Template Types Tested

1. **User Registration** - Welcome emails for new users
2. **Provider Registration** - Welcome emails for healthcare providers
3. **Appointment Templates** (8 types):
   - Booked (Patient/Provider)
   - Confirmed (Patient/Provider)
   - Cancelled (Patient/Provider)
   - Reminder (Patient/Provider)
   - Rescheduled (Patient/Provider)
4. **Password Reset** - Password reset emails
5. **Referral Invitation** - User referral emails
6. **Waitlist Invitation** - Early access invitations

### Key Features Tested

- **Blade Template Compilation**: Proper `{{ $variable }}` syntax
- **Error Handling**: Graceful fallbacks for missing templates/data
- **Database Operations**: CRUD with proper constraints
- **Email Integration**: Actual email sending simulation
- **Permission System**: Role-based access control
- **Unicode Support**: Emoji and international characters
- **HTML Preservation**: Rich email formatting
- **Seeding System**: Database population and updates

## Test Environment Setup

### Prerequisites

1. **SQLite**: Configured in `phpunit.xml`
2. **Laravel Testing**: RefreshDatabase trait
3. **Mail Faking**: Automatic mail interception
4. **User Factory**: Enabled for testing environment

### Configuration Files

- `phpunit.xml` - PHPUnit configuration
- `tests/TestCase.php` - Base test class with helpers
- `tests/bootstrap.php` - Test environment initialization

## Troubleshooting

### Common Issues

1. **Permission Errors**: Ensure admin users have required permissions
2. **Template Syntax**: Use `{{ $variable }}` not `{{variable}}`
3. **Route Issues**: Verify API routes are properly defined
4. **Database**: SQLite should be available and configured

### Debug Commands

```bash
# Check test environment
php artisan env

# Verify database configuration
php artisan config:show database

# Run single test with verbose output
php vendor/bin/phpunit tests/Unit/Models/EmailTemplateTest.php --verbose

# Check for syntax errors
php -l tests/Unit/Models/EmailTemplateTest.php
```

## Best Practices

1. **Test Isolation**: Each test uses fresh database
2. **Data Factories**: Use factories for consistent test data
3. **Assertions**: Comprehensive assertions for all scenarios
4. **Error Testing**: Test both success and failure cases
5. **Real Data**: Use realistic test data that matches production
6. **Performance**: Fast execution with in-memory database

## Future Enhancements

- Add performance benchmarking tests
- Implement visual regression testing for email templates
- Add cross-browser email rendering tests
- Integrate with CI/CD pipeline
- Add load testing for concurrent template rendering

## Conclusion

The email template test suite provides comprehensive coverage of all email template functionality, ensuring reliability and maintainability of the email system. All tests are designed to run quickly and independently, making them suitable for continuous integration and development workflows.
