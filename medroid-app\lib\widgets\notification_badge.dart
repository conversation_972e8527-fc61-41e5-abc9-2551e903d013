import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:medroid_app/services/notification_service.dart';
import 'package:medroid_app/services/web_notification_service.dart';

class NotificationBadge extends StatefulWidget {
  final VoidCallback onTap;
  final Color badgeColor;
  final Color iconColor;

  const NotificationBadge({
    Key? key,
    required this.onTap,
    this.badgeColor = const Color(0xFFEC4899),
    this.iconColor = Colors.black87,
  }) : super(key: key);

  @override
  State<NotificationBadge> createState() => _NotificationBadgeState();
}

class _NotificationBadgeState extends State<NotificationBadge> {
  int _unreadCount = 0;

  @override
  void initState() {
    super.initState();
    _fetchUnreadCount();
  }

  Future<void> _fetchUnreadCount() async {
    try {
      final notificationService = _getNotificationService();
      if (notificationService != null) {
        final count = await notificationService.getUnreadCount();
        setState(() {
          _unreadCount = count;
        });

        // Listen for changes in unread count
        notificationService.unreadCountNotifier.addListener(_updateUnreadCount);
      }
    } catch (e) {
      debugPrint('Error fetching unread count: $e');
    }
  }

  void _updateUnreadCount() {
    final notificationService = _getNotificationService();
    if (notificationService != null) {
      int count = notificationService.unreadCountNotifier.value;

      setState(() {
        _unreadCount = count;
      });
    }
  }

  NotificationService? _getNotificationService() {
    try {
      return RepositoryProvider.of<NotificationService>(context);
    } catch (e) {
      debugPrint('Notification service unavailable: $e');
      return null;
    }
  }

  @override
  void dispose() {
    // Remove listeners
    final notificationService = _getNotificationService();
    if (notificationService != null) {
      notificationService.unreadCountNotifier
          .removeListener(_updateUnreadCount);
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: widget.onTap,
      borderRadius: BorderRadius.circular(20),
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Icon(
              Icons.notifications_outlined,
              color: widget.iconColor,
              size: 24,
            ),
          ),
          if (_unreadCount > 0)
            Positioned(
              top: 0,
              right: 0,
              child: Container(
                padding: const EdgeInsets.all(2),
                decoration: BoxDecoration(
                  color: widget.badgeColor,
                  borderRadius: BorderRadius.circular(10),
                ),
                constraints: const BoxConstraints(
                  minWidth: 16,
                  minHeight: 16,
                ),
                child: Text(
                  _unreadCount > 9 ? '9+' : _unreadCount.toString(),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
        ],
      ),
    );
  }
}
