<template>
  <div class="font-size-control">
    <!-- Compact Mode (for headers/toolbars) -->
    <div v-if="compact" class="flex items-center space-x-2">
      <button
        @click="decreaseFontSize"
        :disabled="currentFontSize === 'small'"
        class="p-1 rounded-md hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
        title="Decrease font size"
      >
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4" />
        </svg>
      </button>
      
      <span class="text-sm font-medium min-w-[60px] text-center">{{ fontSizeName }}</span>
      
      <button
        @click="increaseFontSize"
        :disabled="currentFontSize === 'xlarge'"
        class="p-1 rounded-md hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
        title="Increase font size"
      >
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
        </svg>
      </button>
    </div>

    <!-- Full Mode (for settings pages) -->
    <div v-else class="space-y-4">
      <div class="flex items-center justify-between">
        <label class="text-sm font-medium text-gray-700">Font Size</label>
        <button
          @click="resetFontSize"
          class="text-xs text-gray-500 hover:text-gray-700"
        >
          Reset to Default
        </button>
      </div>
      
      <!-- Font Size Slider -->
      <div class="space-y-3">
        <div class="flex items-center space-x-4">
          <button
            @click="decreaseFontSize"
            :disabled="currentFontSize === 'small'"
            class="p-2 rounded-md border border-gray-300 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            title="Decrease font size"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4" />
            </svg>
          </button>
          
          <!-- Custom Range Slider -->
          <div class="flex-1 relative">
            <input
              type="range"
              :value="fontSizeIndex"
              :min="0"
              :max="availableFontSizes.length - 1"
              @input="handleSliderChange"
              class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
            />
            <div class="flex justify-between text-xs text-gray-500 mt-1">
              <span>A</span>
              <span class="text-sm">A</span>
              <span class="text-lg">A</span>
              <span class="text-xl">A</span>
            </div>
          </div>
          
          <button
            @click="increaseFontSize"
            :disabled="currentFontSize === 'xlarge'"
            class="p-2 rounded-md border border-gray-300 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            title="Increase font size"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
            </svg>
          </button>
        </div>
        
        <!-- Current Selection Display -->
        <div class="text-center">
          <div class="text-sm font-medium text-gray-900">{{ fontSizeName }}</div>
          <div class="text-xs text-gray-500">{{ fontSizeDescription }}</div>
        </div>
        
        <!-- Preview Text -->
        <div class="p-3 bg-gray-50 rounded-lg border">
          <div class="text-xs text-gray-500 mb-1">Preview:</div>
          <div class="space-y-1">
            <p class="text-sm">Small text example</p>
            <p class="text-base">Normal text example</p>
            <p class="text-lg">Large text example</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useFontSize } from '@/composables/useFontSize'

defineProps({
  compact: {
    type: Boolean,
    default: false
  }
})

const {
  currentFontSize,
  fontSizeName,
  fontSizeDescription,
  availableFontSizes,
  setFontSize,
  increaseFontSize,
  decreaseFontSize,
  resetFontSize
} = useFontSize()

// Computed properties
const fontSizeIndex = computed(() => {
  return availableFontSizes.value.findIndex(size => size.key === currentFontSize.value)
})

// Methods
const handleSliderChange = (event) => {
  const index = parseInt(event.target.value)
  const fontSize = availableFontSizes.value[index]
  if (fontSize) {
    setFontSize(fontSize.key)
  }
}
</script>

<style scoped>
/* Custom slider styles */
.slider::-webkit-slider-thumb {
  appearance: none;
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: #17C3B2;
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.slider::-moz-range-thumb {
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: #17C3B2;
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.slider::-webkit-slider-track {
  height: 8px;
  border-radius: 4px;
  background: #e5e7eb;
}

.slider::-moz-range-track {
  height: 8px;
  border-radius: 4px;
  background: #e5e7eb;
}

.slider:focus {
  outline: none;
}

.slider:focus::-webkit-slider-thumb {
  box-shadow: 0 0 0 3px rgba(23, 195, 178, 0.3);
}
</style>
