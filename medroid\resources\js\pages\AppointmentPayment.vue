<script setup>
import { ref, onMounted, nextTick } from 'vue'
import { Head, router } from '@inertiajs/vue3'
import AppLayout from '@/layouts/AppLayout.vue'
import axios from 'axios'

const props = defineProps({
    appointment: Object,
    stripe_public_key: String
})

const loading = ref(false)
const errorMessage = ref('')
const successMessage = ref('')

// Stripe Elements
let stripe = null
let elements = null
let cardElement = null

// Cardholder name
const cardholderName = ref('')

const breadcrumbs = [
    { name: 'Appointments', href: '/appointments' },
    { name: 'Payment', href: '#' }
]

const formatDate = (date) => {
    return new Date(date).toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    })
}

const formatTime = (timeSlot) => {
    if (typeof timeSlot === 'object' && timeSlot.start_time) {
        return `${timeSlot.start_time} - ${timeSlot.end_time}`
    }
    return timeSlot
}

// Initialize Stripe Elements
onMounted(async () => {
    await initializeStripe()
})

const initializeStripe = async () => {
    try {
        // Load Stripe.js if not already loaded
        if (!window.Stripe) {
            const script = document.createElement('script')
            script.src = 'https://js.stripe.com/v3/'
            script.async = true
            document.head.appendChild(script)

            await new Promise((resolve, reject) => {
                script.onload = resolve
                script.onerror = reject
            })
        }

        // Initialize Stripe with UK locale
        stripe = window.Stripe(props.stripe_public_key, {
            locale: 'en-GB'  // Set to UK locale for "Postcode" instead of "ZIP code"
        })
        elements = stripe.elements({
            locale: 'en-GB'
        })

        // Create card element
        cardElement = elements.create('card', {
            style: {
                base: {
                    fontSize: '16px',
                    color: '#424770',
                    '::placeholder': {
                        color: '#aab7c4',
                    },
                },
                invalid: {
                    color: '#9e2146',
                },
            },
        })

        // Mount card element
        await nextTick()
        cardElement.mount('#card-element')

        // Listen for real-time validation errors from the card Element
        cardElement.on('change', ({error}) => {
            if (error) {
                errorMessage.value = error.message
            } else {
                errorMessage.value = ''
            }
        })
    } catch (error) {
        console.error('Error initializing Stripe:', error)
        errorMessage.value = 'Failed to initialize payment system. Please refresh the page.'
    }
}

const processPayment = async () => {
    if (!validateForm()) {
        return
    }

    if (!stripe || !cardElement) {
        errorMessage.value = 'Payment system not ready. Please refresh the page.'
        return
    }

    loading.value = true
    errorMessage.value = ''
    successMessage.value = ''

    try {
        // Check if client secret exists
        if (!props.appointment.client_secret) {
            errorMessage.value = 'Payment information not found. Please refresh the page and try again.'
            loading.value = false
            return
        }

        // Confirm payment with Stripe Elements
        const {error, paymentIntent} = await stripe.confirmCardPayment(props.appointment.client_secret, {
            payment_method: {
                card: cardElement,
                billing_details: {
                    name: cardholderName.value,
                },
            }
        })

        if (error) {
            errorMessage.value = error.message
            loading.value = false
            return
        }

        // Payment succeeded, confirm with backend
        const response = await axios.post('/confirm-elements-payment', {
            payment_intent_id: paymentIntent.id,
            appointment_id: props.appointment.id
        })

        if (response.data.success) {
            successMessage.value = 'Payment processed successfully! Redirecting back to chat...'

            // Redirect to chat with appointment confirmation
            setTimeout(() => {
                const urlParams = new URLSearchParams(window.location.search)
                const conversationId = urlParams.get('conversation')
                const redirectUrl = conversationId
                    ? `/chat?payment_success=true&appointment_id=${props.appointment.id}&conversation=${conversationId}`
                    : `/chat?payment_success=true&appointment_id=${props.appointment.id}`

                router.visit(redirectUrl, {
                    onSuccess: () => {
                        console.log('Redirected to chat after successful payment')
                    }
                })
            }, 2000)
        } else {
            errorMessage.value = response.data.message || 'Payment failed. Please try again.'
        }
    } catch (error) {
        console.error('Payment error:', error)
        if (error.response?.data?.message) {
            errorMessage.value = error.response.data.message
        } else if (error.response?.data?.errors) {
            const errors = Object.values(error.response.data.errors).flat()
            errorMessage.value = errors.join(', ')
        } else {
            errorMessage.value = 'An error occurred while processing your payment. Please try again.'
        }
    } finally {
        loading.value = false
    }
}

const validateForm = () => {
    if (!cardholderName.value.trim()) {
        errorMessage.value = 'Please enter the cardholder name.'
        return false
    }

    return true
}

const goBack = () => {
    router.visit('/appointments')
}

// Format and truncate reason text
const formatReason = (reason) => {
    if (!reason) return 'General consultation'

    // Clean up markdown and formatting
    const cleaned = reason
        .replace(/\*\*/g, '') // Remove bold markdown
        .replace(/###/g, '') // Remove headers
        .replace(/\n+/g, ' ') // Replace line breaks with spaces
        .trim()

    // Split into sentences and take first 3-4 sentences or 200 characters
    const sentences = cleaned.split(/[.!?]+/).filter(s => s.trim().length > 0)
    let truncated = sentences.slice(0, 3).join('. ')

    if (truncated.length > 200) {
        truncated = cleaned.substring(0, 200)
    }

    // Add ellipsis if truncated
    if (truncated.length < cleaned.length) {
        truncated += '...'
    }

    return truncated
}

// Show/hide full reason
const showFullReason = ref(false)
const toggleReason = () => {
    showFullReason.value = !showFullReason.value
}
</script>

<template>
    <Head title="Payment - Medroid" />

    <AppLayout :breadcrumbs="breadcrumbs">
        <div class="max-w-2xl mx-auto py-8 px-4">
            <!-- Appointment Details -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h1 class="text-2xl font-bold text-gray-900 mb-4">Complete Payment</h1>
                
                <div class="border-l-4 border-blue-500 pl-4 mb-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-2">Appointment Details</h2>
                    <div class="space-y-2 text-sm text-gray-600">
                        <p><span class="font-medium">Provider:</span> {{ appointment.provider.name }}</p>
                        <p><span class="font-medium">Specialization:</span> {{ appointment.provider.specialization }}</p>
                        <p><span class="font-medium">Service:</span> {{ appointment.service.name }}</p>
                        <p><span class="font-medium">Date:</span> {{ formatDate(appointment.date) }}</p>
                        <p><span class="font-medium">Time:</span> {{ formatTime(appointment.time_slot) }}</p>
                        <div>
                            <span class="font-medium">Reason:</span>
                            <div class="mt-1">
                                <p v-if="!showFullReason" class="text-gray-600">
                                    {{ formatReason(appointment.reason) }}
                                    <button
                                        v-if="appointment.reason && appointment.reason.length > 200"
                                        @click="toggleReason"
                                        class="ml-2 text-blue-600 hover:text-blue-800 text-sm font-medium"
                                    >
                                        See more
                                    </button>
                                </p>
                                <div v-else class="text-gray-600">
                                    <p class="whitespace-pre-wrap">{{ appointment.reason }}</p>
                                    <button
                                        @click="toggleReason"
                                        class="mt-2 text-blue-600 hover:text-blue-800 text-sm font-medium"
                                    >
                                        Show less
                                    </button>
                                </div>
                            </div>
                        </div>
                        <p v-if="appointment.notes"><span class="font-medium">Notes:</span> {{ appointment.notes }}</p>
                    </div>
                </div>

                <div class="bg-gray-50 rounded-lg p-4">
                    <div class="flex justify-between items-center">
                        <span class="text-lg font-semibold text-gray-900">Total Amount:</span>
                        <span class="text-2xl font-bold text-green-600">${{ appointment.amount }}</span>
                    </div>
                    <!-- Debug info - remove in production -->
                    <div v-if="appointment.client_secret" class="mt-2 text-xs text-gray-500">
                        Payment Intent: {{ appointment.payment_intent_id }}
                        <br>Client Secret: {{ appointment.client_secret.substring(0, 30) }}...
                    </div>
                    <div v-else class="mt-2 text-xs text-red-500">
                        No payment information found
                    </div>
                </div>
            </div>

            <!-- Payment Form -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">Payment Information</h2>

                <!-- Error Message -->
                <div v-if="errorMessage" class="bg-red-50 border border-red-200 rounded-md p-4 mb-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm text-red-800">{{ errorMessage }}</p>
                        </div>
                    </div>
                </div>

                <!-- Success Message -->
                <div v-if="successMessage" class="bg-green-50 border border-green-200 rounded-md p-4 mb-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm text-green-800">{{ successMessage }}</p>
                        </div>
                    </div>
                </div>

                <form @submit.prevent="processPayment" class="space-y-4">
                    <!-- Cardholder Name -->
                    <div>
                        <label for="cardholder_name" class="block text-sm font-medium text-gray-700 mb-1">
                            Cardholder Name *
                        </label>
                        <input
                            id="cardholder_name"
                            v-model="cardholderName"
                            type="text"
                            required
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            placeholder="John Doe"
                        />
                    </div>

                    <!-- Stripe Card Element -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">
                            Card Information *
                        </label>
                        <div
                            id="card-element"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            style="min-height: 40px;"
                        >
                            <!-- Stripe Elements will mount here -->
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex space-x-4 pt-6">
                        <button
                            type="button"
                            @click="goBack"
                            class="flex-1 px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        >
                            Cancel
                        </button>
                        <button
                            type="submit"
                            :disabled="loading"
                            class="flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            <span v-if="loading" class="flex items-center justify-center">
                                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                Processing...
                            </span>
                            <span v-else>
                                Pay ${{ appointment.amount }}
                            </span>
                        </button>
                    </div>
                </form>

                <!-- Security Notice -->
                <div class="mt-6 p-4 bg-gray-50 rounded-md">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm text-gray-600">
                                Your payment information is secure and encrypted. We use Stripe for payment processing.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
