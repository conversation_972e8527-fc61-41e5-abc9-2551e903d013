@extends("emails.layouts.app")

@section('content')
    <h1>🔐 Password Reset Request</h1>

    <p>Hello <span class="highlight">{{ $user->name ?? 'there' }}</span>,</p>

    <p>We received a request to reset the password for your Medroid AI account. If you made this request, click the button below to create a new password.</p>

    <div class="alert warning">
        <strong>🔒 Security Notice:</strong> This password reset request was initiated from your account. If you didn't request this, please ignore this email.
    </div>

    <div style="text-align: center; margin: 30px 0;">
        <a href="{{ $resetUrl ?? '#' }}" class="btn">🔑 Reset Your Password</a>
    </div>

    <h2>🛡️ Security Information:</h2>
    <div class="appointment-details">
        <p><strong>⏰ Expires:</strong> This link will expire in 60 minutes for your security</p>
        <p><strong>🌐 Request IP:</strong> {{ request()->ip() ?? 'Unknown' }}</p>
        <p><strong>📅 Request Time:</strong> {{ now()->format('F j, Y \a\t g:i A T') }}</p>
        <p><strong>🔗 One-Time Use:</strong> This link can only be used once</p>
    </div>

    <h2>🔐 Password Security Tips:</h2>
    <div class="benefits-list">
        <ul>
            <li><strong>🔤 Use a Strong Password:</strong> Include uppercase, lowercase, numbers, and special characters</li>
            <li><strong>📏 Make it Long:</strong> Use at least 12 characters for better security</li>
            <li><strong>🚫 Avoid Common Words:</strong> Don't use dictionary words or personal information</li>
            <li><strong>🔄 Use Unique Passwords:</strong> Don't reuse passwords from other accounts</li>
            <li><strong>🔐 Consider a Password Manager:</strong> Use tools to generate and store secure passwords</li>
        </ul>
    </div>

    <div class="alert">
        <strong>❌ Didn't Request This?</strong> If you didn't request a password reset, please ignore this email. Your password will remain unchanged, and no further action is needed.
    </div>

    <h2>🔗 Having Trouble?</h2>
    <p>If you're having trouble clicking the button, copy and paste this link into your browser:</p>
    <p style="word-break: break-all; font-size: 14px; color: #6b7280; background-color: #f9fafb; padding: 10px; border-radius: 4px; border: 1px solid #e5e7eb;">{{ $resetUrl ?? 'Reset link will appear here' }}</p>

    <h2>📞 Need Help?</h2>
    <p>If you're having trouble with the password reset process or have security concerns:</p>
    <ul>
        <li><strong>Email:</strong> <a href="mailto:<EMAIL>" style="color: #EC4899;"><EMAIL></a></li>
        <li><strong>Support:</strong> <a href="mailto:<EMAIL>" style="color: #EC4899;"><EMAIL></a></li>
        <li><strong>Help Center:</strong> <a href="{{ config('app.url') }}/help/password-reset" style="color: #EC4899;">Password Reset Guide</a></li>
    </ul>

    <p>For your security, we recommend changing your password regularly and never sharing it with anyone.</p>

    <p style="margin-top: 30px;">
        <strong>Stay secure,</strong><br>
        <span style="color: #EC4899; font-weight: 600;">The Medroid AI Security Team</span><br>
        <em>Protecting Your Health Data</em>
    </p>
@endsection