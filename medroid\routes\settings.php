<?php

use App\Http\Controllers\Settings\PasswordController;
use App\Http\Controllers\Settings\ProfileController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::middleware('auth')->group(function () {
    Route::redirect('settings', '/settings/profile');

    Route::get('settings/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('settings/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('settings/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

    // User founder club info API
    Route::get('api/user/founder-club-info', [ProfileController::class, 'getFounderClubInfo']);

    Route::get('settings/password', [PasswordController::class, 'edit'])->name('password.edit');
    Route::put('settings/password', [PasswordController::class, 'update'])->name('password.update');

    Route::get('settings/appearance', function () {
        return Inertia::render('settings/Appearance');
    })->name('appearance');

    // Patient-specific settings
    Route::middleware('role:patient')->group(function () {
        Route::get('settings/appointment-preferences', function () {
            return Inertia::render('settings/AppointmentPreferences');
        })->name('settings.appointment-preferences');

        Route::get('settings/medical-info', function () {
            return Inertia::render('settings/MedicalInfo');
        })->name('settings.medical-info');

        Route::get('settings/emergency-contacts', function () {
            return Inertia::render('settings/EmergencyContacts');
        })->name('settings.emergency-contacts');

        Route::get('settings/insurance', function () {
            return Inertia::render('settings/Insurance');
        })->name('settings.insurance');

        Route::get('settings/communication', function () {
            return Inertia::render('settings/Communication');
        })->name('settings.communication');

        Route::get('settings/privacy', function () {
            return Inertia::render('settings/Privacy');
        })->name('settings.privacy');
    });
});
