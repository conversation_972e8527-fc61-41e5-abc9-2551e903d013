<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Clinic;
use App\Models\Provider;
use App\Models\Patient;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     *
     * @return void
     */
    public function run()
    {
        $this->command->info('Starting production database seeding...');

        // Disable foreign key checks to avoid constraint issues
        Schema::disableForeignKeyConstraints();

        // Clear tables before seeding to avoid duplicate data
        $this->truncateTables([
            'appointments', 'services', 'provider_availabilities',
            'providers', 'patients', 'clinics', 'users', 'model_has_roles', 'model_has_permissions'
        ]);

        // Re-enable foreign key checks
        Schema::enableForeignKeyConstraints();

        // Run essential production seeders
        $this->call(RolesAndPermissionsSeeder::class);
        $this->call(FounderReferralCodesSeeder::class);

        // Seed email templates with proper command context
        $emailTemplateSeeder = new EmailTemplateSeeder();
        $emailTemplateSeeder->setCommand($this->command);
        $emailTemplateSeeder->run();

        // Create default clinic
        $this->seedDefaultClinic();

        // Create admin user
        $this->seedAdminUser();

        $this->command->info('Production database seeding completed successfully!');
        $this->displayCredentials();
    }

    /**
     * Truncate the specified tables.
     *
     * @param array $tables
     * @return void
     */
    protected function truncateTables(array $tables)
    {
        foreach ($tables as $table) {
            if (Schema::hasTable($table)) {
                DB::table($table)->truncate();
            }
        }
    }

    /**
     * Seed default clinic for production.
     *
     * @return void
     */
    protected function seedDefaultClinic()
    {
        $this->command->info('Creating default clinic...');

        // Create a default clinic for the application
        $defaultClinic = Clinic::create([
            'name' => 'Medroid Healthcare Center',
            'description' => 'Default healthcare clinic for providers and patients',
            'email' => '<EMAIL>',
            'phone' => '(*************',
            'address' => '123 Healthcare Drive',
            'city' => 'Medical City',
            'state' => 'CA',
            'postal_code' => '90210',
            'country' => 'US',
            'is_active' => true,
            'accepts_new_patients' => true,
            'telemedicine_enabled' => true,
            'primary_color' => '#3B82F6',
            'secondary_color' => '#EF4444',
        ]);

        $this->command->info('Default clinic created with ID: ' . $defaultClinic->id);
    }

    /**
     * Seed admin user for production.
     *
     * @return void
     */
    protected function seedAdminUser()
    {
        $this->command->info('Creating admin user...');

        // Create admin user
        $admin = User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
            'phone_number' => '+1-************',
            'is_active' => true,
        ]);
        $admin->assignRole('admin');

        // Also update the role column for compatibility
        $admin->update(['role' => 'admin']);
    }

    /**
     * Display login credentials for production.
     *
     * @return void
     */
    protected function displayCredentials()
    {
        $this->command->info('');
        $this->command->info('=== Production Login Credentials ===');
        $this->command->info('Admin: <EMAIL> / password');
        $this->command->info('');
        $this->command->warn('IMPORTANT: Change the admin password immediately after first login!');
        $this->command->info('');
    }
}