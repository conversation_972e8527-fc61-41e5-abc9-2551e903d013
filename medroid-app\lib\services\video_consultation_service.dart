import 'package:flutter/material.dart';
import 'package:medroid_app/services/api_service.dart';
import 'package:medroid_app/services/agora_service.dart';
import 'package:medroid_app/services/agora_service_factory.dart';
import 'package:medroid_app/services/device_info_service.dart';
import 'dart:async';

/// A service to handle video consultations using Agora
/// Implements the same mechanism as the Laravel web version
class VideoConsultationService {
  final ApiService _apiService;
  late final AgoraService _agoraService;

  // Session state
  Map<String, dynamic>? _sessionData;
  String? _connectionStatus; // disconnected, connecting, connected, waiting
  Timer? _statusPollingTimer;

  // Callbacks for UI updates
  Function(String status)? onConnectionStatusChanged;
  Function(Map<String, dynamic> participants)? onParticipantsChanged;
  Function(String error)? onError;
  Function(String message)? onSuccess;

  VideoConsultationService(this._apiService) {
    _agoraService = AgoraServiceFactory.createAgoraService(_apiService);
    _connectionStatus = 'disconnected';
  }

  /// Initialize a video consultation for an appointment
  /// This should be called by providers to start the session
  Future<Map<String, dynamic>> initializeVideoSession(
      String appointmentId) async {
    try {
      _updateConnectionStatus('connecting');

      // Get device information
      final deviceInfo = await DeviceInfoService.getDeviceInfo();

      final response = await _apiService.initializeVideoSession(
        appointmentId,
        deviceInfo: deviceInfo,
      );

      if (response['success']) {
        _showSuccess('Video session initialized successfully');
      } else {
        _showError(response['message'] ?? 'Failed to initialize session');
      }

      return response;
    } catch (e) {
      debugPrint('Error initializing video session: $e');
      _showError('Failed to initialize video session: $e');
      return {
        'success': false,
        'message': 'Failed to initialize video session: $e',
      };
    }
  }

  /// Initialize video call (similar to web version)
  Future<Map<String, dynamic>> initializeCall({
    required String appointmentId,
    required String userRole,
    Map<String, dynamic>? appointment,
  }) async {
    try {
      _updateConnectionStatus('connecting');
      debugPrint('Initializing video call for appointment: $appointmentId');

      // Check if appointment has video session and user is patient
      if (appointment != null &&
          appointment['video_session_id'] == null &&
          userRole == 'patient') {
        debugPrint(
            'Patient joining but provider hasnt started session yet - entering waiting mode');
        _updateConnectionStatus('waiting');
        _startWaitingForProvider(appointmentId);
        return {
          'success': true,
          'status': 'waiting',
          'message': 'Waiting for provider to start session',
        };
      }

      // Get session data from backend
      final deviceInfo = await DeviceInfoService.getDeviceInfo();
      final response = await _apiService.getVideoSessionData(
        appointmentId,
        deviceInfo: deviceInfo,
      );

      // If no session exists and user is patient, wait for provider
      if (!response['success'] && userRole == 'patient') {
        debugPrint('No session data available - patient waiting for provider');
        _updateConnectionStatus('waiting');
        _startWaitingForProvider(appointmentId);
        return {
          'success': true,
          'status': 'waiting',
          'message': 'Waiting for provider to start session',
        };
      }

      if (!response['success']) {
        _showError(response['message'] ?? 'Failed to get session data');
        return response;
      }

      debugPrint('Session data received: ${response['session_data']}');
      _sessionData = response['session_data'];

      // Start the actual video call with session data
      return await _startVideoCallWithSession();
    } catch (e) {
      debugPrint('Error initializing call: $e');
      _showError('Failed to initialize call: $e');
      _updateConnectionStatus('disconnected');
      return {
        'success': false,
        'message': 'Failed to initialize call: $e',
      };
    }
  }

  /// Join a video consultation using Agora
  Future<bool> joinVideoConsultation({
    required String appointmentId,
    required bool isProvider,
    required String userName,
  }) async {
    try {
      debugPrint('VideoConsultationService: Joining Agora video consultation');
      debugPrint('Appointment ID: $appointmentId');
      debugPrint('Is Provider: $isProvider');
      debugPrint('User Name: $userName');

      // Initialize Agora
      await _agoraService.initialize();

      // Join the channel
      final success = await _agoraService.joinChannel(
        appointmentId: appointmentId,
        isProvider: isProvider,
      );

      return success;
    } catch (e) {
      debugPrint('Error joining video consultation: $e');
      return false;
    }
  }

  /// Start waiting for provider to start session (patient only)
  void _startWaitingForProvider(String appointmentId) {
    debugPrint('Starting to wait for provider session...');

    // Set up interval to check for provider session
    _statusPollingTimer =
        Timer.periodic(const Duration(seconds: 2), (timer) async {
      try {
        debugPrint('Checking for provider session...');
        final response = await _apiService.getVideoSessionData(appointmentId);

        if (response['success'] && response['session_data'] != null) {
          debugPrint(
              'Provider has started session! Initializing video call...');
          timer.cancel();
          _sessionData = response['session_data'];

          // Now start the actual video call
          await _startVideoCallWithSession();
        }
      } catch (e) {
        debugPrint('Error checking for provider session: $e');
      }
    });
  }

  /// Start video call with existing session data
  Future<Map<String, dynamic>> _startVideoCallWithSession() async {
    try {
      _updateConnectionStatus('connecting');

      if (_sessionData == null) {
        throw Exception('No session data available');
      }

      // Initialize Agora with session data
      await _agoraService.initialize();

      // Set up Agora event listeners
      _setupAgoraEventListeners();

      // Join the Agora channel using session data
      final success = await _joinAgoraChannel();

      if (success) {
        _updateConnectionStatus('connected');
        _showSuccess('Connected to video call');

        // Start session status polling
        _startSessionStatusPolling();

        return {
          'success': true,
          'message': 'Successfully joined video call',
        };
      } else {
        throw Exception('Failed to join Agora channel');
      }
    } catch (e) {
      debugPrint('Error starting video call with session: $e');
      _showError('Failed to start video call: $e');
      _updateConnectionStatus('disconnected');
      return {
        'success': false,
        'message': 'Failed to start video call: $e',
      };
    }
  }

  /// Join Agora channel using session data
  Future<bool> _joinAgoraChannel() async {
    try {
      if (_sessionData == null) return false;

      // Extract session information
      final channelName = _sessionData!['channel'];
      final uid = _sessionData!['uid'];
      final token = _sessionData!['token'];

      debugPrint('=== JOINING AGORA CHANNEL ===');
      debugPrint('Channel: $channelName');
      debugPrint('UID: $uid');
      debugPrint('Token length: ${token?.length ?? 0}');
      debugPrint('============================');

      // Set session data in Agora service
      _agoraService.channelName = channelName;
      _agoraService.uid = uid;
      _agoraService.token = token;

      // Join the channel using Agora service
      return await _agoraService.joinChannelWithSessionData();
    } catch (e) {
      debugPrint('Error joining Agora channel: $e');
      return false;
    }
  }

  /// Set up Agora event listeners
  void _setupAgoraEventListeners() {
    _agoraService.onUserJoined = (uid) {
      debugPrint('User joined: $uid');
      _notifyBackendJoined();
    };

    _agoraService.onUserLeft = (uid) {
      debugPrint('User left: $uid');
    };

    _agoraService.onConnectionStateChanged = (state) {
      debugPrint('Connection state changed: $state');
    };

    _agoraService.onError = (error) {
      debugPrint('Agora error: $error');
      _showError('Video call error: $error');
    };
  }

  /// Start session status polling
  void _startSessionStatusPolling() {
    _statusPollingTimer?.cancel();
    _statusPollingTimer =
        Timer.periodic(const Duration(seconds: 2), (timer) async {
      // Implementation for status polling will be added
    });
  }

  /// Notify backend that user has joined
  Future<void> _notifyBackendJoined() async {
    // Implementation will be added
  }

  /// Update connection status and notify UI
  void _updateConnectionStatus(String status) {
    _connectionStatus = status;
    onConnectionStatusChanged?.call(status);
    debugPrint('Connection status updated: $status');
  }

  /// Show success message to UI
  void _showSuccess(String message) {
    onSuccess?.call(message);
    debugPrint('Success: $message');
  }

  /// Show error message to UI
  void _showError(String message) {
    onError?.call(message);
    debugPrint('Error: $message');
  }

  /// Leave video session
  Future<Map<String, dynamic>> leaveSession(String appointmentId) async {
    try {
      // Stop polling
      _stopPolling();

      // Leave Agora channel
      await _agoraService.leaveChannel();

      // Notify backend
      final response = await _apiService.leaveVideoSession(appointmentId);

      _updateConnectionStatus('disconnected');
      _sessionData = null;

      return response;
    } catch (e) {
      debugPrint('Error leaving session: $e');
      return {
        'success': false,
        'message': 'Failed to leave session: $e',
      };
    }
  }

  /// End video session
  Future<Map<String, dynamic>> endSession(String appointmentId) async {
    try {
      // Stop polling
      _stopPolling();

      // Leave Agora channel
      await _agoraService.leaveChannel();

      // End session on backend
      final response = await _apiService.endVideoSession(appointmentId);

      _updateConnectionStatus('disconnected');
      _sessionData = null;

      return response;
    } catch (e) {
      debugPrint('Error ending session: $e');
      return {
        'success': false,
        'message': 'Failed to end session: $e',
      };
    }
  }

  /// Stop all polling timers
  void _stopPolling() {
    _statusPollingTimer?.cancel();
    _statusPollingTimer = null;
  }

  // Getters for current state
  String get connectionStatus => _connectionStatus ?? 'disconnected';
  Map<String, dynamic>? get sessionData => _sessionData;
  bool get isConnected => _connectionStatus == 'connected';
  bool get isWaiting => _connectionStatus == 'waiting';

  /// Dispose resources
  void dispose() {
    _stopPolling();
    _agoraService.dispose();
    _sessionData = null;
    _connectionStatus = 'disconnected';
  }
}
