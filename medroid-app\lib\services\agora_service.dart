import 'package:flutter/material.dart';
import 'package:agora_rtc_engine/agora_rtc_engine.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:medroid_app/services/api_service.dart';
import 'package:medroid_app/services/device_info_service.dart';
import 'package:medroid_app/utils/env_config.dart';

/// Simplified Agora service for video consultations
class AgoraService {
  final ApiService _apiService;

  // Agora engine instance
  RtcEngine? engine;

  // Session data
  String? channelName;
  int? uid;
  String? token;
  String? appId;

  // Audio/Video state
  bool _isAudioEnabled = true;
  bool _isVideoEnabled = true;

  // Callbacks
  Function(int uid)? onUserJoined;
  Function(int uid)? onUserLeft;
  Function(ConnectionStateType state)? onConnectionStateChanged;
  Function(String error)? onError;

  // Constructor
  AgoraService(this._apiService);

  /// Initialize the Agora engine
  Future<void> initialize() async {
    try {
      // Mobile-only implementation

      // Create RTC engine instance (mobile only)
      engine = createAgoraRtcEngine();

      // Get App ID from environment configuration
      appId = EnvConfig.agoraAppId;

      debugPrint('Initializing Agora engine with App ID: $appId');

      // Initialize the engine
      await engine!.initialize(
        RtcEngineContext(
          appId: appId!,
          channelProfile: ChannelProfileType.channelProfileLiveBroadcasting,
        ),
      );

      // Register event handlers
      engine!.registerEventHandler(
        RtcEngineEventHandler(
          onJoinChannelSuccess: (RtcConnection connection, int elapsed) {
            debugPrint(
              'Local user joined: ${connection.channelId} with UID: $uid',
            );
          },
          onUserJoined: (RtcConnection connection, int remoteUid, int elapsed) {
            debugPrint('Remote user joined: $remoteUid (Local UID: $uid)');
            if (remoteUid != uid) {
              onUserJoined?.call(remoteUid);
            }
          },
          onUserOffline: (
            RtcConnection connection,
            int remoteUid,
            UserOfflineReasonType reason,
          ) {
            debugPrint('Remote user left: $remoteUid, reason: $reason');
            if (remoteUid != uid) {
              onUserLeft?.call(remoteUid);
            }
          },
          onConnectionStateChanged: (
            RtcConnection connection,
            ConnectionStateType state,
            ConnectionChangedReasonType reason,
          ) {
            debugPrint('Connection state changed: $state, reason: $reason');
            onConnectionStateChanged?.call(state);
          },
          onError: (ErrorCodeType err, String msg) {
            debugPrint('Agora error: $err - $msg');
            onError?.call('$err: $msg');
          },
        ),
      );

      // Enable audio and video
      await engine!.enableVideo();
      await engine!.enableAudio();
      await engine!.setClientRole(role: ClientRoleType.clientRoleBroadcaster);

      debugPrint('Agora service initialized successfully');
    } catch (e) {
      debugPrint('Error initializing Agora engine: $e');
      rethrow;
    }
  }

  /// Join a video consultation channel
  Future<bool> joinChannel({
    required String appointmentId,
    required bool isProvider,
  }) async {
    try {
      // Mobile-only video consultation

      // Request permissions (mobile only)
      Map<Permission, PermissionStatus> statuses =
          await [Permission.camera, Permission.microphone].request();

      debugPrint('Permission status: $statuses');

      // Get device information
      final deviceInfo = await DeviceInfoService.getDeviceInfo();

      // Get session data from our new simplified API
      final sessionData = await _apiService.getVideoSessionData(appointmentId,
          deviceInfo: deviceInfo);

      if (!sessionData['success']) {
        debugPrint('Failed to get session data: ${sessionData['message']}');
        return false;
      }

      final sessionInfo = sessionData['session_data'];

      // Extract session information
      channelName = sessionInfo['channel'];
      uid = sessionInfo['uid'];
      token = sessionInfo['token'];

      debugPrint('=== JOINING AGORA CHANNEL ===');
      debugPrint('Channel: $channelName');
      debugPrint('UID: $uid');
      debugPrint('Token length: ${token?.length ?? 0}');
      debugPrint('User role: ${isProvider ? 'Provider' : 'Patient'}');
      debugPrint('============================');

      // Join the channel
      await engine!.joinChannel(
        token: token!,
        channelId: channelName!,
        uid: uid!,
        options: const ChannelMediaOptions(
          channelProfile: ChannelProfileType.channelProfileLiveBroadcasting,
          clientRoleType: ClientRoleType.clientRoleBroadcaster,
        ),
      );

      debugPrint('Successfully joined Agora channel');
      return true;
    } catch (e) {
      debugPrint('Error joining channel: $e');
      return false;
    }
  }

  /// Join channel with pre-existing session data
  Future<bool> joinChannelWithSessionData() async {
    try {
      if (channelName == null || uid == null || token == null) {
        debugPrint('Missing session data for joining channel');
        return false;
      }

      // Request permissions (mobile only)
      Map<Permission, PermissionStatus> statuses =
          await [Permission.camera, Permission.microphone].request();

      debugPrint('Permission status: $statuses');

      // Join the channel with existing session data
      await engine!.joinChannel(
        token: token!,
        channelId: channelName!,
        uid: uid!,
        options: const ChannelMediaOptions(
          channelProfile: ChannelProfileType.channelProfileLiveBroadcasting,
          clientRoleType: ClientRoleType.clientRoleBroadcaster,
        ),
      );

      debugPrint('Successfully joined Agora channel with session data');
      return true;
    } catch (e) {
      debugPrint('Error joining channel with session data: $e');
      return false;
    }
  }

  /// Leave the channel
  Future<void> leaveChannel() async {
    try {
      debugPrint('Leaving Agora channel');
      if (engine != null) {
        await engine!.leaveChannel();
        debugPrint('Successfully left channel');
      }

      // Reset session data
      channelName = null;
      uid = null;
      token = null;
    } catch (e) {
      debugPrint('Error leaving channel: $e');
    }
  }

  /// Toggle local audio (mute/unmute)
  Future<bool> toggleLocalAudio() async {
    try {
      if (engine == null) {
        debugPrint('Engine is null, cannot toggle audio');
        return false;
      }

      _isAudioEnabled = !_isAudioEnabled;
      await engine!.enableLocalAudio(_isAudioEnabled);
      debugPrint('Audio ${_isAudioEnabled ? 'enabled' : 'disabled'}');

      return _isAudioEnabled;
    } catch (e) {
      debugPrint('Error toggling audio: $e');
      return false;
    }
  }

  /// Toggle local video (enable/disable)
  Future<bool> toggleLocalVideo() async {
    try {
      if (engine == null) {
        debugPrint('Engine is null, cannot toggle video');
        return false;
      }

      _isVideoEnabled = !_isVideoEnabled;
      await engine!.enableLocalVideo(_isVideoEnabled);
      debugPrint('Video ${_isVideoEnabled ? 'enabled' : 'disabled'}');

      return _isVideoEnabled;
    } catch (e) {
      debugPrint('Error toggling video: $e');
      return false;
    }
  }

  /// Switch camera
  Future<void> switchCamera() async {
    try {
      if (engine != null) {
        await engine!.switchCamera();
        debugPrint('Camera switched');
      }
    } catch (e) {
      debugPrint('Error switching camera: $e');
    }
  }

  /// Dispose the service
  void dispose() {
    try {
      leaveChannel();
    } catch (e) {
      debugPrint('Error leaving channel during dispose: $e');
    }

    try {
      engine?.release();
      engine = null;
    } catch (e) {
      debugPrint('Error disposing engine: $e');
    }

    // Reset all state
    channelName = null;
    uid = null;
    token = null;
    _isAudioEnabled = true;
    _isVideoEnabled = true;

    debugPrint('Agora service disposed');
  }

  // Getters for current state
  bool get isAudioEnabled => _isAudioEnabled;
  bool get isVideoEnabled => _isVideoEnabled;
  bool get isConnected => channelName != null && uid != null;
}
