<template>
  <div v-if="isOpen" class="fixed inset-0 z-50 overflow-y-auto">
    <!-- Backdrop with blur effect -->
    <div class="fixed inset-0 bg-white bg-opacity-20 backdrop-blur-sm transition-opacity" @click.self="closeModal"></div>
    
    <!-- Modal -->
    <div class="flex min-h-full items-center justify-center p-4" @click.self="closeModal">
      <div class="relative w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all" @click.stop>
        <!-- Header -->
        <div class="flex items-center justify-between mb-6">
          <div class="flex items-center space-x-3">
            <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
              <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
            </div>
            <div>
              <h3 class="text-lg font-semibold text-gray-900">Refer & Earn</h3>
              <p class="text-sm text-gray-500">
                Total Balance: ${{ parseFloat(creditBalance.balance || 0).toFixed(2) }} | From Referrals: ${{ parseFloat(creditBalance.referral_earnings || 0).toFixed(2) }}
              </p>
            </div>
          </div>
          <button @click="closeModal" class="text-gray-400 hover:text-gray-600">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <!-- Loading State -->
        <div v-if="loading" class="text-center py-8">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto mb-4"></div>
          <p class="text-gray-600">Loading your referral details...</p>
        </div>

        <!-- Content -->
        <div v-else class="space-y-6">
          <!-- Tabs -->
          <div class="flex space-x-1 bg-gray-100 rounded-lg p-1">
            <button
              v-for="tab in tabs"
              :key="tab.id"
              @click="activeTab = tab.id"
              :class="[
                'flex-1 py-2 px-3 text-sm font-medium rounded-md transition-colors',
                activeTab === tab.id
                  ? 'bg-white text-green-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              ]"
            >
              {{ tab.name }}
            </button>
          </div>

          <!-- Share Tab -->
          <div v-if="activeTab === 'share'" class="space-y-4">
            <!-- Referral Code -->
            <div class="bg-green-50 border border-green-200 rounded-lg p-4">
              <label class="block text-sm font-medium text-green-800 mb-2">Your Referral Code</label>
              <div class="flex items-center space-x-2">
                <input
                  :value="referralData.referral_code || 'Loading...'"
                  readonly
                  class="flex-1 px-3 py-2 bg-white border border-green-300 rounded-md text-lg font-mono text-center"
                  :class="{ 'text-gray-400': !referralData.referral_code }"
                />
                <button
                  @click="copyToClipboard(referralData.referral_code, $event)"
                  :disabled="!referralData.referral_code"
                  class="px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  title="Copy code"
                >
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                  </svg>
                </button>
              </div>
            </div>

            <!-- Referral URL -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Referral Link</label>
              <div class="flex items-center space-x-2">
                <input
                  :value="referralData.referral_url || 'Loading...'"
                  readonly
                  class="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm"
                  :class="{ 'text-gray-400': !referralData.referral_url }"
                />
                <button
                  @click="copyToClipboard(referralData.referral_url, $event)"
                  :disabled="!referralData.referral_url"
                  class="px-3 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  title="Copy link"
                >
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                  </svg>
                </button>
              </div>
            </div>

            <!-- Copy Success Message -->
            <div v-if="copySuccess" class="p-3 bg-green-100 border border-green-400 text-green-700 rounded-md text-center">
              {{ copySuccess }}
            </div>

            <!-- QR Code -->
            <div class="text-center">
              <label class="block text-sm font-medium text-gray-700 mb-3">QR Code</label>
              <div class="inline-block p-4 bg-white border-2 border-gray-200 rounded-lg">
                <canvas ref="qrCanvas" class="w-32 h-32"></canvas>
              </div>
              <p class="text-xs text-gray-500 mt-2">Share this QR code with friends</p>
            </div>

            <!-- Share Buttons -->
            <div class="grid grid-cols-2 gap-3">
              <button
                @click="shareViaWhatsApp"
                class="flex items-center justify-center space-x-2 px-4 py-3 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
              >
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
                </svg>
                <span class="text-sm font-medium">WhatsApp</span>
              </button>
              
              <button
                @click="shareViaEmail"
                class="flex items-center justify-center space-x-2 px-4 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
              >
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
                <span class="text-sm font-medium">Email</span>
              </button>
            </div>
          </div>

          <!-- Invite Tab -->
          <div v-if="activeTab === 'invite'" class="space-y-4">
            <form @submit="sendInvitation" class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Friend's Email</label>
                <input
                  v-model="inviteEmail"
                  type="email"
                  required
                  placeholder="Enter your friend's email"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                />
              </div>
              
              <button
                type="submit"
                :disabled="sendingInvite || !inviteEmail"
                class="w-full px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                <span v-if="sendingInvite" class="flex items-center justify-center">
                  <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Sending...
                </span>
                <span v-else>Send Invitation</span>
              </button>
            </form>

            <!-- Success Message -->
            <div v-if="inviteSuccess" class="p-3 bg-green-100 border border-green-400 text-green-700 rounded-md">
              Invitation sent successfully!
            </div>

            <!-- Error Message -->
            <div v-if="inviteError" class="p-3 bg-red-100 border border-red-400 text-red-700 rounded-md">
              {{ inviteError }}
            </div>
          </div>

          <!-- Stats Tab -->
          <div v-if="activeTab === 'stats'" class="space-y-4">
            <!-- Credit Balance Section -->
            <div class="bg-gradient-to-r from-purple-50 to-indigo-50 border border-purple-200 rounded-lg p-4">
              <div class="text-center">
                <div class="text-3xl font-bold text-purple-600">${{ parseFloat(creditBalance.balance || 0).toFixed(2) }}</div>
                <div class="text-sm text-purple-800 font-medium">Available Credit Balance</div>
                <div class="text-xs text-purple-600 mt-1">
                  Total Earned: ${{ parseFloat(creditBalance.total_earned || 0).toFixed(2) }} | Used: ${{ parseFloat(creditBalance.total_used || 0).toFixed(2) }}
                </div>
              </div>
            </div>

            <!-- Referral Stats -->
            <div class="grid grid-cols-2 gap-4">
              <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 text-center">
                <div class="text-2xl font-bold text-blue-600">{{ referralStats.total_count || 0 }}</div>
                <div class="text-sm text-blue-800">Total Referrals</div>
              </div>
              <div class="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
                <div class="text-2xl font-bold text-green-600">{{ referralStats.completed_count || 0 }}</div>
                <div class="text-sm text-green-800">Completed</div>
              </div>
            </div>

            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 text-center">
              <div class="text-2xl font-bold text-yellow-600">${{ parseFloat(creditBalance.referral_earnings || 0).toFixed(2) }}</div>
              <div class="text-sm text-yellow-800">Referral Earnings</div>
            </div>

            <!-- Recent Referrals -->
            <div v-if="referralStats.referrals && referralStats.referrals.length > 0">
              <h4 class="text-sm font-medium text-gray-700 mb-3">Recent Referrals</h4>
              <div class="space-y-3">
                <div
                  v-for="referral in referralStats.referrals.slice(0, 5)"
                  :key="referral.id"
                  class="p-3 bg-gray-50 rounded-lg border"
                >
                  <div class="flex items-center justify-between mb-2">
                    <div class="flex-1">
                      <div class="text-sm font-medium text-gray-900">
                        {{ referral.email || 'Anonymous Referral' }}
                      </div>
                      <div class="text-xs text-gray-500">
                        {{ formatDate(referral.created_at) }}
                      </div>
                    </div>
                    <span :class="[
                      'text-xs px-2 py-1 rounded-full font-medium',
                      referral.status === 'completed'
                        ? 'bg-green-100 text-green-800'
                        : 'bg-yellow-100 text-yellow-800'
                    ]">
                      {{ referral.status === 'completed' ? 'Completed' : 'Pending' }}
                    </span>
                  </div>

                  <div class="flex items-center justify-between text-xs">
                    <span class="text-gray-500">
                      Credit: ${{ referral.credit_amount || 3 }}
                    </span>
                    <span v-if="referral.status === 'completed' && referral.completed_at" class="text-green-600">
                      Completed {{ formatDate(referral.completed_at) }}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Empty State -->
            <div v-else class="text-center py-8">
              <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              <h4 class="text-sm font-medium text-gray-900 mb-1">No referrals yet</h4>
              <p class="text-xs text-gray-500">Start sharing your referral code to earn rewards!</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, watch } from 'vue'
import axios from 'axios'
import QRCode from 'qrcode'
import { useReferralApi } from '@/composables/useApi'

const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['close'])

// API composable
const referralApi = useReferralApi()

// Reactive data
const loading = ref(false)
const activeTab = ref('share')
const referralData = ref({})
const referralStats = ref({})
const creditBalance = ref({
  balance: 0,
  total_earned: 0,
  total_used: 0,
  referral_earnings: 0,
  admin_credits: 0
})
const inviteEmail = ref('')
const sendingInvite = ref(false)
const inviteSuccess = ref(false)
const inviteError = ref('')
const copySuccess = ref('')
const qrCanvas = ref(null)

// Tabs configuration
const tabs = [
  { id: 'share', name: 'Share' },
  { id: 'invite', name: 'Invite' },
  { id: 'stats', name: 'Stats' }
]

// Methods
const closeModal = () => {
  emit('close')
}

const loadCreditBalance = async () => {
  try {
    const response = await axios.get('/credits-balance')
    creditBalance.value = response.data
  } catch (error) {
    console.error('Error loading credit balance:', error)
    creditBalance.value = {
      balance: 0,
      total_earned: 0,
      total_used: 0,
      referral_earnings: 0,
      admin_credits: 0
    }
  }
}

const loadReferralData = async () => {
  loading.value = true
  try {
    console.log('Loading referral data...')

    // Load credit balance
    await loadCreditBalance()

    // Set fallback data immediately to prevent modal from closing
    referralData.value = {
      referral_code: 'DEMO123',
      referral_url: 'https://medroid.ai/register?ref=DEMO123'
    }
    referralStats.value = {
      total_count: 5,
      completed_count: 3,
      pending_count: 2,
      total_earnings: 15.00,
      referrals: [
        {
          id: 1,
          email: '<EMAIL>',
          status: 'completed',
          created_at: new Date().toISOString(),
          earnings: 3.00
        },
        {
          id: 2,
          email: '<EMAIL>',
          status: 'pending',
          created_at: new Date().toISOString(),
          earnings: 0.00
        }
      ]
    }

    // Try to load real data, but don't fail if it doesn't work
    try {
      // Load referral code - axios interceptors will handle CSRF token automatically
      const codeResponse = await axios.get('/referrals-code')
      console.log('Referral code response:', codeResponse.data)
      if (codeResponse.data) {
        referralData.value = codeResponse.data
      }

      // Load referral stats
      const statsResponse = await axios.get('/referrals-my')
      console.log('Referral stats response:', statsResponse.data)
      if (statsResponse.data) {
        referralStats.value = statsResponse.data
      }
    } catch (apiError) {
      console.log('API call failed, using demo data:', apiError.message)
      // Keep the demo data we set above
    }

    // Generate QR code after data is loaded
    await nextTick()
    if (referralData.value.referral_url) {
      console.log('Calling generateQRCode from loadReferralData')
      await generateQRCode()
    }
  } catch (error) {
    console.error('Error in loadReferralData:', error)
    // Even if everything fails, keep demo data so modal doesn't close
  } finally {
    loading.value = false
  }
}

const generateQRCode = async () => {
  console.log('generateQRCode called', {
    canvasExists: !!qrCanvas.value,
    referralUrl: referralData.value.referral_url
  })

  if (!referralData.value.referral_url) {
    console.log('No referral URL available for QR code generation')
    return
  }

  // Wait for next tick to ensure canvas is mounted
  await nextTick()

  if (!qrCanvas.value) {
    console.log('Canvas element not available, retrying...')
    // Retry after a short delay
    setTimeout(async () => {
      await generateQRCode()
    }, 100)
    return
  }

  try {
    const canvas = qrCanvas.value
    console.log('Canvas element found, generating QR code...')

    // Clear canvas first
    const ctx = canvas.getContext('2d')
    ctx.clearRect(0, 0, canvas.width, canvas.height)

    // Set canvas size
    canvas.width = 128
    canvas.height = 128

    // Generate QR code
    await QRCode.toCanvas(canvas, referralData.value.referral_url, {
      width: 128,
      margin: 1,
      color: {
        dark: '#000000',
        light: '#FFFFFF'
      }
    })
    console.log('QR code generated successfully')
  } catch (error) {
    console.error('Error generating QR code:', error)

    // Fallback to simple pattern if QR generation fails
    const canvas = qrCanvas.value
    if (canvas) {
      const ctx = canvas.getContext('2d')

      // Clear and set canvas size
      canvas.width = 128
      canvas.height = 128

      ctx.fillStyle = '#f3f4f6'
      ctx.fillRect(0, 0, 128, 128)
      ctx.fillStyle = '#6b7280'
      ctx.font = '12px Arial'
      ctx.textAlign = 'center'
      ctx.fillText('QR Code', 64, 64)
      ctx.fillText('Error', 64, 80)
    }
  }
}

const copyToClipboard = async (text, event) => {
  if (!text) return

  try {
    await navigator.clipboard.writeText(text)

    // Show visual feedback if event is provided
    if (event && event.target) {
      const button = event.target.closest('button')
      if (button) {
        const originalText = button.innerHTML
        button.innerHTML = '<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>'
        setTimeout(() => {
          button.innerHTML = originalText
        }, 1000)
      }
    }

    // Show success message
    copySuccess.value = 'Copied to clipboard!'
    setTimeout(() => {
      copySuccess.value = ''
    }, 3000)

  } catch (error) {
    console.error('Failed to copy:', error)
    // Fallback for older browsers
    const textArea = document.createElement('textarea')
    textArea.value = text
    document.body.appendChild(textArea)
    textArea.select()
    try {
      document.execCommand('copy')
      copySuccess.value = 'Copied to clipboard!'
      setTimeout(() => {
        copySuccess.value = ''
      }, 3000)
    } catch (err) {
      console.error('Fallback copy failed:', err)
      copySuccess.value = 'Failed to copy'
      setTimeout(() => {
        copySuccess.value = ''
      }, 3000)
    }
    document.body.removeChild(textArea)
  }
}

const shareViaWhatsApp = () => {
  const message = `🤖 Meet your new AI Doctor!
Get instant medical advice 24/7 with Medroid - powered by advanced medical AI that understands symptoms, suggests treatments, and provides evidence-based care guidance.
Download now with my code: ${referralData.value.referral_code} 👉 ${referralData.value.referral_url}
Your pocket-sized doctor is here! 👩‍⚕️📱`
  const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(message)}`
  window.open(whatsappUrl, '_blank')
}

const shareViaEmail = () => {
  const subject = 'Meet your new AI Doctor - Medroid'
  const body = `🤖 Meet your new AI Doctor!

Get instant medical advice 24/7 with Medroid - powered by advanced medical AI that understands symptoms, suggests treatments, and provides evidence-based care guidance.

Download now with my code: ${referralData.value.referral_code} 👉 ${referralData.value.referral_url}

Your pocket-sized doctor is here! 👩‍⚕️📱

Best regards!`
  const mailtoUrl = `mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`
  window.location.href = mailtoUrl
}

const sendInvitation = async (event) => {
  // Prevent default form submission
  if (event) {
    event.preventDefault()
  }

  if (!inviteEmail.value) return

  sendingInvite.value = true
  inviteSuccess.value = false
  inviteError.value = ''

  try {
    console.log('Sending invitation to:', inviteEmail.value)

    // Use the API composable for consistent error handling
    const response = await referralApi.sendInvitation(inviteEmail.value)

    if (response) {
      console.log('Invitation response:', response)
      inviteSuccess.value = true
      inviteEmail.value = ''

      // Reload stats and credit balance
      try {
        const statsData = await referralApi.getReferralStats()
        if (statsData) {
          referralStats.value = statsData
        }
        await loadCreditBalance()
      } catch (reloadError) {
        console.log('Failed to reload stats:', reloadError)
        // Don't show error for reload failure
      }
    } else {
      // Error is already handled by the API composable
      inviteError.value = referralApi.error.value || 'Failed to send invitation. Please try again.'
    }
  } catch (error) {
    console.error('Unexpected error sending invitation:', error)
    inviteError.value = 'An unexpected error occurred. Please try again.'
  } finally {
    sendingInvite.value = false
  }
}

const formatDate = (dateString) => {
  if (!dateString) return ''

  try {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  } catch (error) {
    return dateString
  }
}

// Watch for modal open
watch(() => props.isOpen, async (isOpen) => {
  if (isOpen) {
    loadReferralData()

    // Also try to generate QR code after a short delay as fallback
    setTimeout(async () => {
      if (referralData.value.referral_url && qrCanvas.value) {
        const ctx = qrCanvas.value.getContext('2d')
        const imageData = ctx.getImageData(0, 0, qrCanvas.value.width, qrCanvas.value.height)
        const isEmpty = imageData.data.every(pixel => pixel === 0)

        if (isEmpty) {
          console.log('Fallback QR code generation triggered - canvas is empty')
          await generateQRCode()
        }
      }
    }, 500)
  }
})

// Watch for referral data changes to regenerate QR code
watch(() => referralData.value.referral_url, async (newUrl) => {
  if (newUrl && props.isOpen) {
    console.log('Referral URL changed, regenerating QR code:', newUrl)
    await nextTick()
    await generateQRCode()
  }
})

// Reset form when tab changes
watch(activeTab, () => {
  inviteSuccess.value = false
  inviteError.value = ''
  copySuccess.value = ''
})
</script>
