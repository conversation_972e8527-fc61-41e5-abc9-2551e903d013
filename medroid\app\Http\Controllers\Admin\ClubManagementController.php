<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\FounderReferralCode;
use App\Models\UserClub;
use App\Models\User;
use App\Models\WaitlistInvitation;
use App\Services\ClubService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;

class ClubManagementController extends Controller
{
    protected $clubService;
    protected $waitlistService;

    public function __construct(ClubService $clubService, \App\Services\WaitlistService $waitlistService)
    {
        $this->clubService = $clubService;
        $this->waitlistService = $waitlistService;
    }

    /**
     * Get all founder referral codes
     */
    public function getFounderCodes(Request $request)
    {
        if (!$request->user()->can('view settings')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $query = FounderReferralCode::query();

        // Apply search filter
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('code', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Apply status filter
        if ($request->has('status')) {
            if ($request->status === 'active') {
                $query->where('is_active', true);
            } elseif ($request->status === 'inactive') {
                $query->where('is_active', false);
            }
        }

        $codes = $query->orderBy('created_at', 'desc')
            ->paginate($request->get('per_page', 15));

        // Add usage statistics
        $codes->getCollection()->transform(function ($code) {
            $stats = $this->clubService->getFounderCodeStats($code);
            return array_merge($code->toArray(), [
                'usage_stats' => $stats,
                'usage_percentage' => $code->max_uses ? 
                    round(($code->current_uses / $code->max_uses) * 100, 1) : null,
            ]);
        });

        return response()->json($codes);
    }

    /**
     * Create a new founder referral code
     */
    public function createFounderCode(Request $request)
    {
        if (!$request->user()->can('edit settings')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $validator = Validator::make($request->all(), [
            'code' => 'required|string|max:50|unique:founder_referral_codes,code',
            'description' => 'nullable|string|max:255',
            'max_uses' => 'nullable|integer|min:1',
            'expires_at' => 'nullable|date|after:now',
            'is_active' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            $code = $this->clubService->createFounderCode($request->all());
            
            return response()->json([
                'message' => 'Founder code created successfully',
                'code' => $code,
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error creating founder code',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update a founder referral code
     */
    public function updateFounderCode(Request $request, $id)
    {
        if (!$request->user()->can('edit settings')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $code = FounderReferralCode::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'code' => 'sometimes|required|string|max:50|unique:founder_referral_codes,code,' . $id,
            'description' => 'nullable|string|max:255',
            'max_uses' => 'nullable|integer|min:1',
            'expires_at' => 'nullable|date',
            'is_active' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            $code->update($request->all());
            
            return response()->json([
                'message' => 'Founder code updated successfully',
                'code' => $code,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error updating founder code',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Delete a founder referral code
     */
    public function deleteFounderCode(Request $request, $id)
    {
        if (!$request->user()->can('delete settings')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $code = FounderReferralCode::findOrFail($id);

        // Check if code has been used
        if ($code->current_uses > 0) {
            return response()->json([
                'message' => 'Cannot delete code that has been used',
            ], 400);
        }

        $code->delete();

        return response()->json([
            'message' => 'Founder code deleted successfully',
        ]);
    }

    /**
     * Get club members
     */
    public function getClubMembers(Request $request)
    {
        if (!$request->user()->can('view users')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $query = UserClub::with(['user', 'founderCode']);

        // Filter by club type
        if ($request->has('club_type')) {
            $query->where('club_type', $request->club_type);
        }

        // Filter by verification status
        if ($request->has('verified')) {
            $query->where('is_verified', $request->boolean('verified'));
        }

        // Search by user name or email
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->whereHas('user', function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        $members = $query->orderBy('joined_at', 'desc')
            ->paginate($request->get('per_page', 15));

        // Transform the data
        $members->getCollection()->transform(function ($membership) {
            return [
                'id' => $membership->id,
                'user' => [
                    'id' => $membership->user->id,
                    'name' => $membership->user->name,
                    'email' => $membership->user->email,
                    'total_points' => $membership->user->total_points,
                    'activity_streak' => $membership->user->activity_streak,
                    'created_at' => $membership->user->created_at,
                ],
                'club_type' => $membership->club_type,
                'display_name' => $membership->display_name,
                'membership_level' => $membership->membership_level,
                'level_display' => $membership->level_display,
                'is_verified' => $membership->is_verified,
                'verification_method' => $membership->verification_method,
                'founder_code_used' => $membership->founder_code_used,
                'joined_at' => $membership->joined_at,
                'benefits' => $membership->benefits,
            ];
        });

        return response()->json($members);
    }

    /**
     * Get club statistics
     */
    public function getClubStats(Request $request)
    {
        if (!$request->user()->can('view users')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $stats = [
            'total_members' => UserClub::count(),
            'founder_members' => UserClub::where('club_type', 'founder')->count(),
            'verified_members' => UserClub::where('is_verified', true)->count(),
            'active_codes' => FounderReferralCode::where('is_active', true)->count(),
            'total_codes' => FounderReferralCode::count(),
        ];

        // Membership levels breakdown
        $levelBreakdown = UserClub::selectRaw('membership_level, COUNT(*) as count')
            ->groupBy('membership_level')
            ->pluck('count', 'membership_level')
            ->toArray();

        // Club types breakdown
        $clubBreakdown = UserClub::selectRaw('club_type, COUNT(*) as count')
            ->groupBy('club_type')
            ->pluck('count', 'club_type')
            ->toArray();

        // Recent members (last 30 days)
        $recentMembers = UserClub::where('joined_at', '>=', now()->subDays(30))->count();

        return response()->json([
            'stats' => $stats,
            'level_breakdown' => $levelBreakdown,
            'club_breakdown' => $clubBreakdown,
            'recent_members' => $recentMembers,
        ]);
    }

    /**
     * Manually verify a club membership
     */
    public function verifyMembership(Request $request, $membershipId)
    {
        if (!$request->user()->can('edit users')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $membership = UserClub::findOrFail($membershipId);
        
        $membership->update([
            'is_verified' => true,
            'verification_method' => 'manual_admin',
        ]);

        return response()->json([
            'message' => 'Membership verified successfully',
            'membership' => $membership,
        ]);
    }

    /**
     * Send club invitation to an email
     */
    public function sendClubInvitation(Request $request)
    {
        if (!$request->user()->can('edit settings')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $request->validate([
            'email' => 'required|email|max:255',
            'club_type' => 'required|in:founder,premium,regular',
        ]);

        try {
            // Check if user already exists
            $existingUser = \App\Models\User::where('email', $request->email)->first();
            if ($existingUser) {
                return response()->json([
                    'success' => false,
                    'message' => 'A user with this email already exists. They can be added to clubs directly.'
                ], 400);
            }

            // Create invitation
            $invitation = $this->waitlistService->createInvitation(
                $request->email,
                $request->club_type,
                $request->user(),
                72 // 3 days expiry
            );

            // Send invitation email
            $sent = $this->waitlistService->sendInvitation($invitation);

            if ($sent) {
                return response()->json([
                    'success' => true,
                    'message' => 'Club invitation sent successfully!',
                    'invitation' => [
                        'id' => $invitation->id,
                        'email' => $invitation->email,
                        'club_type' => $invitation->club_type,
                        'membership_level' => $invitation->membership_level,
                        'expires_at' => $invitation->expires_at,
                    ]
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to send invitation email. Please try again.'
                ], 500);
            }
        } catch (\Exception $e) {
            \Log::error('Failed to send club invitation', [
                'email' => $request->email,
                'club_type' => $request->club_type,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while sending the invitation. Please try again.'
            ], 500);
        }
    }

    /**
     * Send bulk club invitations
     */
    public function sendBulkClubInvitations(Request $request)
    {
        $request->validate([
            'emails' => 'required|array|min:1',
            'emails.*' => 'email|max:255',
            'club_type' => 'required|in:founder,premium,regular',
        ]);

        try {
            $successCount = 0;
            $failedEmails = [];
            $duplicateEmails = [];

            foreach ($request->emails as $email) {
                // Check if user already exists
                $existingUser = User::where('email', $email)->first();
                if ($existingUser) {
                    $duplicateEmails[] = $email;
                    continue;
                }

                // Check if invitation already exists
                $existingInvitation = WaitlistInvitation::where('email', $email)
                    ->where('status', 'pending')
                    ->first();

                if ($existingInvitation) {
                    $duplicateEmails[] = $email;
                    continue;
                }

                try {
                    // Create invitation
                    $invitation = $this->waitlistService->createInvitation(
                        $email,
                        $request->club_type,
                        $request->user(),
                        72 // 3 days expiry
                    );

                    // Send email
                    Mail::to($email)->send(new \App\Mail\WaitlistInvitationMail($invitation));

                    $successCount++;
                } catch (\Exception $e) {
                    \Log::error('Failed to send individual bulk invitation', [
                        'email' => $email,
                        'error' => $e->getMessage()
                    ]);
                    $failedEmails[] = $email;
                }
            }

            $message = "Successfully sent {$successCount} invitations.";
            if (count($duplicateEmails) > 0) {
                $message .= " " . count($duplicateEmails) . " emails were skipped (already invited or registered).";
            }
            if (count($failedEmails) > 0) {
                $message .= " " . count($failedEmails) . " emails failed to send.";
            }

            return response()->json([
                'success' => true,
                'message' => $message,
                'stats' => [
                    'sent' => $successCount,
                    'duplicates' => count($duplicateEmails),
                    'failed' => count($failedEmails),
                    'total' => count($request->emails)
                ]
            ]);

        } catch (\Exception $e) {
            \Log::error('Failed to send bulk club invitations', [
                'emails_count' => count($request->emails),
                'club_type' => $request->club_type,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to send bulk invitations. Please try again.'
            ], 500);
        }
    }
}
