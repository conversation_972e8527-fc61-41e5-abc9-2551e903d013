<?php

namespace Tests\Feature;

use App\Models\EmailTemplate;
use Database\Seeders\EmailTemplateSeeder;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\File;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class EmailTemplateSeedingTest extends TestCase
{
    use RefreshDatabase;

    #[Test]
    public function it_can_seed_all_email_templates()
    {
        $this->createEmailTemplateFiles();

        $seeder = new EmailTemplateSeeder();
        $seeder->run();

        // Check that templates were created
        $this->assertDatabaseHas('email_templates', ['slug' => 'user-registration']);
        $this->assertDatabaseHas('email_templates', ['slug' => 'provider-registration']);
        $this->assertDatabaseHas('email_templates', ['slug' => 'appointment-booked-patient']);
        $this->assertDatabaseHas('email_templates', ['slug' => 'password-reset']);
        $this->assertDatabaseHas('email_templates', ['slug' => 'referral-invitation']);

        // Verify template count
        $templateCount = EmailTemplate::count();
        $this->assertGreaterThan(10, $templateCount);
    }

    #[Test]
    public function it_can_seed_templates_via_artisan_command()
    {
        $this->createEmailTemplateFiles();

        Artisan::call('db:seed', ['--class' => 'EmailTemplateSeeder']);

        $this->assertDatabaseHas('email_templates', ['slug' => 'user-registration']);
        $this->assertDatabaseHas('email_templates', ['slug' => 'provider-registration']);
    }

    #[Test]
    public function it_does_not_duplicate_templates_when_seeding_multiple_times()
    {
        $this->createEmailTemplateFiles();

        $seeder = new EmailTemplateSeeder();

        // Run seeder first time
        $seeder->run();
        $firstCount = EmailTemplate::count();

        // Run seeder second time
        $seeder->run();
        $secondCount = EmailTemplate::count();

        $this->assertEquals($firstCount, $secondCount);
    }

    #[Test]
    public function it_handles_missing_template_files_gracefully()
    {
        // Don't create template files, seeder should handle missing files

        $seeder = new EmailTemplateSeeder();
        $seeder->run();

        // Should still create templates with fallback content
        $templates = EmailTemplate::all();
        $this->assertGreaterThan(0, $templates->count());

        foreach ($templates as $template) {
            $this->assertNotEmpty($template->name);
            $this->assertNotEmpty($template->slug);
            $this->assertNotEmpty($template->subject);
            $this->assertNotEmpty($template->content);
        }
    }

    #[Test]
    public function it_preserves_existing_customizations_when_reseeding()
    {
        $this->createEmailTemplateFiles();

        // Create initial template
        $template = EmailTemplate::create([
            'name' => 'User Registration',
            'slug' => 'user-registration',
            'subject' => 'Custom Subject',
            'content' => 'Custom Content',
            'description' => 'Custom Description',
        ]);

        // Run seeder
        $seeder = new EmailTemplateSeeder();
        $seeder->run();

        // Check that customizations are preserved
        $template->refresh();
        $this->assertEquals('Custom Subject', $template->subject);
        $this->assertEquals('Custom Content', $template->content);
        $this->assertEquals('Custom Description', $template->description);
    }

    #[Test]
    public function it_creates_templates_with_correct_structure()
    {
        $this->createEmailTemplateFiles();

        $seeder = new EmailTemplateSeeder();
        $seeder->run();

        $template = EmailTemplate::where('slug', 'user-registration')->first();

        $this->assertNotNull($template);
        $this->assertNotEmpty($template->name);
        $this->assertNotEmpty($template->slug);
        $this->assertNotEmpty($template->subject);
        $this->assertNotEmpty($template->content);
        $this->assertTrue($template->is_active);
        $this->assertNotNull($template->created_at);
        $this->assertNotNull($template->updated_at);
    }

    #[Test]
    public function it_creates_all_required_template_types()
    {
        $this->createEmailTemplateFiles();

        $seeder = new EmailTemplateSeeder();
        $seeder->run();

        $requiredSlugs = [
            'user-registration',
            'provider-registration',
            'appointment-booked-patient',
            'appointment-booked-provider',
            'appointment-confirmed-patient',
            'appointment-confirmed-provider',
            'appointment-cancelled-patient',
            'appointment-cancelled-provider',
            'appointment-reminder-patient',
            'appointment-reminder-provider',
            'appointment-rescheduled-patient',
            'appointment-rescheduled-provider',
            'password-reset',
            'referral-invitation',
            'waitlist-invitation',
        ];

        foreach ($requiredSlugs as $slug) {
            $this->assertDatabaseHas('email_templates', ['slug' => $slug]);
        }
    }

    #[Test]
    public function it_sets_correct_default_values()
    {
        $this->createEmailTemplateFiles();

        $seeder = new EmailTemplateSeeder();
        $seeder->run();

        $templates = EmailTemplate::all();

        foreach ($templates as $template) {
            $this->assertTrue($template->is_active, "Template {$template->slug} should be active by default");
            $this->assertNotEmpty($template->name, "Template {$template->slug} should have a name");
            $this->assertNotEmpty($template->subject, "Template {$template->slug} should have a subject");
        }
    }

    #[Test]
    public function it_handles_database_constraints_properly()
    {
        $this->createEmailTemplateFiles();

        // Create template with same slug manually
        EmailTemplate::create([
            'name' => 'Existing Template',
            'slug' => 'user-registration',
            'subject' => 'Existing Subject',
            'content' => 'Existing Content',
        ]);

        // Seeder should handle existing templates gracefully
        $seeder = new EmailTemplateSeeder();
        $seeder->run();

        // Should not create duplicate
        $count = EmailTemplate::where('slug', 'user-registration')->count();
        $this->assertEquals(1, $count);
    }

    #[Test]
    public function it_can_seed_specific_template_types()
    {
        $this->createEmailTemplateFiles();

        // Test seeding only user registration template
        $seeder = new EmailTemplateSeeder();

        // Simulate seeding specific template (this would require command parameter support)
        $seeder->run();

        $userRegTemplate = EmailTemplate::where('slug', 'user-registration')->first();
        $this->assertNotNull($userRegTemplate);
        $this->assertEquals('User Registration', $userRegTemplate->name);
    }

    #[Test]
    public function it_validates_template_content_after_seeding()
    {
        $this->createEmailTemplateFiles();

        $seeder = new EmailTemplateSeeder();
        $seeder->run();

        $templates = EmailTemplate::all();

        foreach ($templates as $template) {
            // Validate that content contains expected placeholders for specific templates
            if (str_contains($template->slug, 'appointment')) {
                $this->assertTrue(
                    str_contains($template->content, '{{') || str_contains($template->content, 'appointment'),
                    "Appointment template {$template->slug} should contain placeholders or appointment text"
                );
            }

            if ($template->slug === 'password-reset') {
                $this->assertTrue(
                    str_contains($template->content, '{{') || str_contains($template->content, 'password'),
                    "Password reset template should contain placeholders or password text"
                );
            }
        }
    }

    #[Test]
    public function it_maintains_template_relationships_after_seeding()
    {
        $this->createEmailTemplateFiles();

        $seeder = new EmailTemplateSeeder();
        $seeder->run();

        // Test that templates can be queried and relationships work
        $activeTemplates = EmailTemplate::where('is_active', true)->get();
        $this->assertGreaterThan(0, $activeTemplates->count());

        $userTemplate = EmailTemplate::where('slug', 'user-registration')->first();
        $this->assertInstanceOf(EmailTemplate::class, $userTemplate);
    }

    private function createEmailTemplateFiles()
    {
        $emailsPath = resource_path('views/emails');
        
        if (!File::exists($emailsPath)) {
            File::makeDirectory($emailsPath, 0755, true);
        }

        $layoutsPath = $emailsPath . '/layouts';
        if (!File::exists($layoutsPath)) {
            File::makeDirectory($layoutsPath, 0755, true);
        }

        // Create layout file
        File::put($layoutsPath . '/app.blade.php', $this->getLayoutContent());

        // Create template files
        $templates = [
            'user-registration' => $this->getUserRegistrationContent(),
            'provider-registration' => $this->getProviderRegistrationContent(),
            'appointment-booked-patient' => $this->getAppointmentContent(),
            'appointment-booked-provider' => $this->getAppointmentContent(),
            'appointment-confirmed-patient' => $this->getAppointmentContent(),
            'appointment-confirmed-provider' => $this->getAppointmentContent(),
            'appointment-cancelled-patient' => $this->getAppointmentContent(),
            'appointment-cancelled-provider' => $this->getAppointmentContent(),
            'appointment-reminder-patient' => $this->getAppointmentContent(),
            'appointment-reminder-provider' => $this->getAppointmentContent(),
            'appointment-rescheduled-patient' => $this->getAppointmentContent(),
            'appointment-rescheduled-provider' => $this->getAppointmentContent(),
            'password-reset' => $this->getPasswordResetContent(),
            'referral-invitation' => $this->getReferralInvitationContent(),
            'waitlist-invitation' => $this->getWaitlistInvitationContent(),
        ];

        foreach ($templates as $slug => $content) {
            File::put($emailsPath . "/{$slug}.blade.php", $content);
        }
    }

    private function getLayoutContent(): string
    {
        return '<!DOCTYPE html>
<html>
<head>
    <title>{{ $title ?? "Medroid" }}</title>
</head>
<body>
    @yield("content")
    {!! $content ?? "" !!}
</body>
</html>';
    }

    private function getUserRegistrationContent(): string
    {
        return '@extends("emails.layouts.app")

@section("content")
<h1>Welcome to {{$appName ?? "Medroid"}}!</h1>
<p>Hello {{$userName ?? "User"}},</p>
<p>Welcome to your AI Doctor in your pocket!</p>
@endsection';
    }

    private function getProviderRegistrationContent(): string
    {
        return '@extends("emails.layouts.app")

@section("content")
<h1>Welcome Dr. {{$providerName ?? "Provider"}}!</h1>
<p>Your provider account has been created successfully.</p>
@endsection';
    }

    private function getAppointmentContent(): string
    {
        return '@extends("emails.layouts.app")

@section("content")
<h1>Appointment Update</h1>
<p>Dear {{$patientName ?? "Patient"}},</p>
<p>Your appointment with {{$providerName ?? "Provider"}} on {{$appointmentDate ?? "Date"}} has been updated.</p>
@endsection';
    }

    private function getPasswordResetContent(): string
    {
        return '@extends("emails.layouts.app")

@section("content")
<h1>Password Reset Request</h1>
<p>Hello {{$userName ?? "User"}},</p>
<p>Click the link below to reset your password:</p>
<a href="{{$resetUrl ?? "#"}}">Reset Password</a>
@endsection';
    }

    private function getReferralInvitationContent(): string
    {
        return '@extends("emails.layouts.app")

@section("content")
<h1>You\'ve been invited!</h1>
<p>{{$referrerName ?? "Someone"}} has invited you to join {{$appName ?? "Medroid"}}.</p>
<p>Use referral code: {{$referralCode ?? "CODE"}}</p>
@endsection';
    }

    private function getWaitlistInvitationContent(): string
    {
        return '@extends("emails.layouts.app")

@section("content")
<h1>Welcome to the Founders\' Club!</h1>
<p>You\'ve been invited to join {{$appName ?? "Medroid"}} early access.</p>
@endsection';
    }
}
