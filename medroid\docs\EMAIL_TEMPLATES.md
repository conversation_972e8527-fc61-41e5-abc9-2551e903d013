# Email Templates Management

This document explains how to manage email templates in the Medroid application.

## Overview

The email template system provides a robust way to manage all email communications with proper fallbacks, seeding, and production safety.

## Features

- ✅ **Robust Seeding**: Templates are seeded via dedicated seeder with error handling
- ✅ **Production Safe**: Handles missing files gracefully with fallback content
- ✅ **Customization Preservation**: Admin customizations are preserved during updates
- ✅ **Force Update**: Option to force update templates when needed
- ✅ **Individual Template Seeding**: Ability to seed specific templates
- ✅ **Comprehensive Coverage**: All email types are included

## Available Templates

| Template | Slug | Description |
|----------|------|-------------|
| User Registration | `user-registration` | Welcome email for new users |
| Provider Registration | `provider-registration` | Welcome email for new providers |
| Password Reset | `password-reset` | Password reset instructions |
| Appointment Booked (Patient) | `appointment-booked-patient` | Confirmation for patients |
| Appointment Booked (Provider) | `appointment-booked-provider` | Notification for providers |
| Appointment Confirmed (Patient) | `appointment-confirmed-patient` | Payment confirmation for patients |
| Appointment Confirmed (Provider) | `appointment-confirmed-provider` | Payment notification for providers |
| Appointment Cancelled (Patient) | `appointment-cancelled-patient` | Cancellation notice for patients |
| Appointment Cancelled (Provider) | `appointment-cancelled-provider` | Cancellation notice for providers |
| Appointment Rescheduled (Patient) | `appointment-rescheduled-patient` | Reschedule notice for patients |
| Appointment Rescheduled (Provider) | `appointment-rescheduled-provider` | Reschedule notice for providers |
| Appointment Reminder (Patient) | `appointment-reminder-patient` | Reminder for patients |
| Appointment Reminder (Provider) | `appointment-reminder-provider` | Reminder for providers |
| Referral Invitation | `referral-invitation` | Referral invitation emails |
| Waitlist Invitation | `waitlist-invitation` | Waitlist invitation emails |

## Commands

### Seed All Templates
```bash
php artisan email-templates:seed
```

### Force Update All Templates
```bash
php artisan email-templates:seed --force
```

### Seed Specific Template
```bash
php artisan email-templates:seed --template=user-registration
```

### Force Update Specific Template
```bash
php artisan email-templates:seed --template=user-registration --force
```

## Database Seeding

### Production Deployment
Templates are automatically seeded during production deployment via `DatabaseSeeder`:

```bash
php artisan db:seed --class=DatabaseSeeder
```

### Development/Local Setup
```bash
php artisan db:seed --class=EmailTemplateSeeder
```

## Template Files

Template files are stored in `resources/views/emails/` and follow this naming convention:
- `{template-slug}.blade.php`

### Example Template Structure
```blade
@extends('emails.layouts.app')

@section('content')
<div style="text-align: center; padding: 40px 20px;">
    <h1 style="color: #2563eb;">{{ $appName ?? 'Medroid' }}</h1>
    <h2>Welcome to Medroid!</h2>
    
    <p>Hello {{ $userName ?? 'User' }},</p>
    <p>Welcome to your AI Doctor in your pocket!</p>
    
    <div style="margin: 30px 0;">
        <a href="{{ $loginUrl ?? '#' }}" 
           style="background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px;">
            Get Started
        </a>
    </div>
</div>
@endsection
```

## Fallback System

If a template file is missing, the system automatically generates fallback content:

1. **Logs Warning**: Missing file is logged for debugging
2. **Creates Fallback**: Generic template with Medroid branding
3. **Continues Operation**: Email sending doesn't fail
4. **Admin Notification**: Template can be customized in admin panel

## Customization Preservation

The seeding system preserves admin customizations:

- **First Seed**: Creates template from file
- **Subsequent Seeds**: Skips if content exists (preserves customizations)
- **Force Update**: `--force` flag overrides and updates from file
- **Empty Content**: Always updates if template content is empty

## Production Deployment Checklist

### Before Deployment
1. ✅ Ensure all template files exist in `resources/views/emails/`
2. ✅ Test templates locally with `php artisan email-templates:seed`
3. ✅ Verify template rendering in admin panel
4. ✅ Check email layouts are properly included

### During Deployment
1. ✅ Run migrations: `php artisan migrate`
2. ✅ Run seeders: `php artisan db:seed --class=DatabaseSeeder`
3. ✅ Clear caches: `php artisan cache:clear`
4. ✅ Verify templates in admin panel

### After Deployment
1. ✅ Test email sending functionality
2. ✅ Verify template rendering
3. ✅ Check logs for any template errors
4. ✅ Customize templates as needed via admin panel

## Troubleshooting

### Template Not Found Error
```bash
# Check if file exists
ls -la resources/views/emails/

# Reseed specific template
php artisan email-templates:seed --template=template-slug

# Force update from file
php artisan email-templates:seed --template=template-slug --force
```

### Template Not Rendering
1. Check template syntax in admin panel
2. Verify Blade syntax is correct
3. Check email layout exists: `resources/views/emails/layouts/app.blade.php`
4. Review logs: `storage/logs/laravel.log`

### Missing Variables
Templates use these common variables:
- `$appName` - Application name
- `$userName` - User's name
- `$userEmail` - User's email
- `$appointmentDate` - Appointment date/time
- `$providerName` - Provider's name
- `$resetUrl` - Password reset URL
- `$referralCode` - Referral code

## Best Practices

1. **Always Test Locally**: Test template changes before deployment
2. **Use Version Control**: Keep template files in version control
3. **Backup Before Updates**: Backup database before force updates
4. **Monitor Logs**: Check logs for template rendering errors
5. **Preserve Customizations**: Use `--force` carefully to avoid losing customizations
6. **Consistent Styling**: Follow Medroid brand guidelines
7. **Mobile Responsive**: Ensure templates work on mobile devices

## API Integration

Templates are used automatically by the email system:

```php
// Send email using template
$emailService = app(EmailTemplateService::class);
$rendered = $emailService->renderTemplate('user-registration', [
    'userName' => $user->name,
    'loginUrl' => route('login'),
]);

// Send via mail
Mail::to($user->email)->send(new TemplatedMail($rendered));
```

## Support

For issues with email templates:
1. Check this documentation
2. Review application logs
3. Test with seeding commands
4. Contact development team if needed
