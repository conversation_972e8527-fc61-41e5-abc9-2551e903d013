<template>
    <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
            <!-- Header -->
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-lg font-semibold text-gray-900">
                    {{ isEdit ? 'Edit Clinic' : 'Create New Clinic' }}
                </h3>
                <button @click="$emit('close')" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>

            <!-- Form -->
            <form @submit.prevent="saveClinic" class="space-y-6">
                <!-- Basic Information -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Clinic Name *</label>
                        <input
                            v-model="form.name"
                            type="text"
                            required
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-medroid-orange"
                            placeholder="Enter clinic name"
                        />
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                        <input
                            v-model="form.email"
                            type="email"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-medroid-orange"
                            placeholder="<EMAIL>"
                        />
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Phone</label>
                        <input
                            v-model="form.phone"
                            type="tel"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-medroid-orange"
                            placeholder="(*************"
                        />
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Website</label>
                        <input
                            v-model="form.website"
                            type="url"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-medroid-orange"
                            placeholder="https://example.com"
                        />
                    </div>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                    <textarea
                        v-model="form.description"
                        rows="3"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-medroid-orange"
                        placeholder="Brief description of the clinic"
                    ></textarea>
                </div>

                <!-- Address Information -->
                <div class="border-t pt-6">
                    <h4 class="text-md font-medium text-gray-900 mb-4">Address Information</h4>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Street Address</label>
                            <input
                                v-model="form.address"
                                type="text"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-medroid-orange"
                                placeholder="123 Main Street"
                            />
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">City</label>
                                <input
                                    v-model="form.city"
                                    type="text"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-medroid-orange"
                                    placeholder="City"
                                />
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">State</label>
                                <input
                                    v-model="form.state"
                                    type="text"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-medroid-orange"
                                    placeholder="State"
                                />
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Postal Code</label>
                                <input
                                    v-model="form.postal_code"
                                    type="text"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-medroid-orange"
                                    placeholder="12345"
                                />
                            </div>
                        </div>
                    </div>
                </div>

                <!-- License Information -->
                <div class="border-t pt-6">
                    <h4 class="text-md font-medium text-gray-900 mb-4">License Information</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">License Number</label>
                            <input
                                v-model="form.license_number"
                                type="text"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-medroid-orange"
                                placeholder="License number"
                            />
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Tax ID</label>
                            <input
                                v-model="form.tax_id"
                                type="text"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-medroid-orange"
                                placeholder="Tax ID"
                            />
                        </div>
                    </div>
                </div>

                <!-- Settings -->
                <div class="border-t pt-6">
                    <h4 class="text-md font-medium text-gray-900 mb-4">Settings</h4>
                    <div class="space-y-4">
                        <div class="flex items-center">
                            <input
                                v-model="form.is_active"
                                type="checkbox"
                                class="h-4 w-4 text-medroid-orange focus:ring-medroid-orange border-gray-300 rounded"
                            />
                            <label class="ml-2 block text-sm text-gray-900">Active</label>
                        </div>
                        <div class="flex items-center">
                            <input
                                v-model="form.accepts_new_patients"
                                type="checkbox"
                                class="h-4 w-4 text-medroid-orange focus:ring-medroid-orange border-gray-300 rounded"
                            />
                            <label class="ml-2 block text-sm text-gray-900">Accepting New Patients</label>
                        </div>
                        <div class="flex items-center">
                            <input
                                v-model="form.telemedicine_enabled"
                                type="checkbox"
                                class="h-4 w-4 text-medroid-orange focus:ring-medroid-orange border-gray-300 rounded"
                            />
                            <label class="ml-2 block text-sm text-gray-900">Telemedicine Enabled</label>
                        </div>
                    </div>
                </div>



                <!-- Form Actions -->
                <div class="flex justify-end space-x-3 pt-6 border-t">
                    <button
                        type="button"
                        @click="$emit('close')"
                        class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                    >
                        Cancel
                    </button>
                    <button
                        type="submit"
                        :disabled="saving"
                        class="px-4 py-2 text-sm font-medium text-white bg-medroid-orange border border-transparent rounded-md hover:bg-medroid-orange-dark disabled:opacity-50"
                    >
                        {{ saving ? 'Saving...' : (isEdit ? 'Update Clinic' : 'Create Clinic') }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue';

const props = defineProps({
    clinic: Object,
    isEdit: Boolean
});

const emit = defineEmits(['close', 'saved']);

// Reactive data
const saving = ref(false);

const form = ref({
    name: '',
    description: '',
    email: '',
    phone: '',
    website: '',
    address: '',
    city: '',
    state: '',
    postal_code: '',
    license_number: '',
    tax_id: '',
    is_active: true,
    accepts_new_patients: true,
    telemedicine_enabled: true
});

// Methods
const saveClinic = async () => {
    saving.value = true;
    try {
        if (props.isEdit) {
            await axios.put(`/update-clinic/${props.clinic.id}`, form.value);
        } else {
            await axios.post('/save-clinic', form.value);
        }
        emit('saved');
    } catch (error) {
        console.error('Error saving clinic:', error);
        alert('Error saving clinic. Please try again.');
    } finally {
        saving.value = false;
    }
};

// Watchers
watch(() => props.clinic, (newClinic) => {
    if (newClinic && props.isEdit) {
        Object.assign(form.value, {
            name: newClinic.name || '',
            description: newClinic.description || '',
            email: newClinic.email || '',
            phone: newClinic.phone || '',
            website: newClinic.website || '',
            address: newClinic.address || '',
            city: newClinic.city || '',
            state: newClinic.state || '',
            postal_code: newClinic.postal_code || '',
            license_number: newClinic.license_number || '',
            tax_id: newClinic.tax_id || '',
            is_active: newClinic.is_active ?? true,
            accepts_new_patients: newClinic.accepts_new_patients ?? true,
            telemedicine_enabled: newClinic.telemedicine_enabled ?? true
        });
    }
}, { immediate: true });

// Lifecycle
onMounted(() => {
    // Component mounted
});
</script>
