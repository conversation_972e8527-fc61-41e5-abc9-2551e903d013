<script setup>
import AppSidebar from '@/components/AppSidebar.vue';
import Breadcrumbs from '@/components/Breadcrumbs.vue';
import { usePage } from '@inertiajs/vue3';
import { computed } from 'vue';

const props = defineProps({
    breadcrumbs: {
        type: Array,
        default: () => []
    }
});

const page = usePage();
const user = computed(() => page.props.auth?.user || null);
</script>

<template>
    <div class="flex h-screen bg-gray-100">
        <AppSidebar v-if="user" :user="user" />

        <!-- Loading sidebar placeholder -->
        <div v-else class="w-64 bg-white border-r border-gray-200 flex items-center justify-center">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
        </div>

        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Header -->
            <header class="bg-white shadow-sm border-b border-gray-200">
                <div class="px-4 py-2">
                    <div class="flex items-center justify-between">
                        <div>
                            <Breadcrumbs v-if="breadcrumbs.length > 0" :breadcrumbs="breadcrumbs" />
                        </div>
                        <div class="flex items-center space-x-3">
                            <span class="text-sm text-gray-700">Welcome, {{ user?.name || 'Guest' }}</span>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Main Content -->
            <main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-100">
                <slot />
            </main>
        </div>
    </div>
</template>
