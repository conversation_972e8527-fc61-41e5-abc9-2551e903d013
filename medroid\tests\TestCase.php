<?php

namespace Tests;

use Illuminate\Foundation\Testing\TestCase as BaseTestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Notification;

abstract class TestCase extends BaseTestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Fake mail and notifications for testing
        Mail::fake();
        Notification::fake();

        // Set testing environment
        config(['app.env' => 'testing']);
        config(['mail.default' => 'array']);
    }

    /**
     * Create a test user with specific role
     */
    protected function createUserWithRole(string $role = 'user'): \App\Models\User
    {
        $user = \App\Models\User::factory()->create();

        // Assign role and permissions if role system is available
        if (class_exists(\Spatie\Permission\Models\Role::class)) {
            $roleModel = \Spatie\Permission\Models\Role::firstOrCreate(['name' => $role]);
            $user->assignRole($roleModel);

            // Give admin users all permissions for testing
            if ($role === 'admin') {
                $permissions = [
                    'view settings',
                    'edit settings',
                    'view email templates',
                    'edit email templates',
                    'view users',
                    'edit users',
                ];

                foreach ($permissions as $permission) {
                    $permissionModel = \Spatie\Permission\Models\Permission::firstOrCreate(['name' => $permission]);
                    $user->givePermissionTo($permissionModel);
                }
            }
        }

        return $user;
    }

    /**
     * Create authenticated request
     */
    protected function authenticatedJson(string $method, string $uri, array $data = [], array $headers = []): \Illuminate\Testing\TestResponse
    {
        $user = $this->createUserWithRole('admin');
        return $this->actingAs($user)->json($method, $uri, $data, $headers);
    }
}
