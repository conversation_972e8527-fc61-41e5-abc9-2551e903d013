<script setup lang="ts">
import { ref, defineEmits, defineProps, nextTick, watch } from 'vue';

interface Props {
    modelValue: string;
    placeholder?: string;
    disabled?: boolean;
    isLoading?: boolean;
    showTools?: boolean;
    showVersion?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
    placeholder: 'Type your message...',
    disabled: false,
    isLoading: false,
    showTools: false, // Default to false to remove tools
    showVersion: false
});

const emit = defineEmits<{
    'update:modelValue': [value: string];
    'send': [];
    'keydown': [event: KeyboardEvent];
}>();

const chatInputRef = ref<HTMLTextAreaElement | null>(null);

// Format text to sentence case
const formatToSentenceCase = (text: string): string => {
    if (!text) return text;

    // Split by sentences (periods, exclamation marks, question marks)
    return text.replace(/([.!?]\s*)([a-z])/g, (_, punctuation, letter) => {
        return punctuation + letter.toUpperCase();
    }).replace(/^[a-z]/, (match) => {
        return match.toUpperCase();
    });
};

// Auto-resize textarea
const autoResize = () => {
    if (chatInputRef.value) {
        chatInputRef.value.style.height = 'auto';
        const scrollHeight = chatInputRef.value.scrollHeight;

        // Limit to 4-5 lines max (approximately 100px)
        const maxHeight = 100;
        const minHeight = 40; // Single line height (smaller)

        const newHeight = Math.min(Math.max(scrollHeight, minHeight), maxHeight);
        chatInputRef.value.style.height = `${newHeight}px`;

        // Enable scroll if content exceeds max height
        if (scrollHeight > maxHeight) {
            chatInputRef.value.style.overflowY = 'auto';
        } else {
            chatInputRef.value.style.overflowY = 'hidden';
        }
    }
};

const updateValue = (event: Event) => {
    const target = event.target as HTMLTextAreaElement;
    const formattedValue = formatToSentenceCase(target.value);
    emit('update:modelValue', formattedValue);
    
    // Auto-resize after update
    nextTick(() => {
        autoResize();
    });
};

const handleKeyDown = (event: KeyboardEvent) => {
    emit('keydown', event);
};

const handleSend = () => {
    emit('send');
    // Reset height after sending
    nextTick(() => {
        if (chatInputRef.value) {
            chatInputRef.value.style.height = '40px'; // Reset to smaller single line height
            chatInputRef.value.style.overflowY = 'hidden';
        }
    });
};

const focus = () => {
    if (chatInputRef.value) {
        chatInputRef.value.focus();
    }
};

// Watch for modelValue changes to handle auto-resize
watch(() => props.modelValue, () => {
    nextTick(() => {
        autoResize();
    });
});

// Expose focus method to parent
defineExpose({
    focus
});
</script>

<template>
    <div class="space-y-3">
        <div class="relative">
            <!-- Input Container - White background with seamless blending -->
            <div class="bg-white border border-gray-200 rounded-2xl shadow-sm hover:shadow-md transition-all duration-200 overflow-hidden">
                <!-- Input Area -->
                <div class="relative">
                    <textarea
                        ref="chatInputRef"
                        :value="modelValue"
                        @input="updateValue"
                        @keydown="handleKeyDown"
                        :placeholder="placeholder"
                        class="w-full px-4 py-3 text-gray-800 placeholder-gray-400 bg-transparent border-0 resize-none focus:outline-none focus:ring-0 text-base leading-relaxed transition-all duration-200 break-words"
                        style="height: 40px; overflow-y: hidden; word-wrap: break-word; white-space: pre-wrap;"
                        :disabled="disabled || isLoading"
                    ></textarea>

                    <!-- Bottom Bar - Simplified -->
                    <div class="flex items-center justify-end px-4 pb-3">
                        <!-- Send Button -->
                        <button
                            @click="handleSend"
                            :disabled="isLoading || !modelValue.trim()"
                            :class="[
                                'p-2 rounded-lg transition-all duration-200 transform hover:scale-105',
                                modelValue.trim() && !isLoading
                                    ? 'bg-teal-500 text-white hover:bg-teal-600 shadow-lg'
                                    : 'bg-gray-200 text-gray-500 cursor-not-allowed'
                            ]"
                        >
                            <svg v-if="!isLoading" class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M2.01 21L23 12L2.01 3L2 10L17 12L2 14L2.01 21Z" />
                            </svg>
                            <div v-else class="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Disclaimer - Simplified -->
        <div class="relative">
            <p class="text-xs text-gray-500 text-center leading-relaxed px-4 py-2 bg-gray-50/60 rounded-lg border border-gray-200/50">
                Medroid is an AI tool, not a doctor. Always consult a healthcare professional. By using Medroid, you agree to our
                <a href="https://medroid.ai/terms-of-service-1/" target="_blank" class="text-gray-600 hover:text-gray-800 underline underline-offset-2 transition-colors duration-150">Terms of Service</a> and
                <a href="https://medroid.ai/privacy-policy/" target="_blank" class="text-gray-600 hover:text-gray-800 underline underline-offset-2 transition-colors duration-150">Privacy Policy</a>.
            </p>
        </div>
    </div>
</template>

<style scoped>
/* Smooth transitions for textarea resize */
textarea {
    transition: height 0.2s ease-out;
}

/* Custom scrollbar for textarea when needed */
textarea::-webkit-scrollbar {
    width: 4px;
}

textarea::-webkit-scrollbar-track {
    background: transparent;
}

textarea::-webkit-scrollbar-thumb {
    background: rgba(156, 163, 175, 0.5);
    border-radius: 2px;
}

textarea::-webkit-scrollbar-thumb:hover {
    background: rgba(156, 163, 175, 0.7);
}

/* Firefox scrollbar */
textarea {
    scrollbar-width: thin;
    scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

/* Glass morphism effects */
.backdrop-blur-sm {
    backdrop-filter: blur(8px);
}

/* Enhanced hover effects */
.hover\:scale-105:hover {
    transform: scale(1.05);
}
</style>