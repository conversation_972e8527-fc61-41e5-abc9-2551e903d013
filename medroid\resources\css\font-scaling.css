/* Font Size Scaling System */

/* CSS Custom Properties for Font Scaling */
:root {
  --font-scale: 1;
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;
  --text-5xl: 3rem;
  --text-6xl: 3.75rem;
}

/* Font Size Classes with Scaling */
.font-scalable {
  font-size: calc(var(--text-base) * var(--font-scale));
}

.text-xs-scalable {
  font-size: var(--text-xs) !important;
}

.text-sm-scalable {
  font-size: var(--text-sm) !important;
}

.text-base-scalable {
  font-size: var(--text-base) !important;
}

.text-lg-scalable {
  font-size: var(--text-lg) !important;
}

.text-xl-scalable {
  font-size: var(--text-xl) !important;
}

.text-2xl-scalable {
  font-size: var(--text-2xl) !important;
}

.text-3xl-scalable {
  font-size: var(--text-3xl) !important;
}

.text-4xl-scalable {
  font-size: var(--text-4xl) !important;
}

.text-5xl-scalable {
  font-size: var(--text-5xl) !important;
}

.text-6xl-scalable {
  font-size: var(--text-6xl) !important;
}

/* Apply scaling to common text elements */
body.font-size-small {
  --font-scale: 0.875;
}

body.font-size-normal {
  --font-scale: 1;
}

body.font-size-large {
  --font-scale: 1.125;
}

body.font-size-xlarge {
  --font-scale: 1.25;
}

/* Auto-scale common elements */
body.font-size-small p,
body.font-size-small span,
body.font-size-small div,
body.font-size-small button,
body.font-size-small input,
body.font-size-small textarea,
body.font-size-small label {
  font-size: calc(1em * 0.875);
}

body.font-size-large p,
body.font-size-large span,
body.font-size-large div,
body.font-size-large button,
body.font-size-large input,
body.font-size-large textarea,
body.font-size-large label {
  font-size: calc(1em * 1.125);
}

body.font-size-xlarge p,
body.font-size-xlarge span,
body.font-size-xlarge div,
body.font-size-xlarge button,
body.font-size-xlarge input,
body.font-size-xlarge textarea,
body.font-size-xlarge label {
  font-size: calc(1em * 1.25);
}

/* Specific scaling for headings */
body.font-size-small h1 { font-size: calc(2.25rem * 0.875); }
body.font-size-small h2 { font-size: calc(1.875rem * 0.875); }
body.font-size-small h3 { font-size: calc(1.5rem * 0.875); }
body.font-size-small h4 { font-size: calc(1.25rem * 0.875); }
body.font-size-small h5 { font-size: calc(1.125rem * 0.875); }
body.font-size-small h6 { font-size: calc(1rem * 0.875); }

body.font-size-large h1 { font-size: calc(2.25rem * 1.125); }
body.font-size-large h2 { font-size: calc(1.875rem * 1.125); }
body.font-size-large h3 { font-size: calc(1.5rem * 1.125); }
body.font-size-large h4 { font-size: calc(1.25rem * 1.125); }
body.font-size-large h5 { font-size: calc(1.125rem * 1.125); }
body.font-size-large h6 { font-size: calc(1rem * 1.125); }

body.font-size-xlarge h1 { font-size: calc(2.25rem * 1.25); }
body.font-size-xlarge h2 { font-size: calc(1.875rem * 1.25); }
body.font-size-xlarge h3 { font-size: calc(1.5rem * 1.25); }
body.font-size-xlarge h4 { font-size: calc(1.25rem * 1.25); }
body.font-size-xlarge h5 { font-size: calc(1.125rem * 1.25); }
body.font-size-xlarge h6 { font-size: calc(1rem * 1.25); }

/* Preserve icon sizes */
body.font-size-small svg,
body.font-size-large svg,
body.font-size-xlarge svg {
  width: 1em;
  height: 1em;
}

/* Adjust padding and margins proportionally */
body.font-size-small .p-1 { padding: calc(0.25rem * 0.875); }
body.font-size-small .p-2 { padding: calc(0.5rem * 0.875); }
body.font-size-small .p-3 { padding: calc(0.75rem * 0.875); }
body.font-size-small .p-4 { padding: calc(1rem * 0.875); }

body.font-size-large .p-1 { padding: calc(0.25rem * 1.125); }
body.font-size-large .p-2 { padding: calc(0.5rem * 1.125); }
body.font-size-large .p-3 { padding: calc(0.75rem * 1.125); }
body.font-size-large .p-4 { padding: calc(1rem * 1.125); }

body.font-size-xlarge .p-1 { padding: calc(0.25rem * 1.25); }
body.font-size-xlarge .p-2 { padding: calc(0.5rem * 1.25); }
body.font-size-xlarge .p-3 { padding: calc(0.75rem * 1.25); }
body.font-size-xlarge .p-4 { padding: calc(1rem * 1.25); }

/* Smooth transitions for font size changes */
* {
  transition: font-size 0.2s ease-in-out;
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  * {
    transition: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .font-size-control button {
    border: 2px solid currentColor;
  }
}
