<?php

namespace Database\Seeders;

use App\Models\EmailTemplate;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;

class EmailTemplateSeeder extends Seeder
{
    /**
     * Command instance for output.
     *
     * @var \Illuminate\Console\Command|null
     */
    protected $command;

    /**
     * Force update existing templates.
     *
     * @var bool
     */
    protected $forceUpdate = false;

    /**
     * Specific template to seed.
     *
     * @var string|null
     */
    protected $specificTemplate = null;

    /**
     * All email templates with their configuration.
     *
     * @var array
     */
    private $templates = [
        [
            'name' => 'User Registration',
            'slug' => 'user-registration',
            'subject' => 'Welcome to Medroid - Your AI Doctor is Ready!',
            'description' => 'Email sent to users when they register',
            'file' => 'user-registration.blade.php',
        ],
        [
            'name' => 'Provider Registration',
            'slug' => 'provider-registration',
            'subject' => 'Welcome to Medroid - Provider Account Created',
            'description' => 'Email sent to providers when they register',
            'file' => 'provider-registration.blade.php',
        ],
        [
            'name' => 'Password Reset',
            'slug' => 'password-reset',
            'subject' => 'Reset Your Medroid Password',
            'description' => 'Email sent to users when they request a password reset',
            'file' => 'password-reset.blade.php',
        ],
        [
            'name' => 'Appointment Booked (Patient)',
            'slug' => 'appointment-booked-patient',
            'subject' => 'Your Appointment Has Been Booked',
            'description' => 'Email sent to patients when they book an appointment',
            'file' => 'appointment-booked-patient.blade.php',
        ],
        [
            'name' => 'Appointment Booked (Provider)',
            'slug' => 'appointment-booked-provider',
            'subject' => 'New Appointment Booked',
            'description' => 'Email sent to providers when a patient books an appointment with them',
            'file' => 'appointment-booked-provider.blade.php',
        ],
        [
            'name' => 'Appointment Confirmed (Patient)',
            'slug' => 'appointment-confirmed-patient',
            'subject' => 'Your Appointment Payment Has Been Confirmed',
            'description' => 'Email sent to patients when their appointment payment is confirmed',
            'file' => 'appointment-confirmed-patient.blade.php',
        ],
        [
            'name' => 'Appointment Confirmed (Provider)',
            'slug' => 'appointment-confirmed-provider',
            'subject' => 'Appointment Payment Confirmed',
            'description' => 'Email sent to providers when a patient\'s appointment payment is confirmed',
            'file' => 'appointment-confirmed-provider.blade.php',
        ],
        [
            'name' => 'Appointment Cancelled (Patient)',
            'slug' => 'appointment-cancelled-patient',
            'subject' => 'Your Appointment Has Been Cancelled',
            'description' => 'Email sent to patients when their appointment is cancelled',
            'file' => 'appointment-cancelled-patient.blade.php',
        ],
        [
            'name' => 'Appointment Cancelled (Provider)',
            'slug' => 'appointment-cancelled-provider',
            'subject' => 'Appointment Cancelled',
            'description' => 'Email sent to providers when an appointment is cancelled',
            'file' => 'appointment-cancelled-provider.blade.php',
        ],
        [
            'name' => 'Appointment Rescheduled (Patient)',
            'slug' => 'appointment-rescheduled-patient',
            'subject' => 'Your Appointment Has Been Rescheduled',
            'description' => 'Email sent to patients when their appointment is rescheduled',
            'file' => 'appointment-rescheduled-patient.blade.php',
        ],
        [
            'name' => 'Appointment Rescheduled (Provider)',
            'slug' => 'appointment-rescheduled-provider',
            'subject' => 'Appointment Rescheduled',
            'description' => 'Email sent to providers when an appointment is rescheduled',
            'file' => 'appointment-rescheduled-provider.blade.php',
        ],
        [
            'name' => 'Appointment Reminder (Patient)',
            'slug' => 'appointment-reminder-patient',
            'subject' => 'Reminder: Your Appointment is Tomorrow',
            'description' => 'Email sent to patients to remind them of an upcoming appointment',
            'file' => 'appointment-reminder-patient.blade.php',
        ],
        [
            'name' => 'Appointment Reminder (Provider)',
            'slug' => 'appointment-reminder-provider',
            'subject' => 'Reminder: Upcoming Appointment Tomorrow',
            'description' => 'Email sent to providers to remind them of an upcoming appointment',
            'file' => 'appointment-reminder-provider.blade.php',
        ],
        [
            'name' => 'Referral Invitation',
            'slug' => 'referral-invitation',
            'subject' => '🎁 You\'ve Been Invited to Join Medroid - Your AI Doctor Awaits!',
            'description' => 'Email sent to users when they are invited via referral',
            'file' => 'referral-invitation.blade.php',
        ],
        [
            'name' => 'Waitlist Invitation',
            'slug' => 'waitlist-invitation',
            'subject' => '🤖 EXCLUSIVE: Welcome to the Medroid Founders\' Club',
            'description' => 'Email sent to users when they are invited from the waitlist',
            'file' => 'waitlist-invitation.blade.php',
        ],
    ];

    /**
     * Set command instance for output.
     *
     * @param \Illuminate\Console\Command $command
     * @return $this
     */
    public function setCommand($command)
    {
        $this->command = $command;

        // Get options from command if available and if the command has the option method
        if (method_exists($command, 'option')) {
            try {
                $this->forceUpdate = $command->option('force') ?? false;
                $this->specificTemplate = $command->option('template');
            } catch (\Exception $e) {
                // If options don't exist (like when called from DatabaseSeeder), use defaults
                $this->forceUpdate = false;
                $this->specificTemplate = null;
            }
        }

        return $this;
    }

    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        if ($this->command) {
            $this->command->info('Seeding email templates...');
        }

        $created = 0;
        $updated = 0;
        $errors = 0;
        $skipped = 0;

        $templatesToSeed = $this->specificTemplate
            ? array_filter($this->templates, fn($t) => $t['slug'] === $this->specificTemplate)
            : $this->templates;

        if ($this->specificTemplate && empty($templatesToSeed)) {
            if ($this->command) {
                $this->command->error("Template '{$this->specificTemplate}' not found!");
            }
            return;
        }

        foreach ($templatesToSeed as $templateData) {
            try {
                $result = $this->seedTemplate($templateData);
                switch ($result) {
                    case 'created':
                        $created++;
                        break;
                    case 'updated':
                        $updated++;
                        break;
                    case 'skipped':
                        $skipped++;
                        break;
                }
            } catch (\Exception $e) {
                $errors++;
                if ($this->command) {
                    $this->command->error("Failed to seed template '{$templateData['slug']}': " . $e->getMessage());
                }
                Log::error("Email template seeding error", [
                    'template' => $templateData['slug'],
                    'error' => $e->getMessage(),
                ]);
            }
        }

        if ($this->command) {
            $this->command->info("Email templates seeded successfully!");
            $this->command->info("Created: {$created}, Updated: {$updated}, Skipped: {$skipped}, Errors: {$errors}");
        }
    }

    /**
     * Seed a single template.
     *
     * @param array $templateData
     * @return string 'created', 'updated', or 'skipped'
     */
    private function seedTemplate(array $templateData): string
    {
        // Get template content from file
        $content = $this->getTemplateContent($templateData['file']);
        
        // Check if template already exists
        $existingTemplate = EmailTemplate::where('slug', $templateData['slug'])->first();
        
        if ($existingTemplate) {
            // Update if forced or if content is empty or should be updated
            if ($this->forceUpdate || empty($existingTemplate->content) || $this->shouldUpdateTemplate($existingTemplate, $content)) {
                $existingTemplate->update([
                    'name' => $templateData['name'],
                    'subject' => $templateData['subject'],
                    'content' => $content,
                    'description' => $templateData['description'],
                ]);

                if ($this->command) {
                    $this->command->info("Updated template: {$templateData['slug']}");
                }
                return 'updated';
            }

            if ($this->command) {
                $this->command->info("Skipped template (already exists): {$templateData['slug']}");
            }
            return 'skipped';
        }
        
        // Create new template
        EmailTemplate::create([
            'name' => $templateData['name'],
            'slug' => $templateData['slug'],
            'subject' => $templateData['subject'],
            'content' => $content,
            'description' => $templateData['description'],
            'is_active' => true,
        ]);

        if ($this->command) {
            $this->command->info("Created template: {$templateData['slug']}");
        }
        return 'created';
    }

    /**
     * Get template content from file with fallback.
     *
     * @param string $filename
     * @return string
     */
    private function getTemplateContent(string $filename): string
    {
        $filePath = resource_path("views/emails/{$filename}");
        
        if (File::exists($filePath)) {
            return File::get($filePath);
        }
        
        // Log warning and return fallback content
        if ($this->command) {
            $this->command->warn("Template file not found: {$filename}. Using fallback content.");
        }
        Log::warning("Email template file not found", ['file' => $filename]);
        
        return $this->getFallbackContent($filename);
    }

    /**
     * Determine if template should be updated.
     *
     * @param EmailTemplate $template
     * @param string $newContent
     * @return bool
     */
    private function shouldUpdateTemplate(EmailTemplate $template, string $newContent): bool
    {
        // Always update if content is empty
        if (empty($template->content)) {
            return true;
        }
        
        // Don't update if content has been customized (different from file content)
        // This preserves admin customizations
        return false;
    }

    /**
     * Get fallback content for missing template files.
     *
     * @param string $filename
     * @return string
     */
    private function getFallbackContent(string $filename): string
    {
        $templateName = str_replace(['.blade.php', '-'], [' ', ' '], $filename);
        $templateName = ucwords($templateName);
        
        return <<<HTML
@extends('emails.layouts.app')

@section('content')
<div style="text-align: center; padding: 40px 20px;">
    <h1 style="color: #2563eb; margin-bottom: 20px;">Medroid</h1>
    <h2 style="color: #374151; margin-bottom: 30px;">{$templateName}</h2>
    
    <div style="background: #f9fafb; padding: 20px; border-radius: 8px; margin: 20px 0;">
        <p style="color: #6b7280; margin: 0;">
            This is a fallback template. Please update the content in the admin panel.
        </p>
    </div>
    
    <p style="color: #374151; line-height: 1.6;">
        Thank you for using Medroid - Your AI Doctor in your pocket.
    </p>
</div>
@endsection
HTML;
    }
}
