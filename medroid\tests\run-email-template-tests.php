<?php

/**
 * Email Template Test Runner
 * 
 * This script runs comprehensive tests for the email template system
 * including unit tests, integration tests, and seeding tests.
 */

require_once __DIR__ . '/../vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\Artisan;

class EmailTemplateTestRunner
{
    private $app;
    private $results = [];

    public function __construct()
    {
        $this->setupApplication();
    }

    public function runAllTests()
    {
        echo "🧪 Starting Email Template Test Suite\n";
        echo "=====================================\n\n";

        $this->runUnitTests();
        $this->runFeatureTests();
        $this->runIntegrationTests();
        $this->runSeedingTests();

        $this->displayResults();
    }

    private function setupApplication()
    {
        // Set up Laravel application for testing
        $app = require __DIR__ . '/../bootstrap/app.php';
        $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
        $this->app = $app;

        // Set testing environment
        $app['env'] = 'testing';
        config(['database.default' => 'sqlite']);
        config(['database.connections.sqlite.database' => ':memory:']);
    }

    private function runUnitTests()
    {
        echo "📋 Running Unit Tests...\n";
        
        $tests = [
            'Unit/Models/EmailTemplateTest',
            'Unit/Services/EmailTemplateServiceTest',
        ];

        foreach ($tests as $test) {
            $this->runTest($test, 'Unit');
        }
        
        echo "\n";
    }

    private function runFeatureTests()
    {
        echo "🔧 Running Feature Tests...\n";
        
        $tests = [
            'Feature/EmailTemplateControllerTest',
        ];

        foreach ($tests as $test) {
            $this->runTest($test, 'Feature');
        }
        
        echo "\n";
    }

    private function runIntegrationTests()
    {
        echo "🔗 Running Integration Tests...\n";
        
        $tests = [
            'Feature/EmailTemplateIntegrationTest',
        ];

        foreach ($tests as $test) {
            $this->runTest($test, 'Integration');
        }
        
        echo "\n";
    }

    private function runSeedingTests()
    {
        echo "🌱 Running Seeding Tests...\n";
        
        $tests = [
            'Feature/EmailTemplateSeedingTest',
        ];

        foreach ($tests as $test) {
            $this->runTest($test, 'Seeding');
        }
        
        echo "\n";
    }

    private function runTest($testClass, $category)
    {
        $command = "vendor/bin/phpunit tests/{$testClass}.php --testdox";
        
        echo "  Running {$testClass}...\n";
        
        $output = [];
        $returnCode = 0;
        
        exec($command . " 2>&1", $output, $returnCode);
        
        $success = $returnCode === 0;
        $this->results[$category][$testClass] = [
            'success' => $success,
            'output' => implode("\n", $output),
            'return_code' => $returnCode
        ];

        if ($success) {
            echo "    ✅ PASSED\n";
        } else {
            echo "    ❌ FAILED\n";
            echo "    Error: " . implode("\n    ", array_slice($output, -3)) . "\n";
        }
    }

    private function displayResults()
    {
        echo "📊 Test Results Summary\n";
        echo "======================\n\n";

        $totalTests = 0;
        $passedTests = 0;

        foreach ($this->results as $category => $tests) {
            echo "📁 {$category} Tests:\n";
            
            foreach ($tests as $testName => $result) {
                $totalTests++;
                $status = $result['success'] ? '✅ PASSED' : '❌ FAILED';
                echo "  {$testName}: {$status}\n";
                
                if ($result['success']) {
                    $passedTests++;
                } else {
                    echo "    Details: " . substr($result['output'], -200) . "\n";
                }
            }
            echo "\n";
        }

        echo "📈 Overall Results:\n";
        echo "  Total Tests: {$totalTests}\n";
        echo "  Passed: {$passedTests}\n";
        echo "  Failed: " . ($totalTests - $passedTests) . "\n";
        echo "  Success Rate: " . round(($passedTests / $totalTests) * 100, 2) . "%\n\n";

        if ($passedTests === $totalTests) {
            echo "🎉 All tests passed! Email template system is working correctly.\n";
        } else {
            echo "⚠️  Some tests failed. Please review the failures above.\n";
        }
    }

    public function runSpecificTest($testClass)
    {
        echo "🧪 Running Specific Test: {$testClass}\n";
        echo "========================================\n\n";

        $command = "vendor/bin/phpunit tests/{$testClass}.php --testdox --verbose";
        
        echo "Executing: {$command}\n\n";
        
        passthru($command);
    }

    public function runTestsWithCoverage()
    {
        echo "📊 Running Tests with Coverage Report\n";
        echo "====================================\n\n";

        $command = "vendor/bin/phpunit tests/ --coverage-html coverage-report --whitelist app/";
        
        echo "Executing: {$command}\n\n";
        
        passthru($command);
        
        echo "\n📁 Coverage report generated in coverage-report/ directory\n";
    }
}

// Command line interface
if (isset($argv[1])) {
    $runner = new EmailTemplateTestRunner();
    
    switch ($argv[1]) {
        case 'all':
            $runner->runAllTests();
            break;
            
        case 'coverage':
            $runner->runTestsWithCoverage();
            break;
            
        case 'specific':
            if (isset($argv[2])) {
                $runner->runSpecificTest($argv[2]);
            } else {
                echo "Please specify a test class. Example: php run-email-template-tests.php specific Unit/Models/EmailTemplateTest\n";
            }
            break;
            
        default:
            echo "Usage:\n";
            echo "  php run-email-template-tests.php all          - Run all email template tests\n";
            echo "  php run-email-template-tests.php coverage     - Run tests with coverage report\n";
            echo "  php run-email-template-tests.php specific <test> - Run specific test class\n";
            break;
    }
} else {
    echo "Email Template Test Runner\n";
    echo "=========================\n\n";
    echo "Usage:\n";
    echo "  php run-email-template-tests.php all          - Run all email template tests\n";
    echo "  php run-email-template-tests.php coverage     - Run tests with coverage report\n";
    echo "  php run-email-template-tests.php specific <test> - Run specific test class\n\n";
    echo "Examples:\n";
    echo "  php run-email-template-tests.php all\n";
    echo "  php run-email-template-tests.php specific Unit/Models/EmailTemplateTest\n";
    echo "  php run-email-template-tests.php coverage\n";
}
