import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart' show kIsWeb, kDebugMode;
import 'package:agora_rtc_engine/agora_rtc_engine.dart';
import 'package:medroid_app/services/agora_service_factory.dart';
import 'package:medroid_app/services/api_service.dart';
import 'package:medroid_app/services/video_consultation_service.dart';
// Debug imports removed for production build
import 'package:permission_handler/permission_handler.dart';
import 'dart:async';

class AgoraVideoConsultationScreen extends StatefulWidget {
  final String appointmentId;
  final bool isProvider;
  final String userName;

  const AgoraVideoConsultationScreen({
    Key? key,
    required this.appointmentId,
    required this.isProvider,
    required this.userName,
  }) : super(key: key);

  @override
  State<AgoraVideoConsultationScreen> createState() =>
      _AgoraVideoConsultationScreenState();
}

class _AgoraVideoConsultationScreenState
    extends State<AgoraVideoConsultationScreen> {
  late final VideoConsultationService _videoService;
  late final dynamic _agoraService; // Can be AgoraService or AgoraWebService

  // State variables
  bool _isAgoraInitialized = false;
  bool _localAudioMuted = false;
  bool _localVideoMuted = false;
  bool _isEndingCall = false;
  int? _remoteUid;
  int? _localUid; // Add local UID tracking

  // Control button loading states
  bool _isTogglingAudio = false;
  bool _isTogglingVideo = false;
  bool _isSwitchingCamera = false;

  // Connection status (matching web version)
  String _connectionStatus =
      'disconnected'; // disconnected, connecting, connected, waiting
  bool _isConnecting = false;
  String? _connectionError;

  // Timer for periodic checks
  Timer? _periodicTimer;

  // Flag to ensure initialization happens only once
  bool _hasInitialized = false;

  @override
  void initState() {
    super.initState();
    _agoraService = AgoraServiceFactory.createAgoraService(ApiService());
    _videoService = VideoConsultationService(ApiService());
    _setupVideoServiceCallbacks();
  }

  /// Setup callbacks for video service
  void _setupVideoServiceCallbacks() {
    _videoService.onConnectionStatusChanged = (status) {
      if (mounted) {
        setState(() {
          _connectionStatus = status;
          _isConnecting = status == 'connecting';
        });
      }
    };

    _videoService.onError = (error) {
      if (mounted) {
        setState(() {
          _connectionError = error;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(error),
            backgroundColor: Colors.red,
          ),
        );
      }
    };

    _videoService.onSuccess = (message) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(message),
            backgroundColor: Colors.green,
          ),
        );
      }
    };
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (!_hasInitialized) {
      _hasInitialized = true;
      _initializeVideoCall();
    }
  }

  /// Initialize video call using the new video service (matching web version)
  Future<void> _initializeVideoCall() async {
    try {
      debugPrint(
          'Initializing video call for appointment: ${widget.appointmentId}');

      // Use the new video service to initialize the call
      final result = await _videoService.initializeCall(
        appointmentId: widget.appointmentId,
        userRole: widget.isProvider ? 'provider' : 'patient',
        appointment: null, // We could pass appointment data if available
      );

      if (result['success']) {
        if (result['status'] == 'waiting') {
          // Patient is waiting for provider
          debugPrint('Patient waiting for provider to start session');
          setState(() {
            _connectionStatus = 'waiting';
          });
        } else {
          // Session started successfully, continue with Agora initialization
          await _initializeAgora();
        }
      } else {
        _showError(result['message'] ?? 'Failed to initialize video call');
      }
    } catch (e) {
      debugPrint('Error in _initializeVideoCall: $e');
      _showError('Failed to initialize video call: $e');
    }
  }

  /// Show error message
  void _showError(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  void dispose() {
    debugPrint('Disposing Agora video consultation screen');
    _periodicTimer?.cancel();
    try {
      _videoService.dispose();
      _agoraService.dispose();
    } catch (e) {
      debugPrint('Error disposing services: $e');
    }
    super.dispose();
  }

  Future<void> _initializeAgora() async {
    try {
      debugPrint('Initializing Agora for appointment: ${widget.appointmentId}');
      debugPrint(
          'User is ${widget.isProvider ? "Provider" : "Patient"}: ${widget.userName}');

      // Request permissions with better error handling
      if (!kIsWeb) {
        // Mobile permission handling with retry logic
        bool permissionsGranted = false;
        int retryCount = 0;
        const maxRetries = 2;

        while (!permissionsGranted && retryCount < maxRetries) {
          try {
            final status = await [Permission.camera, Permission.microphone].request();
            debugPrint('Permission status (attempt ${retryCount + 1}): $status');

            if (status[Permission.camera] == PermissionStatus.granted &&
                status[Permission.microphone] == PermissionStatus.granted) {
              permissionsGranted = true;
              debugPrint('All permissions granted successfully');
            } else {
              retryCount++;
              
              if (retryCount >= maxRetries) {
                // Check if permissions are permanently denied
                final cameraStatus = await Permission.camera.status;
                final micStatus = await Permission.microphone.status;
                
                if (cameraStatus.isPermanentlyDenied || micStatus.isPermanentlyDenied) {
                  if (mounted) {
                    Future.microtask(() {
                      if (mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: const Text(
                              'Camera and microphone permissions are required for video calls. Please enable them in app settings.',
                            ),
                            backgroundColor: Colors.red,
                            duration: const Duration(seconds: 8),
                            action: SnackBarAction(
                              label: 'Open Settings',
                              textColor: Colors.white,
                              onPressed: () => openAppSettings(),
                            ),
                          ),
                        );
                      }
                    });
                  }
                } else {
                  // Permissions denied but not permanently
                  if (mounted) {
                    Future.microtask(() {
                      if (mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: const Text(
                              'Camera and microphone access is needed for video calls. Please allow when prompted.',
                            ),
                            backgroundColor: Colors.orange,
                            duration: const Duration(seconds: 5),
                          ),
                        );
                      }
                    });
                  }
                }
              } else {
                // Wait before retry
                await Future.delayed(const Duration(milliseconds: 500));
              }
            }
          } catch (e) {
            debugPrint('Error requesting permissions: $e');
            retryCount++;
            if (retryCount >= maxRetries) {
              if (mounted) {
                Future.microtask(() {
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Error requesting permissions: $e'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                });
              }
            }
          }
        }
      } else {
        // Web permission handling - permissions are requested by the web service during initialization
        debugPrint('Web platform - permissions will be requested by web service');
      }

      // Initialize Agora service (both web and mobile)
      try {
        await _agoraService.initialize();
        debugPrint('Agora service initialized successfully');
      } catch (e) {
        debugPrint('Error initializing Agora service: $e');
        if (mounted) {
          Future.microtask(() {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Error initializing video: $e'),
                  backgroundColor: Colors.red,
                ),
              );
            }
          });
        }
        return;
      }

      // Set up event handlers
      _agoraService.onUserJoined = (uid) {
        debugPrint('Remote user joined: $uid (Local UID: $_localUid)');

        // Only set remote UID if it's different from our local UID
        if (uid != _localUid && mounted) {
          debugPrint(
              '🚨 BEFORE setState: _remoteUid = $_remoteUid, new uid = $uid');
          setState(() {
            _remoteUid = uid;
          });
          debugPrint('🚨 AFTER setState: _remoteUid = $_remoteUid');
          debugPrint('🚨 FORCING UI REBUILD...');

          // Force an additional rebuild to ensure UI updates
          Future.delayed(const Duration(milliseconds: 100), () {
            if (mounted) {
              debugPrint('🚨 DELAYED REBUILD: _remoteUid = $_remoteUid');
              setState(() {
                // Force rebuild
              });
            }
          });

          // Debug: Remote user joined
          if (kDebugMode) {
            debugPrint(
                'Remote user joined - Local UID: $_localUid, Remote UID: $_remoteUid');
          }

          Future.microtask(() {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                      '${widget.isProvider ? "Patient" : "Provider"} has joined the call'),
                  backgroundColor: Colors.green,
                  duration: const Duration(seconds: 2),
                ),
              );
            }
          });
        } else {
          debugPrint(
              'Ignoring user joined event for local user or invalid state');
        }
      };

      _agoraService.onUserLeft = (uid) {
        debugPrint('Remote user left: $uid (Current remote UID: $_remoteUid)');

        // Only clear remote UID if it matches the user who left
        if (uid == _remoteUid && mounted) {
          setState(() {
            _remoteUid = null;
          });
          debugPrint('Remote UID cleared');
          Future.microtask(() {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                      '${widget.isProvider ? "Patient" : "Provider"} has left the call'),
                  backgroundColor: Colors.orange,
                  duration: const Duration(seconds: 2),
                ),
              );
            }
          });
        } else {
          debugPrint(
              'Ignoring user left event for different user or invalid state');
        }
      };

      _agoraService.onConnectionStateChanged = (state) {
        debugPrint('Connection state changed: $state');
        if (mounted) {
          String message;
          Color color;
          switch (state) {
            case ConnectionStateType.connectionStateConnected:
              message = 'Connected to video call';
              color = Colors.green;
              break;
            case ConnectionStateType.connectionStateDisconnected:
              message = 'Disconnected from video call';
              color = Colors.orange;
              break;
            case ConnectionStateType.connectionStateFailed:
              message = 'Failed to connect to video call';
              color = Colors.red;
              break;
            default:
              return;
          }
          Future.microtask(() {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text(message), backgroundColor: color),
              );
            }
          });
        }
      };

      _agoraService.onError = (error) {
        debugPrint('Agora error: $error');
        if (mounted) {
          Future.microtask(() {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Video call error: $error'),
                  backgroundColor: Colors.red,
                ),
              );
            }
          });
        }
      };

      // Join the channel (both web and mobile)
      debugPrint(
          'Joining Agora channel for appointment: ${widget.appointmentId}');
      final success = await _agoraService.joinChannel(
        appointmentId: widget.appointmentId,
        isProvider: widget.isProvider,
      );

      if (success) {
        debugPrint(
            'Successfully joined the channel as ${widget.isProvider ? "Provider" : "Patient"}');

        // Store the local UID for proper video rendering
        _localUid = _agoraService.uid;
        debugPrint('Local UID set to: $_localUid');

        // Debug: Channel joined successfully
        if (kDebugMode) {
          debugPrint(
              'Channel joined successfully - Local UID: $_localUid, Channel: ${_agoraService.channelName}');
        }

        if (mounted) {
          setState(() {
            _isAgoraInitialized = true;
          });
          Future.microtask(() {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Joined video call successfully'),
                  backgroundColor: Colors.green,
                ),
              );
            }
          });
        }

        // Start periodic check for remote users (especially useful for web)
        _startPeriodicCheck();
      } else {
        debugPrint('Failed to join the channel');
        if (mounted) {
          Future.microtask(() {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Failed to join video call'),
                  backgroundColor: Colors.red,
                ),
              );
            }
          });
        }
      }
    } catch (e) {
      debugPrint('Error initializing Agora: $e');
      if (mounted) {
        Future.microtask(() {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Error setting up video call: $e'),
                backgroundColor: Colors.red,
              ),
            );
          }
        });
      }
    }
  }

  void _startPeriodicCheck() {
    _periodicTimer = Timer.periodic(const Duration(seconds: 3), (timer) {
      if (!mounted) {
        timer.cancel();
        return;
      }

      // For web platforms, we might need to manually check for remote users
      // since the web service might not always trigger callbacks properly
      if (kIsWeb && _remoteUid == null && _isAgoraInitialized) {
        debugPrint('Periodic check: Looking for remote users on web...');
        // The web service should handle this, but we can add a fallback here if needed
      }

      // Debug: Periodic check
      if (kDebugMode) {
        debugPrint(
            'Periodic check - Local UID: $_localUid, Remote UID: $_remoteUid, Initialized: $_isAgoraInitialized');
      }
    });
  }

  Widget _buildLocalVideo() {
    if (kIsWeb) {
      // For web, the video is handled by the web service and displayed in HTML containers
      // Show a placeholder that indicates the video is active
      return Container(
        color: Colors.black54,
        child: const Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.videocam,
                color: Colors.white70,
                size: 24,
              ),
              SizedBox(height: 4),
              Text(
                'You (Web)',
                style: TextStyle(
                  color: Colors.white70,
                  fontSize: 10,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    // Mobile implementation
    if (!_isAgoraInitialized || (_agoraService.engine == null)) {
      return Container(
        color: Colors.black54,
        child: const Center(
          child: CircularProgressIndicator(color: Colors.white),
        ),
      );
    }

    try {
      // Use the actual local UID instead of hardcoded 0
      final localUid = _localUid ?? 0;
      debugPrint('Building local video with UID: $localUid');

      return AgoraVideoView(
        controller: VideoViewController(
          rtcEngine: _agoraService.engine!,
          canvas: VideoCanvas(uid: localUid),
        ),
      );
    } catch (e) {
      debugPrint('Error building local video: $e');
      return Container(
        color: Colors.black54,
        child: const Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.videocam_off,
                color: Colors.white70,
                size: 24,
              ),
              SizedBox(height: 4),
              Text(
                'Camera unavailable',
                style: TextStyle(
                  color: Colors.white70,
                  fontSize: 10,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }
  }

  Widget _buildRemoteVideo() {
    debugPrint('🎯 _buildRemoteVideo called: _remoteUid = $_remoteUid');
    if (_remoteUid == null) {
      debugPrint('🎯 Showing waiting message because _remoteUid is null');
      return Container(
        width: double.infinity,
        height: double.infinity,
        color: Colors.black,
        child: const Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.person_outline,
                color: Color(0xFFEC4899),
                size: 80,
              ),
              SizedBox(height: 20),
              Text(
                'Waiting for other participant to join...',
                style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 10),
              Text(
                'Your camera and microphone are active',
                style: TextStyle(color: Colors.white70, fontSize: 14),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    debugPrint(
        'Building remote video view for UID: $_remoteUid (Local UID: $_localUid)');

    if (kIsWeb) {
      // For web, show a placeholder indicating remote user is connected
      // The actual video rendering is handled by AgoraWebService
      return Container(
        width: double.infinity,
        height: double.infinity,
        color: Colors.black,
        child: Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(
                Icons.person,
                color: Color(0xFFEC4899),
                size: 80,
              ),
              const SizedBox(height: 20),
              Text(
                '${widget.isProvider ? "Patient" : "Provider"} is connected',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 10),
              Text(
                'Video call is active (Web)\nRemote UID: $_remoteUid',
                style: const TextStyle(color: Colors.white70, fontSize: 14),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    // Mobile implementation
    if (!_isAgoraInitialized ||
        (!kIsWeb &&
            (_agoraService.engine == null ||
                _agoraService.channelName == null))) {
      return Container(
        width: double.infinity,
        height: double.infinity,
        color: Colors.black,
        child: const Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(color: Colors.white),
              SizedBox(height: 20),
              Text(
                'Connecting to remote video...',
                style: TextStyle(color: Colors.white70, fontSize: 14),
              ),
            ],
          ),
        ),
      );
    }

    // For mobile platforms, use AgoraVideoView
    if (!kIsWeb) {
      try {
        debugPrint(
            'Creating remote video view with UID: $_remoteUid, Channel: ${_agoraService.channelName}');

        return AgoraVideoView(
          controller: VideoViewController.remote(
            rtcEngine: _agoraService.engine!,
            canvas: VideoCanvas(uid: _remoteUid),
            connection: RtcConnection(channelId: _agoraService.channelName!),
          ),
        );
      } catch (e) {
        debugPrint('Error building remote video: $e');
        return Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(Icons.error_outline, color: Colors.red, size: 48),
              const SizedBox(height: 16),
              const Text(
                'Unable to display remote video',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton.icon(
                icon: const Icon(Icons.refresh),
                label: const Text('Retry Connection'),
                onPressed: () {
                  if (mounted) {
                    setState(() {
                      // Force rebuild
                    });
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),
        );
      }
    }

    // For web platforms, the video is handled by HTML containers
    // Show a transparent container since the actual video is rendered by the web service
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: Colors.transparent,
    );
  }

  Future<void> _onToggleAudio() async {
    if (!_isAgoraInitialized) {
      _showInitializingMessage();
      return;
    }

    if (mounted) {
      setState(() {
        _isTogglingAudio = true;
      });
    }

    try {
      final enabled = await _agoraService.toggleLocalAudio();

      if (mounted) {
        setState(() {
          _localAudioMuted = !enabled;
          _isTogglingAudio = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(enabled ? 'Microphone unmuted' : 'Microphone muted'),
            duration: const Duration(seconds: 1),
          ),
        );
      }
    } catch (e) {
      debugPrint('Error toggling audio: $e');
      if (mounted) {
        setState(() {
          _isTogglingAudio = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error toggling audio: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    }
  }

  Future<void> _onToggleVideo() async {
    if (!_isAgoraInitialized) {
      _showInitializingMessage();
      return;
    }

    if (mounted) {
      setState(() {
        _isTogglingVideo = true;
      });
    }

    try {
      final enabled = await _agoraService.toggleLocalVideo();

      if (mounted) {
        setState(() {
          _localVideoMuted = !enabled;
          _isTogglingVideo = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(enabled ? 'Camera enabled' : 'Camera disabled'),
            duration: const Duration(seconds: 1),
          ),
        );
      }
    } catch (e) {
      debugPrint('Error toggling video: $e');
      if (mounted) {
        setState(() {
          _isTogglingVideo = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error toggling video: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    }
  }

  Future<void> _onSwitchCamera() async {
    if (!_isAgoraInitialized) {
      _showInitializingMessage();
      return;
    }

    if (mounted) {
      setState(() {
        _isSwitchingCamera = true;
      });
    }

    try {
      await _agoraService.switchCamera();

      if (mounted) {
        setState(() {
          _isSwitchingCamera = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Camera switched'),
            duration: Duration(seconds: 1),
          ),
        );
      }
    } catch (e) {
      debugPrint('Error switching camera: $e');
      if (mounted) {
        setState(() {
          _isSwitchingCamera = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error switching camera: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    }
  }

  void _showInitializingMessage() {
    if (mounted) {
      Future.microtask(() {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Video call is still initializing. Please wait...'),
              backgroundColor: Colors.orange,
              duration: Duration(seconds: 2),
            ),
          );
        }
      });
    }
  }

  Future<void> _onEndCall() async {
    debugPrint('Ending call for appointment: ${widget.appointmentId}');

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Ending call...'),
          duration: Duration(seconds: 1),
        ),
      );
    }

    setState(() {
      _isEndingCall = true;
    });

    try {
      debugPrint('Leaving Agora channel');
      await _agoraService.leaveChannel();

      // Call the API to end the consultation
      try {
        final apiService = ApiService();
        final result = await apiService.endVideoSession(widget.appointmentId);

        if (result['success']) {
          debugPrint('Consultation ended successfully');
          if (result.containsKey('consultation_duration')) {
            debugPrint(
                'Consultation duration: ${result['consultation_duration']}');
          }
        } else {
          debugPrint('Error ending consultation: ${result['message']}');
        }
      } catch (apiError) {
        debugPrint('API error ending consultation: $apiError');
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Call ended successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }

      if (mounted) {
        debugPrint('Navigating back from video call screen');
        Navigator.pop(context);
      }
    } catch (e) {
      debugPrint('Error ending call: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error ending call: $e'),
            backgroundColor: Colors.orange,
          ),
        );
        Navigator.pop(context);
      }
    }
  }

  Widget _buildControlButton({
    required IconData icon,
    required Color color,
    required bool isLoading,
    required VoidCallback onPressed,
  }) {
    return isLoading
        ? Container(
            width: 52,
            height: 52,
            decoration: BoxDecoration(
              color: const Color(0xFF2A2A2A),
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.4),
                  blurRadius: 6,
                  spreadRadius: 1.0,
                ),
              ],
              border: Border.all(color: const Color(0xFF3A3A3A), width: 1),
            ),
            padding: const EdgeInsets.all(12),
            child: const CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            ),
          )
        : Container(
            width: 52,
            height: 52,
            decoration: BoxDecoration(
              color: color == Colors.red
                  ? Colors.red.withValues(alpha: 0.3)
                  : const Color(0xFF2A2A2A),
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.4),
                  blurRadius: 6,
                  spreadRadius: 1.0,
                ),
              ],
              border: Border.all(color: const Color(0xFF3A3A3A), width: 1),
            ),
            child: IconButton(
              icon: Icon(
                icon,
                color: color,
                size: 26,
              ),
              onPressed: onPressed,
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(),
              splashRadius: 26,
            ),
          );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: Text('Video Consultation with ${widget.userName}'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: _onEndCall,
        ),
      ),
      body: Stack(
        children: [
          // Main content
          _connectionStatus == 'waiting'
              ? Center(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        width: 120,
                        height: 120,
                        decoration: BoxDecoration(
                          color: Colors.blue.withValues(alpha: 0.2),
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.medical_services,
                          color: Colors.blue,
                          size: 60,
                        ),
                      ),
                      const SizedBox(height: 30),
                      const Text(
                        'Waiting for Provider',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 15),
                      const Text(
                        'Please wait while the provider starts the video consultation',
                        style: TextStyle(
                          color: Colors.white70,
                          fontSize: 16,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 30),
                      const CircularProgressIndicator(
                        color: Colors.blue,
                        strokeWidth: 3,
                      ),
                    ],
                  ),
                )
              : !_isAgoraInitialized
                  ? Center(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const CircularProgressIndicator(color: Colors.white),
                          const SizedBox(height: 20),
                          Text(
                            _isConnecting
                                ? 'Connecting to video call...'
                                : 'Initializing video call...',
                            style: const TextStyle(
                                color: Colors.white70, fontSize: 16),
                          ),
                        ],
                      ),
                    )
                  : Stack(
                      children: [
                        // Remote video (full screen)
                        Center(child: _buildRemoteVideo()),

                        // Local video (picture-in-picture) - ALWAYS visible when initialized
                        Positioned(
                          right: 20,
                          bottom: 120, // Fixed position above controls
                          width: 100,
                          height: 140,
                          child: Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(10),
                              color: Colors.black54,
                              border:
                                  Border.all(color: Colors.white30, width: 1),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withValues(alpha: 0.3),
                                  blurRadius: 5,
                                  spreadRadius: 1,
                                ),
                              ],
                            ),
                            clipBehavior: Clip.hardEdge,
                            child: _buildLocalVideo(),
                          ),
                        ),

                        // Debug info (only in debug mode)
                        if (kDebugMode)
                          Positioned(
                            top: 50,
                            left: 16,
                            child: Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color:
                                    Colors.black.withAlpha(179), // 70% opacity
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Text(
                                    'DEBUG INFO',
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 12,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  Text(
                                    'Local UID: $_localUid',
                                    style: const TextStyle(
                                        color: Colors.white, fontSize: 10),
                                  ),
                                  Text(
                                    'Remote UID: $_remoteUid',
                                    style: const TextStyle(
                                        color: Colors.white, fontSize: 10),
                                  ),
                                  Text(
                                    'Initialized: $_isAgoraInitialized',
                                    style: const TextStyle(
                                        color: Colors.white, fontSize: 10),
                                  ),
                                ],
                              ),
                            ),
                          ),

                        // Manual test button (only in debug mode)
                        if (kDebugMode && kIsWeb)
                          Positioned(
                            top: 300,
                            left: 10,
                            child: ElevatedButton(
                              onPressed: () {
                                // Manually trigger remote user detection for testing
                                debugPrint(
                                    '🧪 MANUAL TEST: Simulating remote user join');
                                final testUid = widget.isProvider
                                    ? 1001
                                    : 2001; // Opposite role UID
                                _agoraService.onUserJoined?.call(testUid);
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.orange,
                                foregroundColor: Colors.white,
                              ),
                              child: const Text('Test Remote User'),
                            ),
                          ),

                        // Floating controls - ALWAYS visible when initialized
                        Positioned(
                          left: 0,
                          right: 0,
                          bottom: 20,
                          child: Center(
                            child: Container(
                              padding: const EdgeInsets.symmetric(
                                  vertical: 12, horizontal: 20),
                              margin:
                                  const EdgeInsets.symmetric(horizontal: 20),
                              decoration: BoxDecoration(
                                color: const Color(0xFF1E1E1E),
                                borderRadius: BorderRadius.circular(30),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withValues(alpha: 0.5),
                                    blurRadius: 10,
                                    spreadRadius: 2,
                                  ),
                                ],
                                border: Border.all(
                                    color: const Color(0xFF3A3A3A), width: 1),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  _buildControlButton(
                                    icon: _localAudioMuted
                                        ? Icons.mic_off
                                        : Icons.mic,
                                    color: _localAudioMuted
                                        ? Colors.red
                                        : Colors.white,
                                    isLoading: _isTogglingAudio,
                                    onPressed: _onToggleAudio,
                                  ),
                                  const SizedBox(width: 24),
                                  _buildControlButton(
                                    icon: _localVideoMuted
                                        ? Icons.videocam_off
                                        : Icons.videocam,
                                    color: _localVideoMuted
                                        ? Colors.red
                                        : Colors.white,
                                    isLoading: _isTogglingVideo,
                                    onPressed: _onToggleVideo,
                                  ),
                                  const SizedBox(width: 24),
                                  _buildControlButton(
                                    icon: Icons.switch_camera,
                                    color: Colors.white,
                                    isLoading: _isSwitchingCamera,
                                    onPressed: _onSwitchCamera,
                                  ),
                                  const SizedBox(width: 24),
                                  Container(
                                    width: 56,
                                    height: 56,
                                    decoration: BoxDecoration(
                                      color: Colors.red,
                                      shape: BoxShape.circle,
                                      boxShadow: [
                                        BoxShadow(
                                          color:
                                              Colors.red.withValues(alpha: 0.3),
                                          blurRadius: 8,
                                          spreadRadius: 2,
                                        ),
                                      ],
                                    ),
                                    child: IconButton(
                                      icon: const Icon(
                                        Icons.call_end,
                                        color: Colors.white,
                                        size: 28,
                                      ),
                                      onPressed: _onEndCall,
                                      padding: EdgeInsets.zero,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),

          // Call ending indicator
          if (_isEndingCall)
            Container(
              color: Colors.black54,
              width: double.infinity,
              height: double.infinity,
              child: const Center(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    CircularProgressIndicator(color: Colors.white),
                    SizedBox(height: 20),
                    Text(
                      'Ending call...',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
      // Remove bottom navigation bar since floating controls are always visible
      bottomNavigationBar: null,
    );
  }
}
