<script setup>
import { ref, onMounted, onUnmounted, nextTick, watch, toRefs } from 'vue';
import AgoraRTC from 'agora-rtc-sdk-ng';

const props = defineProps({
    isOpen: {
        type: Boolean,
        required: true
    },
    appointmentId: {
        type: [String, Number],
        required: true
    },
    appointment: {
        type: Object,
        required: false
    },
    userRole: {
        type: String,
        required: true
    }
});

const emit = defineEmits(['close']);

// Agora client and tracks
const client = ref(null);
const localAudioTrack = ref(null);
const localVideoTrack = ref(null);
const remoteUsers = ref({});
const joinedUsers = ref(new Set()); // Track users who have joined the channel

// UI state
const isConnecting = ref(false);
const isAudioMuted = ref(false);
const isVideoMuted = ref(false);
const connectionError = ref('');
const connectionTimeout = ref(null);

// Enhanced UI state for better UX
const isTogglingAudio = ref(false);
const isTogglingVideo = ref(false);
const isLeavingCall = ref(false);
const isEndingCall = ref(false);
const connectionStatus = ref('disconnected'); // disconnected, connecting, connected, reconnecting
const showSuccessMessage = ref(false);
const successMessage = ref('');

// Session data
const sessionData = ref(null);

// Polling for session status
const statusPollingInterval = ref(null);
const sessionStatus = ref(null);

// Track health monitoring
const trackHealthInterval = ref(null);

// Utility functions for better UX
const showSuccess = (message, duration = 3000) => {
    successMessage.value = message;
    showSuccessMessage.value = true;
    setTimeout(() => {
        showSuccessMessage.value = false;
    }, duration);
};

const updateConnectionStatus = (status) => {
    connectionStatus.value = status;
    console.log('🔗 Connection status updated:', status);
};

// Start waiting for provider
const startWaitingForProvider = () => {
    console.log('Patient entering waiting mode for provider');
    
    // Set up interval to check for provider
    const waitingInterval = setInterval(async () => {
        try {
            console.log('Checking for provider session...');
            const tokenMeta = document.querySelector('meta[name="csrf-token"]');
            const token = tokenMeta ? tokenMeta.getAttribute('content') : '';
            
            const response = await fetch(`/video/session/${props.appointmentId}`, {
                headers: {
                    'Accept': 'application/json',
                    'X-CSRF-TOKEN': token
                }
            });

            const data = await response.json();
            
            if (data.session_data) {
                console.log('Provider has started session! Initializing video call...');
                clearInterval(waitingInterval);
                sessionData.value = data.session_data;
                
                // Now start the actual video call
                await startVideoCallWithSession();
            }
        } catch (error) {
            console.error('Error checking for provider session:', error);
        }
    }, 3000); // Check every 3 seconds

    // Stop checking after 10 minutes
    setTimeout(() => {
        clearInterval(waitingInterval);
        if (connectionStatus.value === 'waiting') {
            connectionError.value = 'Provider did not start the session. Please try again later.';
        }
    }, 600000);
};

// Start video call with existing session data
const startVideoCallWithSession = async () => {
    try {
        isConnecting.value = true;
        updateConnectionStatus('connecting');

        // Create Agora client with optimal configuration
        client.value = AgoraRTC.createClient({
            mode: 'rtc',
            codec: 'vp8'  // VP8 for better compatibility and performance
        });
        
        // Enable debug mode for development
        AgoraRTC.enableLogUpload();
        AgoraRTC.setLogLevel(4); // Info level
        console.log('Agora client created');

        // Set up event listeners
        setupEventListeners();

        // Validate UID before joining
        if (!sessionData.value.uid || sessionData.value.uid === 0) {
            throw new Error(`Invalid UID received: ${sessionData.value.uid}`);
        }

        // Join channel
        console.log('Joining Agora channel:', sessionData.value.channel, 'with UID:', sessionData.value.uid);
        const joinResult = await client.value.join(
            sessionData.value.app_id,
            sessionData.value.channel,
            sessionData.value.token,
            parseInt(sessionData.value.uid) // Ensure UID is an integer
        );
        console.log('Successfully joined Agora channel, result:', joinResult);

        // Wait a moment for connection to stabilize, then verify and notify backend
        await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second

        if (client.value.connectionState === 'CONNECTED') {
            console.log('Agora connection verified, notifying backend');
            updateConnectionStatus('connected');
            await notifyBackendJoined();
            showSuccess('Connected to video call', 2000);
        } else {
            console.error('Agora connection not established, connection state:', client.value.connectionState);
            throw new Error('Failed to establish Agora connection');
        }

        // Create and publish local tracks immediately
        await createLocalTracks();
        await publishLocalTracks();
        console.log('Local tracks created and published');

        // Start session status polling
        startSessionStatusPolling();
        startTrackHealthCheck();

    } catch (error) {
        console.error('Error starting video call with session:', error);
        connectionError.value = error.message;
        isConnecting.value = false;
    }
};

// Initialize video call
const initializeCall = async () => {
    if (isConnecting.value) return;

    try {
        isConnecting.value = true;
        connectionError.value = '';
        updateConnectionStatus('connecting');
        console.log('Initializing video call for appointment:', props.appointmentId);

        // Check if appointment has video session
        if (props.appointment && !props.appointment.video_session_id && props.userRole === 'patient') {
            console.log('Patient joining but provider hasnt started session yet - entering waiting mode');
            isConnecting.value = false;
            updateConnectionStatus('waiting');
            startWaitingForProvider();
            return;
        }

        // Get session data from backend
        const tokenMeta = document.querySelector('meta[name="csrf-token"]');
        const token = tokenMeta ? tokenMeta.getAttribute('content') : '';
        
        const response = await fetch(`/video/session/${props.appointmentId}`, {
            headers: {
                'Accept': 'application/json',
                'X-CSRF-TOKEN': token
            }
        });

        const data = await response.json();
        
        // If no session exists and user is patient, wait for provider
        if (!data.session_data && props.userRole === 'patient') {
            console.log('No session data available - patient waiting for provider');
            isConnecting.value = false;
            updateConnectionStatus('waiting');
            startWaitingForProvider();
            return;
        }

        console.log('Session data received:', data);
        console.log('Raw session_data:', data.session_data);
        console.log('UID from session_data:', data.session_data?.uid);
        sessionData.value = data.session_data;

        // Create Agora client with optimal configuration
        client.value = AgoraRTC.createClient({
            mode: 'rtc',
            codec: 'vp8'  // VP8 for better compatibility and performance
        });
        
        // Enable debug mode for development
        AgoraRTC.enableLogUpload();
        AgoraRTC.setLogLevel(4); // Info level
        console.log('Agora client created');

        // Set up event listeners
        setupEventListeners();

        // Validate UID before joining
        if (!sessionData.value.uid || sessionData.value.uid === 0) {
            throw new Error(`Invalid UID received: ${sessionData.value.uid}`);
        }

        // Join channel
        console.log('Joining Agora channel:', sessionData.value.channel, 'with UID:', sessionData.value.uid);
        const joinResult = await client.value.join(
            sessionData.value.app_id,
            sessionData.value.channel,
            sessionData.value.token,
            parseInt(sessionData.value.uid) // Ensure UID is an integer
        );
        console.log('Successfully joined Agora channel, result:', joinResult);

        // Wait a moment for connection to stabilize, then verify and notify backend
        await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second

        if (client.value.connectionState === 'CONNECTED') {
            console.log('Agora connection verified, notifying backend');
            updateConnectionStatus('connected');
            await notifyBackendJoined();
            showSuccess('Connected to video call', 2000);
        } else {
            console.error('Agora connection not established, connection state:', client.value.connectionState);
            throw new Error('Failed to establish Agora connection');
        }

        // Create and publish local tracks immediately
        await createLocalTracks();
        await publishLocalTracks();
        console.log('Local tracks created and published');

        // Start session monitoring
        startSessionStatusPolling();
        
        // Check for existing remote users in the channel
        const existingUsers = client.value.remoteUsers;
        console.log('👥 Existing remote users in channel:', existingUsers.map(u => u.uid));

        // Subscribe to existing users
        for (const user of existingUsers) {
            if (user.hasVideo) {
                console.log('📹 Found existing user with video:', user.uid);
                await handleUserPublished(user, 'video');
            }
            if (user.hasAudio) {
                console.log('🔊 Found existing user with audio:', user.uid);
                await handleUserPublished(user, 'audio');
            }
        }

    } catch (error) {
        console.error('Error initializing video call:', error);
        connectionError.value = error.message;
    } finally {
        isConnecting.value = false;
    }
};

// Setup event listeners
const setupEventListeners = () => {
    client.value.on('user-joined', handleUserJoined);
    client.value.on('user-published', handleUserPublished);
    client.value.on('user-unpublished', handleUserUnpublished);
    client.value.on('user-left', handleUserLeft);
    client.value.on('connection-state-changed', handleConnectionStateChanged);
    client.value.on('exception', (evt) => {
        // Only log significant exceptions to reduce console noise
        if (evt.code && evt.code !== 'OPERATION_ABORTED') {
            console.warn('⚠️ Agora exception:', evt.code, evt.msg);
        }
    });

    console.log('✅ Agora event listeners set up');
};

// Handle user joined (before they publish media)
const handleUserJoined = (user) => {
    console.log('👤 User joined channel:', user.uid);
    joinedUsers.value.add(user.uid);

    // Clear connection timeout since someone joined
    if (connectionTimeout.value) {
        clearTimeout(connectionTimeout.value);
        connectionTimeout.value = null;
    }

    // Give the user time to fully register in the channel
    setTimeout(() => {
        console.log('🔍 Checking if user has published media after join...');
        const foundUser = client.value.remoteUsers.find(u => u.uid === user.uid);
        if (foundUser) {
            if (foundUser.hasVideo) {
                console.log('📹 User has video, subscribing...');
                handleUserPublished(foundUser, 'video');
            }
            if (foundUser.hasAudio) {
                console.log('🔊 User has audio, subscribing...');
                handleUserPublished(foundUser, 'audio');
            }
        }
    }, 200); // Small delay to ensure proper registration

    // Log current state for debugging
    console.log('📋 Current joined users:', Array.from(joinedUsers.value));
    console.log('📋 Current remote users:', Object.keys(remoteUsers.value));
};

// Handle connection state changes
const handleConnectionStateChanged = (curState, revState) => {
    console.log('Connection state changed:', curState, 'from:', revState);

    if (curState === 'CONNECTED') {
        // Start polling for session status
        startSessionStatusPolling();

        // Set a timeout to wait for other participants
        connectionTimeout.value = setTimeout(() => {
            console.log('Connection timeout - checking for participants');
        }, 10000); // 10 second timeout
    }
};

// Simplified session status polling
const startSessionStatusPolling = () => {
    if (statusPollingInterval.value) return;

    console.log('🔄 Starting session status polling');
    statusPollingInterval.value = setInterval(async () => {
        await checkSessionStatus();
    }, 5000); // Poll every 5 seconds - less frequent
};

// Stop polling session status
const stopSessionStatusPolling = () => {
    if (statusPollingInterval.value) {
        console.log('Stopping session status polling');
        clearInterval(statusPollingInterval.value);
        statusPollingInterval.value = null;
    }
};

// Simplified track monitoring
const startTrackHealthCheck = () => {
    if (trackHealthInterval.value) return;

    console.log('🔍 Starting track health monitoring');
    trackHealthInterval.value = setInterval(() => {
        // Simple check for track publishing status
        if (client.value && localAudioTrack.value && localVideoTrack.value) {
            const publishedTracks = client.value.localTracks || [];
            const hasAudio = publishedTracks.some(t => t.trackMediaType === 'audio');
            const hasVideo = publishedTracks.some(t => t.trackMediaType === 'video');

            if (!hasAudio || !hasVideo) {
                console.log('⚠️ Track health issue detected, republishing tracks');
                publishLocalTracks();
            }
        }
    }, 10000); // Check every 10 seconds
};

const stopTrackHealthCheck = () => {
    if (trackHealthInterval.value) {
        clearInterval(trackHealthInterval.value);
        trackHealthInterval.value = null;
    }
};

// Simplified session status check
const checkSessionStatus = async () => {
    try {
        const tokenMeta = document.querySelector('meta[name="csrf-token"]');
        const token = tokenMeta ? tokenMeta.getAttribute('content') : '';
        
        const response = await fetch(`/video/status/${props.appointmentId}`, {
            headers: {
                'Accept': 'application/json',
                'X-CSRF-TOKEN': token
            }
        });

        if (response.ok) {
            const data = await response.json();
            sessionStatus.value = data;
            // Just log status, don't try to sync participants
            console.log('📊 Session status updated:', data.success ? 'active' : 'inactive');
        }
    } catch (error) {
        console.error('❌ Error checking session status:', error);
    }
};

// Removed complex participant synchronization logic to prevent race conditions

// Leave session (for when user wants to leave but session continues)
const leaveSession = async () => {
    console.log('🚪 Leaving video session');

    // Stop all monitoring
    stopSessionStatusPolling();
    stopTrackHealthCheck();

    if (connectionTimeout.value) {
        clearTimeout(connectionTimeout.value);
        connectionTimeout.value = null;
    }

    try {
        // Notify backend
        await leaveSessionOnBackend();
        
        // Leave Agora channel
        if (client.value) {
            await client.value.leave();
            console.log('✅ Left Agora channel');
        }
    } catch (error) {
        console.error('❌ Error leaving session:', error);
    }

    // Clear state
    remoteUsers.value = {};
    joinedUsers.value.clear();
    updateConnectionStatus('disconnected');
};

// Cleanup call resources (complete cleanup when ending call)
const cleanupCall = async () => {
    console.log('🧹 Starting complete call cleanup');

    // First leave the session
    await leaveSession();

    // Now destroy local tracks completely
    if (localAudioTrack.value) {
        localAudioTrack.value.stop();
        localAudioTrack.value.close();
        localAudioTrack.value = null;
        console.log('🧹 Local audio track destroyed');
    }
    if (localVideoTrack.value) {
        localVideoTrack.value.stop();
        localVideoTrack.value.close();
        localVideoTrack.value = null;
        console.log('🧹 Local video track destroyed');
    }
    if (client.value) {
        client.value = null;
        console.log('🧹 Agora client destroyed');
    }

    // Reset all state
    isConnecting.value = false;
    isAudioMuted.value = false;
    isVideoMuted.value = false;
    connectionError.value = '';
    sessionData.value = null;
    sessionStatus.value = null;

    console.log('🧹 Complete call cleanup finished');
};

// Notify backend that user has successfully joined the Agora channel
const notifyBackendJoined = async () => {
    try {
        const tokenMeta = document.querySelector('meta[name="csrf-token"]');
        const token = tokenMeta ? tokenMeta.getAttribute('content') : '';
        
        const response = await fetch(`/video/join/${props.appointmentId}`, {
            method: 'POST',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': token
            }
        });

        if (response.ok) {
            const data = await response.json();
            console.log('Successfully notified backend of join:', data);
        } else {
            console.error('Failed to notify backend of join:', response.status);
        }
    } catch (error) {
        console.error('Error notifying backend of join:', error);
    }
};

// Leave session on backend (participant leaves but session continues)
const leaveSessionOnBackend = async () => {
    try {
        const tokenMeta = document.querySelector('meta[name="csrf-token"]');
        const token = tokenMeta ? tokenMeta.getAttribute('content') : '';
        
        const response = await fetch(`/video/leave/${props.appointmentId}`, {
            method: 'POST',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': token
            }
        });

        if (response.ok) {
            const data = await response.json();
            console.log('Left session on backend:', data);
        } else {
            console.error('Failed to leave session on backend:', response.status);
        }
    } catch (error) {
        console.error('Error leaving session on backend:', error);
    }
};

// Notify backend that a user has disconnected from Agora
const notifyBackendUserDisconnected = async (userId) => {
    try {
        const tokenMeta = document.querySelector('meta[name="csrf-token"]');
        const token = tokenMeta ? tokenMeta.getAttribute('content') : '';
        
        const response = await fetch(`/video/participant-disconnected/${props.appointmentId}`, {
            method: 'POST',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': token
            },
            body: JSON.stringify({
                user_id: userId
            })
        });

        if (response.ok) {
            const data = await response.json();
            console.log('Notified backend of user disconnect:', data);
        } else {
            console.error('Failed to notify backend of user disconnect:', response.status);
        }
    } catch (error) {
        console.error('Error notifying backend of user disconnect:', error);
    }
};

// Create local audio and video tracks
const createLocalTracks = async () => {
    try {
        console.log('🎬 Creating local tracks...');
        
        // Create tracks with optimal settings
        localAudioTrack.value = await AgoraRTC.createMicrophoneAudioTrack({
            echoCancellation: true,
            noiseSuppression: true,
            autoGainControl: true
        });
        
        localVideoTrack.value = await AgoraRTC.createCameraVideoTrack({
            optimizationMode: 'motion',
            encoderConfig: {
                width: 640,
                height: 480,
                frameRate: 15,
                bitrateMax: 1000,
                bitrateMin: 600
            }
        });
        
        console.log('✅ Local tracks created successfully');
        
        // Play local video immediately
        await nextTick();
        const localVideoElement = document.getElementById('local-video');
        if (localVideoElement && localVideoTrack.value) {
            await localVideoTrack.value.play(localVideoElement);
            console.log('✅ Local video playing');
        }
    } catch (error) {
        console.error('❌ Error creating local tracks:', error);
        connectionError.value = 'Failed to access camera/microphone. Please check permissions.';
        throw error;
    }
};

// Publish local tracks - simplified
const publishLocalTracks = async () => {
    try {
        if (!client.value || !localAudioTrack.value || !localVideoTrack.value) {
            console.error('❌ Cannot publish: Missing client or tracks');
            return false;
        }

        console.log('📡 Publishing local tracks...');
        await client.value.publish([localAudioTrack.value, localVideoTrack.value]);
        console.log('✅ Local tracks published successfully');
        
        return true;
    } catch (error) {
        console.error('❌ Error publishing tracks:', error);
        return false;
    }
};

// Force check for a specific remote user
const forceCheckRemoteUser = async (uid) => {
    try {
        if (!client.value) return;

        const agoraUid = uid; // UID should already be integer from backend
        console.log('Force checking for remote user:', agoraUid);
        const remoteUsers = client.value.remoteUsers || [];
        console.log('Current remote users in client:', remoteUsers.map(u => u.uid));

        const targetUser = remoteUsers.find(u => u.uid == agoraUid);
        if (targetUser) {
            console.log('Found target user:', agoraUid);

            if (!joinedUsers.value.has(agoraUid)) {
                handleUserJoined(targetUser);
            }

            if (targetUser.hasVideo && targetUser.videoTrack) {
                await handleUserPublished(targetUser, 'video');
            }
            if (targetUser.hasAudio && targetUser.audioTrack) {
                await handleUserPublished(targetUser, 'audio');
            }
        } else {
            console.log('User not found in Agora remote users:', agoraUid);
        }

    } catch (error) {
        console.error('Error in force check remote user:', error);
    }
};

// Handle remote user published - fixed timing issues
const handleUserPublished = async (user, mediaType) => {
    console.log('🎥 User published media:', user.uid, 'type:', mediaType);
    
    try {
        // Add a small delay to ensure user is properly registered in channel
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // Get fresh reference to user from client.remoteUsers
        const currentUser = client.value.remoteUsers.find(u => u.uid === user.uid);
        if (!currentUser) {
            console.warn('⚠️ User not found in remoteUsers, waiting for proper registration...');
            // Wait a bit more and try again
            await new Promise(resolve => setTimeout(resolve, 300));
            const retryUser = client.value.remoteUsers.find(u => u.uid === user.uid);
            if (!retryUser) {
                console.error('❌ User still not found after retry, skipping subscription');
                return;
            }
            // Use the retry user
            user = retryUser;
        } else {
            user = currentUser;
        }
        
        // Verify the user actually has the media type
        if (mediaType === 'video' && !user.hasVideo) {
            console.warn('⚠️ User does not have video published');
            return;
        }
        if (mediaType === 'audio' && !user.hasAudio) {
            console.warn('⚠️ User does not have audio published');
            return;
        }
        
        // Subscribe to the user's media
        console.log('🔄 Subscribing to user:', user.uid, 'mediaType:', mediaType);
        await client.value.subscribe(user, mediaType);
        console.log('✅ Successfully subscribed to user:', user.uid, 'mediaType:', mediaType);

        if (mediaType === 'video' && user.videoTrack) {
            console.log('🎬 Playing remote video for user:', user.uid);
            
            // Wait for DOM to be ready
            await nextTick();
            
            const remoteVideoElement = document.getElementById('remote-video');
            if (remoteVideoElement) {
                // Clear any existing content
                if (remoteVideoElement.srcObject) {
                    remoteVideoElement.srcObject = null;
                }
                
                // Play the remote video
                await user.videoTrack.play(remoteVideoElement);
                console.log('✅ Remote video playing for user:', user.uid);
                
                // Show success message
                const roleText = props.userRole === 'provider' ? 'Patient' : 'Provider';
                showSuccess(`${roleText} video connected`, 2000);
            } else {
                console.error('❌ Remote video element not found');
            }
        }

        if (mediaType === 'audio' && user.audioTrack) {
            await user.audioTrack.play();
            console.log('🔊 Playing remote audio for user:', user.uid);
        }

        // Store the user reference
        remoteUsers.value[user.uid] = user;
        console.log('📊 Remote users count:', Object.keys(remoteUsers.value).length);

    } catch (error) {
        console.error('❌ Error subscribing to user:', user.uid, 'mediaType:', mediaType, 'error:', error);
        
        // Handle specific error cases
        if (error.code === 'INVALID_REMOTE_USER') {
            console.log('🔄 User not properly registered yet, retrying in 500ms...');
            setTimeout(async () => {
                // Try to find user again
                const foundUser = client.value.remoteUsers.find(u => u.uid === user.uid);
                if (foundUser) {
                    console.log('✅ Found user on retry, attempting subscription...');
                    await handleUserPublished(foundUser, mediaType);
                } else {
                    console.error('❌ User still not found on retry');
                }
            }, 500);
        }
    }
};

// Handle remote user unpublished
const handleUserUnpublished = (user, mediaType) => {
    console.log('User unpublished media:', user.uid, 'type:', mediaType);

    if (mediaType === 'video') {
        const remoteVideoElement = document.getElementById('remote-video');
        if (remoteVideoElement) {
            remoteVideoElement.srcObject = null;
        }
    }
};

// Handle remote user left
const handleUserLeft = (user) => {
    console.log('User left channel:', user.uid);
    delete remoteUsers.value[user.uid];
    joinedUsers.value.delete(user.uid);

    const remoteVideoElement = document.getElementById('remote-video');
    if (remoteVideoElement) {
        remoteVideoElement.srcObject = null;
    }

    // If all remote users have left, consider ending the session
    if (Object.keys(remoteUsers.value).length === 0 && joinedUsers.value.size === 0) {
        console.log('All participants have left, ending call in 3 seconds');
        setTimeout(() => {
            endCall();
        }, 3000); // Give 3 seconds for potential reconnection
    }
};

// Toggle audio - simplified
const toggleAudio = async () => {
    if (isTogglingAudio.value || !localAudioTrack.value) return;

    if (connectionStatus.value !== 'connected') {
        showSuccess('Please wait for the consultation to start', 2000);
        return;
    }

    try {
        isTogglingAudio.value = true;
        
        await localAudioTrack.value.setEnabled(isAudioMuted.value);
        isAudioMuted.value = !isAudioMuted.value;
        
        const message = isAudioMuted.value ? 'Microphone muted' : 'Microphone unmuted';
        showSuccess(message, 1500);
        
    } catch (error) {
        console.error('❌ Error toggling audio:', error);
        showSuccess('Failed to toggle microphone', 2000);
    } finally {
        isTogglingAudio.value = false;
    }
};

// Toggle video - simplified
const toggleVideo = async () => {
    if (isTogglingVideo.value || !localVideoTrack.value) return;

    if (connectionStatus.value !== 'connected') {
        showSuccess('Please wait for the consultation to start', 2000);
        return;
    }

    try {
        isTogglingVideo.value = true;
        
        await localVideoTrack.value.setEnabled(isVideoMuted.value);
        isVideoMuted.value = !isVideoMuted.value;
        
        // Re-attach video if enabled
        if (!isVideoMuted.value) {
            await nextTick();
            const localVideoElement = document.getElementById('local-video');
            if (localVideoElement) {
                await localVideoTrack.value.play(localVideoElement);
            }
        }
        
        const message = isVideoMuted.value ? 'Camera turned off' : 'Camera turned on';
        showSuccess(message, 1500);
        
    } catch (error) {
        console.error('❌ Error toggling video:', error);
        showSuccess('Failed to toggle camera', 2000);
    } finally {
        isTogglingVideo.value = false;
    }
};

// Leave call (user leaves but call continues for others)
const leaveCall = async () => {
    if (isLeavingCall.value) return; // Prevent double-clicks

    try {
        isLeavingCall.value = true;
        updateConnectionStatus('disconnecting');

        await leaveSession();
        showSuccess('You have left the call', 2000);

        // Small delay for better UX
        setTimeout(() => {
            emit('close');
        }, 500);
    } catch (error) {
        console.error('Error leaving call:', error);
        connectionError.value = 'Failed to leave call. Please try again.';
    } finally {
        isLeavingCall.value = false;
    }
};

// End call
const endCall = async () => {
    if (isEndingCall.value) return; // Prevent double-clicks

    try {
        isEndingCall.value = true;
        updateConnectionStatus('ending');

        // Notify backend to end the session
        await endSessionOnBackend();
        await cleanupCall();

        showSuccess('Call ended', 1500);

        // Small delay for better UX
        setTimeout(() => {
            emit('close');
        }, 500);
    } catch (error) {
        console.error('Error ending call:', error);
        connectionError.value = 'Failed to end call. Please try again.';
        isEndingCall.value = false;
    }
};

// End session on backend
const endSessionOnBackend = async () => {
    try {
        const tokenMeta = document.querySelector('meta[name="csrf-token"]');
        const token = tokenMeta ? tokenMeta.getAttribute('content') : '';
        
        const response = await fetch(`/video/end/${props.appointmentId}`, {
            method: 'POST',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': token
            }
        });

        if (response.ok) {
            const data = await response.json();
            console.log('Session ended on backend:', data);
        } else {
            console.error('Failed to end session on backend:', response.status);
        }
    } catch (error) {
        console.error('Error ending session on backend:', error);
    }
};

// Reset component state
const resetComponentState = () => {
    console.log('🔄 Resetting component state');
    isConnecting.value = false;
    isAudioMuted.value = false;
    isVideoMuted.value = false;
    connectionError.value = '';
    remoteUsers.value = {};
    joinedUsers.value.clear();
    sessionData.value = null;
    sessionStatus.value = null;
};

// Watch for modal open/close to handle initialization
const { isOpen, appointment } = toRefs(props);
watch(isOpen, async (newValue, oldValue) => {
    if (newValue && !oldValue) {
        // Modal is opening
        console.log('📹 Video modal opening, initializing call...');
        resetComponentState();
        await initializeCall();
    } else if (!newValue && oldValue) {
        // Modal is closing
        console.log('📹 Video modal closing, leaving session...');
        await leaveSession();
    }
});

// Watch for appointment changes (when provider starts session)
watch(appointment, async (newAppointment, oldAppointment) => {
    if (newAppointment && oldAppointment && 
        newAppointment.video_session_id && !oldAppointment.video_session_id &&
        connectionStatus.value === 'waiting') {
        console.log('🔄 Appointment updated with video session, reinitializing...');
        await initializeCall();
    }
}, { deep: true });

// Initialize when modal opens
onMounted(() => {
    if (props.isOpen) {
        resetComponentState();
        initializeCall();
    }
});

// Cleanup on unmount and page unload
onUnmounted(() => {
    cleanupCall();
});

// Also cleanup when page is about to unload (user closes browser/tab)
if (typeof window !== 'undefined') {
    window.addEventListener('beforeunload', () => {
        if (client.value && sessionData.value) {
            // Quick cleanup without waiting for promises
            try {
                client.value.leave();
            } catch (error) {
                console.error('Error during page unload cleanup:', error);
            }
        }
    });
}
</script>

<template>
    <div v-if="isOpen" class="fixed inset-0 z-50 overflow-y-auto bg-white bg-opacity-10 backdrop-blur-sm">
        <div class="flex min-h-screen items-center justify-center p-4">
            <div class="bg-white rounded-xl shadow-2xl w-full max-w-5xl max-h-[95vh] min-h-[600px] flex flex-col overflow-hidden">
                <!-- Header -->
                <div class="flex items-center justify-between px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-gray-50 to-white shrink-0">
                    <div class="flex items-center space-x-4">
                        <div class="flex items-center space-x-2">
                            <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                                <i class="fas fa-video text-white text-sm"></i>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-900">
                                Video Consultation
                            </h3>
                        </div>
                        <div
                            :class="[
                                'connection-indicator px-3 py-1.5 rounded-full text-xs font-medium shadow-sm',
                                connectionStatus === 'connected' ? 'bg-green-100 text-green-800 connected' :
                                connectionStatus === 'connecting' ? 'bg-yellow-100 text-yellow-800 connecting' :
                                connectionStatus === 'waiting' ? 'bg-blue-100 text-blue-800 waiting' :
                                connectionStatus === 'disconnecting' ? 'bg-orange-100 text-orange-800' :
                                connectionStatus === 'ending' ? 'bg-red-100 text-red-800' :
                                'bg-gray-100 text-gray-800 disconnected'
                            ]"
                        >
                            {{ connectionStatus === 'connected' ? 'Connected' :
                               connectionStatus === 'connecting' ? 'Connecting...' :
                               connectionStatus === 'waiting' ? 'Waiting for provider...' :
                               connectionStatus === 'disconnecting' ? 'Leaving...' :
                               connectionStatus === 'ending' ? 'Ending...' :
                               'Disconnected' }}
                        </div>
                    </div>
                    <button
                        @click="endCall"
                        :disabled="isEndingCall"
                        class="text-gray-400 hover:text-gray-600 p-2 rounded-lg hover:bg-gray-100 transition-all duration-200 disabled:opacity-50"
                    >
                        <i class="fas fa-times text-lg"></i>
                    </button>
                </div>

                <!-- Video Container -->
                <div class="flex-1 relative bg-gradient-to-br from-gray-900 to-gray-800 overflow-hidden">
                    <!-- Remote Video (Main) -->
                    <div class="w-full h-full relative">
                        <video
                            id="remote-video"
                            class="w-full h-full object-cover rounded-lg"
                            autoplay
                            playsinline
                        ></video>
                        <!-- Waiting for other participant -->
                        <div v-if="Object.keys(remoteUsers).length === 0 && joinedUsers.size === 0" class="absolute inset-0 flex items-center justify-center">
                            <div class="text-center text-white max-w-md mx-auto px-6">
                                <div v-if="connectionStatus === 'waiting'" class="mb-8">
                                    <div class="w-20 h-20 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-6 animate-pulse shadow-lg">
                                        <i class="fas fa-user-md text-2xl text-white"></i>
                                    </div>
                                    <h3 class="text-xl font-semibold mb-3">Waiting for Provider</h3>
                                    <p class="text-gray-300 mb-6 text-sm leading-relaxed">Please wait while the provider starts the consultation.</p>
                                    <div class="flex justify-center items-center space-x-1">
                                        <div class="w-2 h-2 bg-blue-400 rounded-full animate-bounce"></div>
                                        <div class="w-2 h-2 bg-blue-400 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
                                        <div class="w-2 h-2 bg-blue-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
                                    </div>
                                </div>
                                <div v-else>
                                    <div class="w-16 h-16 bg-gradient-to-br from-gray-600 to-gray-700 rounded-2xl flex items-center justify-center mx-auto mb-6">
                                        <i class="fas fa-user-circle text-2xl text-gray-300"></i>
                                    </div>
                                    <p v-if="userRole === 'provider'" class="text-lg mb-2">Waiting for patient to join...</p>
                                    <p v-else class="text-lg mb-2">Waiting for provider to start the call...</p>
                                    <p v-if="sessionStatus && sessionStatus.session" class="text-xs mt-3 opacity-60 bg-black bg-opacity-20 rounded-lg px-3 py-2 inline-block">
                                        Session: {{ sessionStatus.session.status }} | Participants: {{ sessionStatus.session.participants }}
                                    </p>
                                    <div class="mt-6">
                                        <div class="animate-pulse flex space-x-1 justify-center">
                                            <div class="w-1.5 h-1.5 bg-white rounded-full"></div>
                                            <div class="w-1.5 h-1.5 bg-white rounded-full animation-delay-200"></div>
                                            <div class="w-1.5 h-1.5 bg-white rounded-full animation-delay-400"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Show when users joined but haven't published media yet -->
                        <div v-if="Object.keys(remoteUsers).length === 0 && joinedUsers.size > 0" class="absolute inset-0 flex items-center justify-center">
                            <div class="text-center text-white max-w-md mx-auto px-6">
                                <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-6 animate-pulse shadow-lg">
                                    <i class="fas fa-video text-2xl text-white"></i>
                                </div>
                                <p v-if="userRole === 'provider'" class="text-lg mb-2">Patient connected, waiting for video...</p>
                                <p v-else class="text-lg mb-2">Provider connected, waiting for video...</p>
                                <p v-if="sessionStatus && sessionStatus.session" class="text-xs mt-3 opacity-60 bg-black bg-opacity-20 rounded-lg px-3 py-2 inline-block">
                                    Session: {{ sessionStatus.session.status }} | Participants: {{ sessionStatus.session.participants }}
                                </p>
                                <div class="mt-6">
                                    <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-white mx-auto"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Show participant info when video is active -->
                        <div v-if="Object.keys(remoteUsers).length > 0" class="absolute top-4 left-4 bg-black bg-opacity-60 text-white px-3 py-2 rounded-lg text-sm backdrop-blur-sm">
                            <i class="fas fa-video mr-2 text-green-400"></i>
                            <span v-if="userRole === 'provider'">Patient Video</span>
                            <span v-else>Provider Video</span>
                        </div>
                    </div>

                    <!-- Local Video (Picture-in-Picture) -->
                    <div class="absolute top-4 right-4 w-44 h-32 sm:w-48 sm:h-36 bg-gray-800 rounded-xl overflow-hidden border-2 border-white shadow-xl">
                        <!-- Local video label -->
                        <div class="absolute top-2 left-2 bg-black bg-opacity-60 text-white px-2 py-1 rounded-md text-xs z-10 backdrop-blur-sm">
                            <i class="fas fa-user mr-1 text-blue-400"></i>
                            You
                        </div>
                        <video
                            id="local-video"
                            class="w-full h-full object-cover"
                            autoplay
                            playsinline
                            muted
                        ></video>
                        <div v-if="isVideoMuted" class="absolute inset-0 flex items-center justify-center bg-gray-800">
                            <div class="text-center">
                                <i class="fas fa-video-slash text-white text-xl mb-2"></i>
                                <p class="text-white text-xs">Camera Off</p>
                            </div>
                        </div>
                    </div>

                    <!-- Connection Status -->
                    <div v-if="isConnecting" class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-75 backdrop-blur-sm">
                        <div class="text-center text-white bg-gray-800 bg-opacity-95 rounded-xl p-8 max-w-sm mx-4 shadow-2xl">
                            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-400 mx-auto mb-6"></div>
                            <p class="text-lg font-medium mb-3">
                                {{ connectionStatus === 'connecting' ? 'Connecting to video call...' :
                                   connectionStatus === 'waiting' ? 'Preparing consultation...' :
                                   connectionStatus === 'disconnecting' ? 'Leaving call...' :
                                   connectionStatus === 'ending' ? 'Ending call...' : 'Connecting...' }}
                            </p>
                            <p class="text-sm text-gray-300">Please wait a moment</p>
                        </div>
                    </div>

                    <!-- Success Message -->
                    <div v-if="showSuccessMessage" class="absolute top-6 left-1/2 transform -translate-x-1/2 z-20">
                        <div class="bg-green-500 text-white px-4 py-3 rounded-xl shadow-lg flex items-center space-x-3 animate-fade-in backdrop-blur-sm">
                            <i class="fas fa-check-circle text-lg"></i>
                            <span class="font-medium">{{ successMessage }}</span>
                        </div>
                    </div>

                    <!-- Error Message -->
                    <div v-if="connectionError" class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-60 backdrop-blur-sm">
                        <div class="text-center text-white bg-gray-800 bg-opacity-95 rounded-xl p-8 max-w-md mx-4 shadow-2xl">
                            <div class="w-16 h-16 bg-red-500 rounded-2xl flex items-center justify-center mx-auto mb-6">
                                <i class="fas fa-exclamation-triangle text-2xl text-white"></i>
                            </div>
                            <h3 class="text-lg font-semibold mb-3">Connection Error</h3>
                            <p class="mb-6 text-gray-300 leading-relaxed">{{ connectionError }}</p>
                            <button @click="endCall" class="px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors font-medium">
                                Close
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Controls -->
                <div class="px-6 py-5 bg-gradient-to-r from-gray-50 via-white to-gray-50 border-t border-gray-200 shrink-0">
                    <div class="flex items-center justify-center space-x-4">
                        <!-- Audio Toggle -->
                        <div class="flex flex-col items-center">
                            <button
                                @click="toggleAudio"
                                :disabled="isTogglingAudio || isConnecting || connectionStatus === 'waiting'"
                                :class="[
                                    'relative p-3 rounded-xl transition-all duration-200 text-white shadow-md transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none',
                                    connectionStatus === 'waiting' ? 'bg-gray-400' :
                                    isAudioMuted ? 'bg-red-500 hover:bg-red-600' : 'bg-blue-500 hover:bg-blue-600'
                                ]"
                                :title="connectionStatus === 'waiting' ? 'Please wait for consultation to start' :
                                       isAudioMuted ? 'Unmute microphone' : 'Mute microphone'"
                            >
                                <i v-if="!isTogglingAudio" :class="isAudioMuted ? 'fas fa-microphone-slash text-lg' : 'fas fa-microphone text-lg'"></i>
                                <div v-else class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                            </button>
                            <span class="text-xs text-gray-600 mt-2 font-medium">{{ isAudioMuted ? 'Muted' : 'Audio' }}</span>
                        </div>

                        <!-- Video Toggle -->
                        <div class="flex flex-col items-center">
                            <button
                                @click="toggleVideo"
                                :disabled="isTogglingVideo || isConnecting || connectionStatus === 'waiting'"
                                :class="[
                                    'relative p-3 rounded-xl transition-all duration-200 text-white shadow-md transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none',
                                    connectionStatus === 'waiting' ? 'bg-gray-400' :
                                    isVideoMuted ? 'bg-red-500 hover:bg-red-600' : 'bg-blue-500 hover:bg-blue-600'
                                ]"
                                :title="connectionStatus === 'waiting' ? 'Please wait for consultation to start' :
                                       isVideoMuted ? 'Turn on camera' : 'Turn off camera'"
                            >
                                <i v-if="!isTogglingVideo" :class="isVideoMuted ? 'fas fa-video-slash text-lg' : 'fas fa-video text-lg'"></i>
                                <div v-else class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                            </button>
                            <span class="text-xs text-gray-600 mt-2 font-medium">{{ isVideoMuted ? 'Off' : 'Video' }}</span>
                        </div>

                        <!-- Leave Call -->
                        <div class="flex flex-col items-center">
                            <button
                                @click="leaveCall"
                                :disabled="isLeavingCall || isEndingCall || isConnecting"
                                class="relative p-3 rounded-xl bg-yellow-500 hover:bg-yellow-600 text-white transition-all duration-200 shadow-md transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                                title="Leave call (others can continue)"
                            >
                                <i v-if="!isLeavingCall" class="fas fa-sign-out-alt text-lg"></i>
                                <div v-else class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                            </button>
                            <span class="text-xs text-gray-600 mt-2 font-medium">Leave</span>
                        </div>

                        <!-- End Call -->
                        <div class="flex flex-col items-center">
                            <button
                                @click="endCall"
                                :disabled="isEndingCall || isLeavingCall || isConnecting"
                                class="relative p-3 rounded-xl bg-red-500 hover:bg-red-600 text-white transition-all duration-200 shadow-md transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                                title="End call for everyone"
                            >
                                <i v-if="!isEndingCall" class="fas fa-phone-slash text-lg"></i>
                                <div v-else class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                            </button>
                            <span class="text-xs text-gray-600 mt-2 font-medium">End</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped>
/* Enhanced animations */
@keyframes fade-in {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fade-in {
    animation: fade-in 0.3s ease-out;
}

/* Smooth transitions for video elements */
video {
    transition: opacity 0.3s ease-in-out;
}

/* Enhanced button hover effects */
button:hover:not(:disabled) {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    transform: translateY(-1px);
}

/* Loading spinner improvements */
.animate-spin {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Enhanced backdrop blur */
.backdrop-blur-sm {
    backdrop-filter: blur(8px);
}

/* Responsive design improvements */
@media (max-width: 640px) {
    .max-w-5xl {
        max-width: 100%;
        margin: 0;
        border-radius: 0;
        height: 100vh;
        max-height: 100vh;
    }

    .w-44 {
        width: 8rem;
    }

    .h-32 {
        height: 6rem;
    }
}

/* Enhanced pulse animation for waiting states */
@keyframes pulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.05);
    }
}

.animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Smooth modal transitions with scale */
.fixed.inset-0 {
    animation: modal-fade-in 0.3s ease-out;
}

@keyframes modal-fade-in {
    from {
        opacity: 0;
        transform: scale(0.95);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Enhanced connection status indicator */
.connection-indicator {
    position: relative;
    transition: all 0.3s ease;
}

.connection-indicator::before {
    content: '';
    position: absolute;
    top: -3px;
    right: -3px;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.connection-indicator.connected::before {
    background-color: #10b981; /* green */
    box-shadow: 0 0 8px rgba(16, 185, 129, 0.4);
}

.connection-indicator.connecting::before {
    background-color: #f59e0b; /* yellow */
    animation: pulse-glow 1.5s infinite;
}

.connection-indicator.waiting::before {
    background-color: #3b82f6; /* blue */
    animation: pulse-glow 1.5s infinite;
}

.connection-indicator.disconnected::before {
    background-color: #ef4444; /* red */
}

@keyframes pulse-glow {
    0%, 100% {
        opacity: 1;
        box-shadow: 0 0 8px rgba(59, 130, 246, 0.4);
    }
    50% {
        opacity: 0.7;
        box-shadow: 0 0 16px rgba(59, 130, 246, 0.6);
    }
}

/* Scrollbar styling for better UX */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.5);
}
</style>
