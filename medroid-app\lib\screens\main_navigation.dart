import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'package:medroid_app/screens/ai_chat_screen.dart';
import 'package:medroid_app/screens/feed_screen.dart';
import 'package:medroid_app/screens/chat_history_screen.dart';
import 'package:medroid_app/screens/provider_marketplace.dart';
import 'package:medroid_app/screens/profile_screen_new.dart';
import 'package:medroid_app/services/api_service.dart';
import 'package:medroid_app/services/auth_service.dart';
import 'package:medroid_app/widgets/custom_bottom_navigation.dart';
import 'package:medroid_app/widgets/provider_selection_dialog.dart';
import 'package:medroid_app/providers/theme_provider.dart';
import 'package:medroid_app/utils/responsive_utils.dart';
import 'package:medroid_app/utils/app_colors.dart';
import 'package:medroid_app/widgets/responsive_container.dart';
import 'package:shared_preferences/shared_preferences.dart';

class MainNavigation extends StatefulWidget {
  final int? initialTabIndex;
  final String? initialConversationId;

  const MainNavigation(
      {Key? key, this.initialTabIndex, this.initialConversationId})
      : super(key: key);

  @override
  MainNavigationState createState() => MainNavigationState();
}

class MainNavigationState extends State<MainNavigation> {
  int _currentIndex = 0;
  String? _conversationId;
  List<dynamic> _chatHistory = [];
  bool _isLoadingChatHistory = false;

  // We'll initialize chat history in the existing initState method

  // Method to load chat history
  Future<void> _loadChatHistory() async {
    if (_isLoadingChatHistory) return;

    setState(() {
      _isLoadingChatHistory = true;
    });

    try {
      final apiService = RepositoryProvider.of<ApiService>(context);
      final conversations = await apiService.getChatHistory(perPage: 20);

      if (mounted) {
        setState(() {
          _chatHistory = conversations;
          _isLoadingChatHistory = false;
        });
      }
    } catch (e) {
      debugPrint('Error loading chat history: $e');
      if (mounted) {
        setState(() {
          _isLoadingChatHistory = false;
        });
      }
    }
  }

  // Method to open a conversation
  Future<void> _openConversation(String conversationId) async {
    // Get API service
    final apiService = RepositoryProvider.of<ApiService>(context);

    try {
      // Show loading indicator
      setState(() {
        _isLoadingChatHistory = true;
      });

      // Fetch the conversation details to ensure it exists
      await apiService.getConversation(conversationId);

      if (!mounted) return;

      // Update the state to show the conversation
      setState(() {
        _currentIndex = 0; // Switch to chat tab
        _conversationId = conversationId;
        _isLoadingChatHistory = false;
      });
    } catch (e) {
      debugPrint('Error opening conversation: $e');
      if (mounted) {
        setState(() {
          _isLoadingChatHistory = false;
        });

        // Show error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error opening conversation: $e'),
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  // Method to delete a conversation
  Future<void> _deleteConversation(dynamic conversation) async {
    // Get API service and conversation ID before showing dialog
    final apiService = RepositoryProvider.of<ApiService>(context);
    final conversationId = conversation['id'].toString();

    // Show confirmation dialog
    final bool? confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Conversation'),
        content: const Text(
            'Are you sure you want to delete this conversation? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirm != true || !mounted) return;

    try {
      // Delete the conversation
      final success = await apiService.deleteConversation(conversationId);

      if (!mounted) return;

      if (success) {
        // Refresh the chat history
        _loadChatHistory();

        // If the deleted conversation was the current one, clear it
        if (_conversationId == conversationId) {
          setState(() {
            _conversationId = null;
          });
        }

        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Conversation deleted successfully'),
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error deleting conversation: $e'),
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  // Method to share a conversation to feed
  Future<void> _shareConversation(dynamic conversation) async {
    // Get API service and conversation ID before showing dialog
    final apiService = RepositoryProvider.of<ApiService>(context);
    final conversationId = conversation['id'].toString();

    // Show confirmation dialog
    final bool? confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Share Conversation'),
        content: const Text(
            'This will share your conversation to the public feed where other users can see it. Continue?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Share'),
          ),
        ],
      ),
    );

    if (confirm != true || !mounted) return;

    try {
      // Share the conversation
      final success = await apiService.shareConversationToFeed(conversationId);

      if (!mounted) return;

      // Show success or failure message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(success
              ? 'Conversation shared to feed successfully'
              : 'Failed to share conversation'),
          duration: const Duration(seconds: 2),
        ),
      );

      // Refresh the chat history to update the shared status
      _loadChatHistory();
    } catch (e) {
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error sharing conversation: $e'),
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  // Method to edit a conversation title
  Future<void> _editConversationTitle(dynamic conversation) async {
    // Get API service and conversation ID before showing dialog
    final apiService = RepositoryProvider.of<ApiService>(context);
    final conversationId = conversation['id'].toString();

    final TextEditingController titleController = TextEditingController(
      text: conversation['title'] ?? 'Health Conversation',
    );

    // Show edit dialog
    final String? newTitle = await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Edit Conversation Title'),
        content: TextField(
          controller: titleController,
          decoration: const InputDecoration(
            labelText: 'Title',
            hintText: 'Enter a new title for this conversation',
          ),
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, titleController.text),
            child: const Text('Save'),
          ),
        ],
      ),
    );

    // If user cancelled or entered empty title, do nothing
    if (newTitle == null || newTitle.trim().isEmpty || !mounted) return;

    try {
      // Update the conversation title
      final success = await apiService.updateConversationTitle(
        conversationId,
        newTitle.trim(),
      );

      if (!mounted) return;

      if (success) {
        // Refresh the chat history
        _loadChatHistory();

        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Conversation title updated successfully'),
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error updating conversation title: $e'),
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  // Method to handle logout
  void _handleLogout() {
    // Show confirmation dialog
    showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Logout'),
        content: const Text('Are you sure you want to logout?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Logout'),
          ),
        ],
      ),
    ).then((shouldLogout) {
      if (shouldLogout == true) {
        _performLogout();
      }
    });
  }

  // Perform the actual logout
  Future<void> _performLogout() async {
    // Get auth service
    final authService = RepositoryProvider.of<AuthService>(context);

    // Perform logout
    await authService.logout();

    // Navigate to home screen if still mounted
    if (mounted) {
      Navigator.of(context).pushNamedAndRemoveUntil('/', (route) => false);
    }
  }

  // Define navigation items - match the ones in anonymous chat for consistency
  final List<CustomNavItem> _navItems = [
    CustomNavItem(
      icon: Icons.chat_bubble_outline_rounded,
      activeIcon: Icons.chat_bubble_rounded,
      label: 'Chat',
    ),
    CustomNavItem(
      icon: Icons.explore_outlined,
      activeIcon: Icons.explore,
      label: 'Discover',
    ),
    CustomNavItem(
      icon: Icons.history_rounded,
      activeIcon: Icons.history_rounded,
      label: 'History',
    ),
    CustomNavItem(
      icon: Icons.shopping_bag_outlined,
      activeIcon: Icons.shopping_bag,
      label: 'Shop',
    ),
    CustomNavItem(
      icon: Icons.person_outline_rounded,
      activeIcon: Icons.person_rounded,
      label: 'Profile',
    ),
  ];

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // Check if we have arguments from navigation
    final args = ModalRoute.of(context)?.settings.arguments;
    if (args != null && args is Map<String, dynamic>) {
      // Set the current tab index if provided
      if (args.containsKey('tabIndex')) {
        setState(() {
          _currentIndex = args['tabIndex'];
        });
      }

      // Store the conversation ID if provided
      if (args.containsKey('conversationId')) {
        _conversationId = args['conversationId'];
      }
    }
  }

  // Store the post-login conversation ID
  String? _postLoginConversationId;

  @override
  void initState() {
    super.initState();

    // Set initial values from widget parameters if provided
    if (widget.initialTabIndex != null) {
      _currentIndex = widget.initialTabIndex!;
    }

    if (widget.initialConversationId != null) {
      _conversationId = widget.initialConversationId;
    }

    // Load the post-login conversation ID
    _loadPostLoginConversationId();

    // Load chat history for desktop/tablet sidebar
    _loadChatHistory();
    // Check for pending appointment booking after login
    _checkForPendingAppointment();
    // Check if we're coming from anonymous chat
    _checkForAnonymousChatTransfer();
  }

  // Load the conversation ID from SharedPreferences
  Future<void> _loadPostLoginConversationId() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final conversationId = prefs.getString('post_login_conversation_id');
      final redirect = prefs.getString('post_login_redirect');

      if (conversationId != null && redirect == 'chat' && mounted) {
        debugPrint('Found post-login conversation ID: $conversationId');

        setState(() {
          _postLoginConversationId = conversationId;
        });

        // Clear the stored ID after retrieving it to avoid reusing it
        await prefs.remove('post_login_conversation_id');
        await prefs.remove('post_login_redirect');

        // Show a snackbar to inform the user
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Continuing your previous conversation...'),
                duration: Duration(seconds: 3),
              ),
            );
          }
        });
      }
    } catch (e) {
      debugPrint('Error loading post-login conversation ID: $e');
    }
  }

  // Get the post-login conversation ID
  String? _getPostLoginConversationId() {
    return _postLoginConversationId;
  }

  // Check if there's a pending appointment booking that needs to be continued
  Future<void> _checkForPendingAppointment() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final continueBooking =
          prefs.getBool('continue_appointment_booking') ?? false;
      final triggerBookingAfterLogin =
          prefs.getBool('trigger_appointment_booking_after_login') ?? false;
      final conversationId = prefs.getString('post_login_conversation_id');
      final pendingAppointmentJson = prefs.getString('pending_appointment');
      final hasPendingSlots =
          prefs.getBool('has_pending_appointment_slots') ?? false;
      final availableSlotsJson = prefs.getString('available_appointment_slots');

      // Get transferred demographic information if available
      final transferGender = prefs.getString('transfer_gender');
      final transferAge = prefs.getString('transfer_age');

      // Check if we need to continue an appointment booking
      if ((continueBooking || triggerBookingAfterLogin) &&
          conversationId != null) {
        debugPrint('Found pending appointment booking to continue');

        // Clear the flags to avoid processing again
        await prefs.setBool('continue_appointment_booking', false);
        await prefs.setBool('trigger_appointment_booking_after_login', false);
        await prefs.remove('transfer_gender');
        await prefs.remove('transfer_age');

        // Get appointment details if available
        Map<String, dynamic>? appointmentDetails;
        if (pendingAppointmentJson != null) {
          try {
            appointmentDetails =
                json.decode(pendingAppointmentJson) as Map<String, dynamic>;
          } catch (e) {
            debugPrint('Error parsing pending appointment: $e');
          }
        }

        // Check if we have specific appointment details or just general booking intent
        bool hasSpecificAppointment = appointmentDetails != null &&
            appointmentDetails.containsKey('provider_id') &&
            appointmentDetails.containsKey('provider_name') &&
            appointmentDetails.containsKey('date') &&
            appointmentDetails.containsKey('time');

        if (mounted) {
          final apiService = RepositoryProvider.of<ApiService>(context);

          // First, send demographic information if available
          if (transferGender != null || transferAge != null) {
            String demographicMessage = 'My demographic information: ';
            if (transferGender != null) {
              demographicMessage += 'Gender: $transferGender';
            }
            if (transferAge != null) {
              demographicMessage += transferGender != null
                  ? ', Age group: $transferAge'
                  : 'Age group: $transferAge';
            }

            await apiService.sendChatMessage(
              conversationId,
              demographicMessage,
            );
          }

          if (hasSpecificAppointment) {
            // We have specific appointment details, send a message to continue booking
            await apiService.sendChatMessage(
              conversationId,
              'I would like to confirm my appointment with Dr. ${appointmentDetails['provider_name']} '
              'on ${appointmentDetails['date']} at ${appointmentDetails['time']}.',
            );

            // Show a snackbar to inform the user
            // Use a post-frame callback to ensure we're in a valid context
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content:
                        Text('Continuing with your appointment booking...'),
                    duration: Duration(seconds: 3),
                  ),
                );
              }
            });
          } else {
            // We only have general booking intent, show provider selection dialog
            await apiService.sendChatMessage(
              conversationId,
              'Now that you\'re signed in, let\'s find an available appointment for you. '
              'I\'ll show you a list of providers you can choose from.',
            );

            // Show provider selection dialog
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (mounted) {
                _showProviderSelectionDialog(conversationId);
              }
            });
          }
        }
      } else if (conversationId != null &&
          (transferGender != null || transferAge != null)) {
        // If we have demographic information but no appointment booking, just send the demographic information
        if (mounted) {
          final apiService = RepositoryProvider.of<ApiService>(context);

          String demographicMessage = 'My demographic information: ';
          if (transferGender != null) {
            demographicMessage += 'Gender: $transferGender';
          }
          if (transferAge != null) {
            demographicMessage += transferGender != null
                ? ', Age group: $transferAge'
                : 'Age group: $transferAge';
          }

          await apiService.sendChatMessage(
            conversationId,
            demographicMessage,
          );

          // Clear the demographic information
          await prefs.remove('transfer_gender');
          await prefs.remove('transfer_age');
        }
      }

      // Check if we have pending appointment slots from anonymous chat
      if (hasPendingSlots &&
          availableSlotsJson != null &&
          conversationId != null) {
        debugPrint('Found pending appointment slots from anonymous chat');

        try {
          final availableSlots = json.decode(availableSlotsJson) as List;

          // Clear the pending slots to avoid processing again
          await prefs.setBool('has_pending_appointment_slots', false);
          await prefs.remove('available_appointment_slots');

          // Show appointment slots dialog
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted) {
              _showAppointmentSlotsFromAnonymousChat(
                  availableSlots, conversationId);
            }
          });
        } catch (e) {
          debugPrint('Error parsing available appointment slots: $e');
        }
      }

      // No need to update state here
    } catch (e) {
      debugPrint('Error checking for pending appointment: $e');
    }
  }

  // Check if we're coming from anonymous chat and handle the transfer
  Future<void> _checkForAnonymousChatTransfer() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Check multiple keys for redundancy
      final comingFromAnonymousChat =
          prefs.getBool('coming_from_anonymous_chat') ?? false;
      final transferComplete =
          prefs.getBool('anonymous_chat_transfer_complete') ?? false;
      final hasTransferTimestamp =
          prefs.getString('transfer_timestamp') != null;

      if (comingFromAnonymousChat || transferComplete || hasTransferTimestamp) {
        debugPrint(
            'CHAT TRANSFER: Coming from anonymous chat, checking for transferred data');

        // Try multiple keys for the conversation ID for redundancy
        String? conversationId =
            prefs.getString('post_login_conversation_id') ??
                prefs.getString('transferred_conversation_id') ??
                prefs.getString('active_conversation_id') ??
                prefs.getString('initial_conversation_id');

        final transferredDataJson = prefs.getString('transferred_messages');

        if (conversationId != null && mounted) {
          debugPrint(
              'CHAT TRANSFER: Found transferred conversation ID: $conversationId');

          // Check if we have transferred messages
          if (transferredDataJson != null) {
            debugPrint('CHAT TRANSFER: Found transferred messages');
            try {
              final messages = json.decode(transferredDataJson);
              debugPrint('CHAT TRANSFER: Decoded ${messages.length} messages');
            } catch (e) {
              debugPrint('CHAT TRANSFER: Error decoding messages: $e');
            }
          }

          // Set the conversation ID to ensure it's loaded in the chat screen
          setState(() {
            _postLoginConversationId = conversationId;
            _currentIndex = 0; // Switch to chat tab
          });

          // Show a snackbar to inform the user
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text(
                      'Your anonymous chat has been transferred to your account'),
                  duration: Duration(seconds: 3),
                ),
              );
            }
          });

          // Clear all transfer flags to avoid processing again, but keep the conversation ID
          await prefs.setBool('coming_from_anonymous_chat', false);
          await prefs.setBool('anonymous_chat_transfer_complete', false);
          await prefs.remove('transfer_timestamp');

          // Don't remove the conversation IDs yet - they'll be used by the AIChatScreen
          // We'll let the AIChatScreen handle clearing them after it's loaded the conversation
        } else {
          debugPrint(
              'CHAT TRANSFER: No valid conversation ID found for transfer');
        }
      }
    } catch (e) {
      debugPrint('CHAT TRANSFER ERROR: $e');
    }
  }

  // Show dialog to select a provider and book an appointment
  void _showProviderSelectionDialog(String conversationId) {
    showDialog(
      context: context,
      builder: (context) => ProviderSelectionDialog(
        onClose: () {
          Navigator.of(context).pop();
        },
        onAppointmentSelected: (provider, slot) async {
          Navigator.of(context).pop();

          // Handle appointment selection
          final providerName = provider['name'] ?? 'Unknown';
          final startTime = DateTime.parse(slot['start_time']);
          final formattedDate =
              '${startTime.year}-${startTime.month.toString().padLeft(2, '0')}-${startTime.day.toString().padLeft(2, '0')}';
          final formattedTime =
              '${startTime.hour.toString().padLeft(2, '0')}:${startTime.minute.toString().padLeft(2, '0')}';

          // Send a message to the chat about the selected appointment
          if (mounted) {
            final apiService = RepositoryProvider.of<ApiService>(context);
            await apiService.sendChatMessage(
              conversationId,
              'I would like to book an appointment with Dr. $providerName on $formattedDate at $formattedTime.',
            );

            // Show a snackbar to inform the user
            // Use a post-frame callback to ensure we're in a valid context
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Processing your appointment booking...'),
                    duration: Duration(seconds: 3),
                  ),
                );
              }
            });
          }
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // Check if we're on a desktop-sized screen
    final isDesktop = ResponsiveUtils.isDesktop(context);
    final isTablet = ResponsiveUtils.isTablet(context);

    // For desktop and tablet layouts, we'll use a different approach
    if (isDesktop || isTablet) {
      return Scaffold(
        backgroundColor:
            AppColors.backgroundLight, // Consistent background color
        body: Row(
          children: [
            // Side navigation for desktop/tablet
            Container(
              width: isDesktop ? 240 : 80,
              decoration: BoxDecoration(
                color: Colors.white, // White background for sidebar
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(13), // 0.05 * 255 ≈ 13
                    blurRadius: 10,
                    offset: const Offset(0, 0),
                  ),
                ],
              ),
              child: Column(
                children: [
                  // App logo at the top
                  Container(
                    height: 80,
                    alignment: Alignment.center,
                    padding: const EdgeInsets.all(16),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Image.asset(
                          'assets/images/medroid_icon.png',
                          height: 32,
                          width: 32,
                        ),
                        if (isDesktop) ...[
                          const SizedBox(width: 8),
                          const Text(
                            'Medroid',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                  // Navigation items (using Flex layout to keep profile/logout at bottom)
                  Expanded(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        // Top section (navigation items and chat history)
                        Expanded(
                          child: Column(
                            children: [
                              // Main navigation items (excluding History tab for desktop/tablet)
                              Column(
                                children: [
                                  // Main navigation items
                                  ...List.generate(_navItems.length, (index) {
                                    // Skip the History tab (index 2) and Profile tab (index 4) for desktop/tablet
                                    if (index == 2 || index == 4) {
                                      return const SizedBox.shrink();
                                    }

                                    final item = _navItems[index];
                                    final isSelected = _currentIndex == index;

                                    return InkWell(
                                      onTap: () {
                                        setState(() {
                                          _currentIndex = index;
                                        });
                                      },
                                      child: Container(
                                        height: 40, // Even more compact height
                                        padding: EdgeInsets.symmetric(
                                          horizontal: isDesktop ? 14 : 6,
                                          vertical:
                                              4, // Smaller vertical padding
                                        ),
                                        margin:
                                            const EdgeInsets.only(bottom: 2),
                                        decoration: BoxDecoration(
                                          color: isSelected
                                              ? const Color.fromRGBO(
                                                  139, 233, 200, 0.2)
                                              : Colors.transparent,
                                          border: Border(
                                            left: BorderSide(
                                              color: isSelected
                                                  ? const Color(0xFF17C3B2)
                                                  : Colors.transparent,
                                              width: 4,
                                            ),
                                          ),
                                        ),
                                        child: Row(
                                          mainAxisAlignment: isDesktop
                                              ? MainAxisAlignment.start
                                              : MainAxisAlignment.center,
                                          children: [
                                            Icon(
                                              isSelected
                                                  ? item.activeIcon
                                                  : item.icon,
                                              color: isSelected
                                                  ? const Color(0xFF17C3B2)
                                                  : const Color(
                                                      0xFF666666), // Darker grey
                                              size:
                                                  20, // Even smaller icon size
                                            ),
                                            if (isDesktop) ...[
                                              const SizedBox(
                                                  width: 8), // Smaller spacing
                                              Text(
                                                item.label,
                                                style: TextStyle(
                                                  fontSize:
                                                      13, // Even smaller font size
                                                  color: isSelected
                                                      ? const Color(0xFF17C3B2)
                                                      : const Color(
                                                          0xFF666666), // Darker grey
                                                  fontWeight: isSelected
                                                      ? FontWeight.bold
                                                      : FontWeight.normal,
                                                ),
                                              ),
                                            ],
                                          ],
                                        ),
                                      ),
                                    );
                                  }),

                                  // Appointments menu item (moved to top section)
                                  if (isDesktop || isTablet)
                                    InkWell(
                                      onTap: () {
                                        // Navigate to appointments screen
                                        Navigator.of(context)
                                            .pushNamed('/appointments');
                                      },
                                      child: Container(
                                        height:
                                            40, // Same height as other menu items
                                        padding: EdgeInsets.symmetric(
                                          horizontal: isDesktop ? 14 : 6,
                                          vertical:
                                              4, // Same padding as other menu items
                                        ),
                                        margin:
                                            const EdgeInsets.only(bottom: 2),
                                        decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(8),
                                        ),
                                        child: Row(
                                          mainAxisAlignment: isDesktop
                                              ? MainAxisAlignment.start
                                              : MainAxisAlignment.center,
                                          children: [
                                            const Icon(
                                              Icons.calendar_today,
                                              color: Color(
                                                  0xFF666666), // Darker grey
                                              size:
                                                  20, // Even smaller icon size
                                            ),
                                            if (isDesktop) ...[
                                              const SizedBox(
                                                  width: 8), // Smaller spacing
                                              const Text(
                                                'Appointments',
                                                style: TextStyle(
                                                  fontSize:
                                                      13, // Even smaller font size
                                                  color: Color(
                                                      0xFF666666), // Darker grey
                                                  fontWeight: FontWeight.normal,
                                                ),
                                              ),
                                            ],
                                          ],
                                        ),
                                      ),
                                    ),
                                ],
                              ),

                              // Chat history section for desktop and tablet
                              if (isDesktop || isTablet) ...[
                                // Chat history header
                                Padding(
                                  padding:
                                      const EdgeInsets.fromLTRB(14, 10, 14, 4),
                                  child: Row(
                                    children: [
                                      // Use Expanded to prevent overflow
                                      const Expanded(
                                        child: Text(
                                          'Chat History',
                                          style: TextStyle(
                                            fontSize: 13,
                                            fontWeight: FontWeight.bold,
                                            color: Color(
                                                0xFF666666), // Darker grey
                                          ),
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                      ),
                                      // Use a smaller, more compact refresh button
                                      GestureDetector(
                                        onTap: _loadChatHistory,
                                        child: const Padding(
                                          padding: EdgeInsets.all(2),
                                          child: Icon(
                                            Icons.refresh_outlined,
                                            size: 16,
                                            color: Color(
                                                0xFF666666), // Darker grey
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),

                                // Chat history list
                                Expanded(
                                  child: _isLoadingChatHistory
                                      ? const Center(
                                          child: SizedBox(
                                            width: 20,
                                            height: 20,
                                            child: CircularProgressIndicator(
                                              strokeWidth: 2,
                                            ),
                                          ),
                                        )
                                      : _chatHistory.isEmpty
                                          ? const Center(
                                              child: Text(
                                                'No chat history',
                                                style: TextStyle(
                                                  fontSize: 13,
                                                  color: Color(
                                                      0xFF666666), // Darker grey
                                                ),
                                              ),
                                            )
                                          : ListView.builder(
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                      horizontal: 14),
                                              itemCount: _chatHistory.length,
                                              itemBuilder: (context, index) {
                                                final conversation =
                                                    _chatHistory[index];
                                                final date = conversation[
                                                            'updated_at'] !=
                                                        null
                                                    ? DateTime.parse(
                                                        conversation[
                                                            'updated_at'])
                                                    : DateTime.now();

                                                // Format date (shorter format for sidebar)
                                                final formattedDate =
                                                    DateFormat('MMM d')
                                                        .format(date);

                                                // Get the conversation title
                                                String title =
                                                    conversation['title'] ??
                                                        'Health Conversation';

                                                // Track hover state
                                                return StatefulBuilder(builder:
                                                    (context, setState) {
                                                  bool isHovering = false;

                                                  return MouseRegion(
                                                    onEnter: (_) => setState(
                                                        () =>
                                                            isHovering = true),
                                                    onExit: (_) => setState(
                                                        () =>
                                                            isHovering = false),
                                                    child: InkWell(
                                                      onTap: () =>
                                                          _openConversation(
                                                              conversation['id']
                                                                  .toString()),
                                                      child: Container(
                                                        padding:
                                                            const EdgeInsets
                                                                .symmetric(
                                                                vertical: 6,
                                                                horizontal: 10),
                                                        margin: const EdgeInsets
                                                            .only(bottom: 2),
                                                        decoration:
                                                            BoxDecoration(
                                                          borderRadius:
                                                              BorderRadius
                                                                  .circular(8),
                                                          color: _conversationId ==
                                                                  conversation[
                                                                          'id']
                                                                      .toString()
                                                              ? const Color
                                                                  .fromRGBO(139,
                                                                  233, 200, 0.1)
                                                              : isHovering
                                                                  ? Colors.grey
                                                                      .withAlpha(
                                                                          13) // 0.05 * 255 ≈ 13
                                                                  : Colors
                                                                      .transparent,
                                                        ),
                                                        child: Row(
                                                          children: [
                                                            // Chat info
                                                            Expanded(
                                                              child: Column(
                                                                crossAxisAlignment:
                                                                    CrossAxisAlignment
                                                                        .start,
                                                                children: [
                                                                  Text(
                                                                    title,
                                                                    style:
                                                                        TextStyle(
                                                                      fontSize:
                                                                          13,
                                                                      fontWeight: _conversationId ==
                                                                              conversation['id']
                                                                                  .toString()
                                                                          ? FontWeight
                                                                              .bold
                                                                          : FontWeight
                                                                              .normal,
                                                                      color: _conversationId ==
                                                                              conversation['id']
                                                                                  .toString()
                                                                          ? const Color(
                                                                              0xFF17C3B2)
                                                                          : Colors
                                                                              .black87,
                                                                    ),
                                                                    maxLines: 1,
                                                                    overflow:
                                                                        TextOverflow
                                                                            .ellipsis,
                                                                  ),
                                                                  const SizedBox(
                                                                      height:
                                                                          1),
                                                                  Text(
                                                                    formattedDate,
                                                                    style:
                                                                        const TextStyle(
                                                                      fontSize:
                                                                          10,
                                                                      color: Color(
                                                                          0xFF666666),
                                                                    ),
                                                                  ),
                                                                ],
                                                              ),
                                                            ),

                                                            // Three dots menu (only visible on hover)
                                                            if (isHovering)
                                                              PopupMenuButton<
                                                                  String>(
                                                                padding:
                                                                    EdgeInsets
                                                                        .zero,
                                                                icon: Icon(
                                                                  Icons
                                                                      .more_vert,
                                                                  size: 18,
                                                                  color: Colors
                                                                          .grey[
                                                                      600],
                                                                ),
                                                                onSelected:
                                                                    (value) {
                                                                  if (value ==
                                                                      'delete') {
                                                                    _deleteConversation(
                                                                        conversation);
                                                                  } else if (value ==
                                                                      'share') {
                                                                    _shareConversation(
                                                                        conversation);
                                                                  } else if (value ==
                                                                      'edit') {
                                                                    _editConversationTitle(
                                                                        conversation);
                                                                  }
                                                                },
                                                                itemBuilder:
                                                                    (context) =>
                                                                        [
                                                                  const PopupMenuItem(
                                                                    value:
                                                                        'edit',
                                                                    child: Row(
                                                                      children: [
                                                                        Icon(
                                                                            Icons
                                                                                .edit,
                                                                            size:
                                                                                18),
                                                                        SizedBox(
                                                                            width:
                                                                                8),
                                                                        Text(
                                                                            'Edit Title'),
                                                                      ],
                                                                    ),
                                                                  ),
                                                                  const PopupMenuItem(
                                                                    value:
                                                                        'share',
                                                                    child: Row(
                                                                      children: [
                                                                        Icon(
                                                                            Icons
                                                                                .share,
                                                                            size:
                                                                                18),
                                                                        SizedBox(
                                                                            width:
                                                                                8),
                                                                        Text(
                                                                            'Share'),
                                                                      ],
                                                                    ),
                                                                  ),
                                                                  const PopupMenuItem(
                                                                    value:
                                                                        'delete',
                                                                    child: Row(
                                                                      children: [
                                                                        Icon(
                                                                            Icons
                                                                                .delete,
                                                                            size:
                                                                                18,
                                                                            color:
                                                                                Colors.red),
                                                                        SizedBox(
                                                                            width:
                                                                                8),
                                                                        Text(
                                                                            'Delete',
                                                                            style:
                                                                                TextStyle(color: Colors.red)),
                                                                      ],
                                                                    ),
                                                                  ),
                                                                ],
                                                              ),
                                                          ],
                                                        ),
                                                      ),
                                                    ),
                                                  );
                                                });
                                              },
                                            ),
                                ),

                                // View all link (always visible)
                                Align(
                                  alignment: Alignment.centerLeft,
                                  child: Padding(
                                    padding:
                                        const EdgeInsets.fromLTRB(14, 4, 14, 8),
                                    child: GestureDetector(
                                      onTap: () {
                                        Navigator.pushNamed(
                                            context, '/chat-history');
                                      },
                                      child: const Text(
                                        'View all chats',
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: Color(0xFF17C3B2),
                                          fontWeight: FontWeight.w400,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ],
                          ),
                        ),

                        // Additional menu items for desktop and tablet
                        if (isDesktop || isTablet) ...[
                          // Divider
                          Container(
                            height: 1,
                            margin: const EdgeInsets.symmetric(vertical: 6),
                            color: Colors.grey.withAlpha(51), // 0.2 * 255 ≈ 51
                          ),

                          // Appointments button (for desktop/tablet only)
                          InkWell(
                            onTap: () {
                              // Navigate to appointments screen using named route
                              Navigator.pushNamed(context, '/appointments');
                            },
                            child: Container(
                              height: 40,
                              padding: const EdgeInsets.symmetric(
                                horizontal: 14,
                                vertical: 6,
                              ),
                              child: Row(
                                children: [
                                  const Icon(
                                    Icons.calendar_today,
                                    color:
                                        AppColors.tealSurge, // Use theme color
                                    size: 20,
                                  ),
                                  if (isDesktop) ...[
                                    const SizedBox(width: 8),
                                    const Text(
                                      'Appointments',
                                      style: TextStyle(
                                        fontSize: 14,
                                        color: AppColors
                                            .tealSurge, // Use theme color
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ],
                                ],
                              ),
                            ),
                          ),

                          // Profile and Logout in one row (for desktop/tablet)
                          Container(
                            height: 40,
                            padding: const EdgeInsets.symmetric(
                              horizontal: 14,
                              vertical: 6,
                            ),
                            decoration: BoxDecoration(
                              border: Border(
                                top: BorderSide(
                                  color: Colors.grey
                                      .withAlpha(51), // 0.2 * 255 ≈ 51
                                  width: 1,
                                ),
                              ),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                // Profile button
                                InkWell(
                                  onTap: () {
                                    // Set current index to Profile tab
                                    setState(() {
                                      _currentIndex = 4; // Profile tab
                                    });
                                  },
                                  child: Row(
                                    children: [
                                      const Icon(
                                        Icons.person,
                                        color: Color(0xFF666666), // Darker grey
                                        size: 20,
                                      ),
                                      if (isDesktop) ...[
                                        const SizedBox(width: 8),
                                        const Text(
                                          'Profile',
                                          style: TextStyle(
                                            fontSize: 13,
                                            color: Color(
                                                0xFF666666), // Darker grey
                                            fontWeight: FontWeight.normal,
                                          ),
                                        ),
                                      ],
                                    ],
                                  ),
                                ),

                                // Logout button (icon only)
                                InkWell(
                                  onTap: _handleLogout,
                                  child: const Icon(
                                    Icons.logout,
                                    color: Color(0xFF666666), // Darker grey
                                    size: 20,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],

                        // Logout button for mobile (not desktop/tablet)
                        if (!(isDesktop || isTablet))
                          InkWell(
                            onTap: _handleLogout,
                            child: Container(
                              height: 50,
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 8,
                              ),
                              decoration: BoxDecoration(
                                border: Border(
                                  top: BorderSide(
                                    color: Colors.grey
                                        .withAlpha(51), // 0.2 * 255 ≈ 51
                                    width: 1,
                                  ),
                                ),
                              ),
                              child: const Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.logout,
                                    color: Color(0xFF666666), // Darker grey
                                    size: 20,
                                  ),
                                ],
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            // Main content area
            Expanded(
              child: ResponsiveCenteredContainer(
                padding: EdgeInsets.zero,
                constraints: null,
                child: IndexedStack(
                  index: _currentIndex,
                  children: [
                    AIChatScreen(
                      initialConversationId:
                          _conversationId ?? _getPostLoginConversationId(),
                    ),
                    const FeedScreen(),
                    ChatHistoryScreen(
                      onCreateNewChat: (BuildContext context,
                          {String? initialMessage, File? imageFile}) {
                        setState(() {
                          _currentIndex = 0; // Switch to chat tab
                        });
                      },
                    ),
                    ProviderMarketplaceScreen(
                        key: ValueKey('shop-tab-${_currentIndex == 3}')),
                    const ProfileScreenNew(),
                  ],
                ),
              ),
            ),
          ],
        ),
      );
    }

    // Mobile layout (original layout)
    return Scaffold(
      backgroundColor:
          AppColors.backgroundLight, // Consistent background color for all tabs
      // Remove app bar completely
      appBar: null,
      body: Stack(
        children: [
          // Main content
          IndexedStack(
            index: _currentIndex,
            children: [
              AIChatScreen(
                // Use the conversation ID from route arguments if available, otherwise use post-login ID
                initialConversationId:
                    _conversationId ?? _getPostLoginConversationId(),
              ),
              // Real screens for other tabs
              const FeedScreen(),
              ChatHistoryScreen(
                onCreateNewChat: (BuildContext context,
                    {String? initialMessage, File? imageFile}) {
                  setState(() {
                    _currentIndex = 0; // Switch to chat tab
                  });
                },
              ),
              ProviderMarketplaceScreen(
                  key: ValueKey('shop-tab-${_currentIndex == 3}')),
              const ProfileScreenNew(),
            ],
          ),
        ],
      ),
      bottomNavigationBar: CustomBottomNavigation(
        currentIndex: _currentIndex,
        onTap: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        items: _navItems,
        isAnonymousMode:
            false, // Explicitly set to false for authenticated users
      ),
    );
  }

  // Show appointment slots that were saved from anonymous chat
  void _showAppointmentSlotsFromAnonymousChat(
      List availableSlots, String conversationId) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Container(
          constraints: const BoxConstraints(maxWidth: 500, maxHeight: 600),
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'Available Appointment Slots',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              const Text(
                'Welcome back! Here are the appointment slots we found for you. Select one to continue with booking.',
                style: TextStyle(color: Colors.grey),
              ),
              const SizedBox(height: 16),
              Flexible(
                child: ListView.builder(
                  shrinkWrap: true,
                  itemCount: availableSlots.length,
                  itemBuilder: (context, index) {
                    final slot = availableSlots[index];
                    return Card(
                      margin: const EdgeInsets.only(bottom: 8),
                      child: ListTile(
                        leading: CircleAvatar(
                          backgroundImage: slot['provider_image'] != null
                              ? NetworkImage(slot['provider_image'])
                              : null,
                          child: slot['provider_image'] == null
                              ? const Icon(Icons.person)
                              : null,
                        ),
                        title:
                            Text(slot['provider_name'] ?? 'Unknown Provider'),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('${slot['date']} at ${slot['start_time']}'),
                            if (slot['service_name'] != null)
                              Text('Service: ${slot['service_name']}'),
                            if (slot['price'] != null)
                              Text('Price: \$${slot['price']}'),
                          ],
                        ),
                        trailing: const Icon(Icons.arrow_forward_ios),
                        onTap: () {
                          Navigator.of(context).pop();
                          _selectAppointmentSlotFromAnonymousChat(
                              slot, conversationId);
                        },
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Handle appointment slot selection from anonymous chat
  void _selectAppointmentSlotFromAnonymousChat(
      Map<String, dynamic> slot, String conversationId) async {
    try {
      final apiService = RepositoryProvider.of<ApiService>(context);

      // Send a message to the chat about the selected appointment
      final providerName = slot['provider_name'] ?? 'Unknown Provider';
      final date = slot['date'] ?? '';
      final startTime = slot['start_time'] ?? '';

      await apiService.sendChatMessage(
        conversationId,
        'I would like to book the appointment with Dr. $providerName on $date at $startTime.',
      );

      if (!mounted) return;

      // Show a snackbar to inform the user
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Processing your appointment booking...'),
          duration: Duration(seconds: 3),
        ),
      );

      // Navigate to chat tab to see the booking process
      setState(() {
        _currentIndex = 0; // Chat tab index
      });
    } catch (e) {
      debugPrint('Error selecting appointment slot: $e');

      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error processing appointment: $e'),
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }
}
