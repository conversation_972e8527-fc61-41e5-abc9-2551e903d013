<?php

/**
 * Email Template Test Bootstrap
 * 
 * This file sets up the testing environment specifically for email template tests
 */

// Ensure we're in testing environment
$_ENV['APP_ENV'] = 'testing';
$_ENV['DB_CONNECTION'] = 'sqlite';
$_ENV['DB_DATABASE'] = ':memory:';
$_ENV['MAIL_MAILER'] = 'array';
$_ENV['CACHE_DRIVER'] = 'array';
$_ENV['SESSION_DRIVER'] = 'array';
$_ENV['QUEUE_CONNECTION'] = 'sync';

// Load the application
require_once __DIR__ . '/../vendor/autoload.php';

// Create application instance
$app = require __DIR__ . '/../bootstrap/app.php';

// Bootstrap the application
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

// Set up testing database
if (!file_exists(database_path('database.sqlite'))) {
    touch(database_path('database.sqlite'));
}

echo "✅ Email Template Test Environment Initialized\n";
echo "   - Database: SQLite (in-memory)\n";
echo "   - Mail: Array driver (fake)\n";
echo "   - Cache: Array driver\n";
echo "   - Queue: Sync\n\n";
