<script setup lang="ts">
import { Head, useForm } from '@inertiajs/vue3';
import { ref, onMounted } from 'vue';
import Layout from '@/layouts/settings/Layout.vue';
import Heading from '@/components/Heading.vue';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import axios from 'axios';

const loading = ref(false);
const saving = ref(false);
const successMessage = ref('');
const errorMessage = ref('');

const form = useForm({
    preferred_location: '',
    preferred_gender: '',
    preferred_language: '',
    latitude: null,
    longitude: null,
    search_radius: '25',
});

const loadPreferences = async () => {
    loading.value = true;
    try {
        const response = await axios.get('/api/patient/appointment-preferences');
        const preferences = response.data.appointment_preferences;
        
        form.preferred_location = preferences.preferred_location || '';
        form.preferred_gender = preferences.preferred_gender || '';
        form.preferred_language = preferences.preferred_language || '';
        form.latitude = preferences.latitude || null;
        form.longitude = preferences.longitude || null;
        form.search_radius = preferences.search_radius || '25';
    } catch (error) {
        console.error('Error loading preferences:', error);
        errorMessage.value = 'Failed to load appointment preferences';
    } finally {
        loading.value = false;
    }
};

const savePreferences = async () => {
    saving.value = true;
    errorMessage.value = '';
    successMessage.value = '';
    
    try {
        await axios.put('/api/patient/appointment-preferences', form.data());
        successMessage.value = 'Appointment preferences updated successfully!';
    } catch (error) {
        console.error('Error saving preferences:', error);
        errorMessage.value = error.response?.data?.message || 'Failed to save preferences';
    } finally {
        saving.value = false;
    }
};

const getCurrentLocation = () => {
    if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
            (position) => {
                form.latitude = position.coords.latitude;
                form.longitude = position.coords.longitude;
                successMessage.value = 'Location updated successfully!';
            },
            (error) => {
                console.error('Error getting location:', error);
                errorMessage.value = 'Failed to get current location';
            }
        );
    } else {
        errorMessage.value = 'Geolocation is not supported by this browser';
    }
};

onMounted(() => {
    loadPreferences();
});
</script>

<template>
    <Head title="Appointment Preferences" />

    <Layout>
        <div class="space-y-6">
            <div>
                <Heading>Appointment Preferences</Heading>
                <p class="text-muted-foreground">
                    Set your preferences for booking appointments with healthcare providers.
                </p>
            </div>

            <Separator />

            <div v-if="loading" class="flex items-center justify-center py-8">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
            </div>

            <div v-else class="space-y-6">
                <!-- Success/Error Messages -->
                <div v-if="successMessage" class="p-4 bg-green-50 border border-green-200 rounded-lg">
                    <p class="text-green-800">{{ successMessage }}</p>
                </div>

                <div v-if="errorMessage" class="p-4 bg-red-50 border border-red-200 rounded-lg">
                    <p class="text-red-800">{{ errorMessage }}</p>
                </div>

                <form @submit.prevent="savePreferences" class="space-y-6">
                    <!-- Provider Preferences -->
                    <Card>
                        <CardHeader>
                            <CardTitle>Provider Preferences</CardTitle>
                            <CardDescription>
                                Choose your preferred healthcare provider characteristics.
                            </CardDescription>
                        </CardHeader>
                        <CardContent class="space-y-4">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div class="space-y-2">
                                    <Label for="preferred_gender">Preferred Provider Gender</Label>
                                    <Select v-model="form.preferred_gender">
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select gender preference" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="any">No Preference</SelectItem>
                                            <SelectItem value="male">Male</SelectItem>
                                            <SelectItem value="female">Female</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>

                                <div class="space-y-2">
                                    <Label for="preferred_language">Preferred Language</Label>
                                    <Select v-model="form.preferred_language">
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select language" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="english">English</SelectItem>
                                            <SelectItem value="spanish">Spanish</SelectItem>
                                            <SelectItem value="french">French</SelectItem>
                                            <SelectItem value="german">German</SelectItem>
                                            <SelectItem value="italian">Italian</SelectItem>
                                            <SelectItem value="portuguese">Portuguese</SelectItem>
                                            <SelectItem value="chinese">Chinese</SelectItem>
                                            <SelectItem value="arabic">Arabic</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <!-- Location Preferences -->
                    <Card>
                        <CardHeader>
                            <CardTitle>Location Preferences</CardTitle>
                            <CardDescription>
                                Set your preferred location and search radius for finding providers.
                            </CardDescription>
                        </CardHeader>
                        <CardContent class="space-y-4">
                            <div class="space-y-2">
                                <Label for="preferred_location">Preferred Location</Label>
                                <Input
                                    id="preferred_location"
                                    v-model="form.preferred_location"
                                    placeholder="Enter city, state, or address"
                                />
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div class="space-y-2">
                                    <Label for="search_radius">Search Radius (miles)</Label>
                                    <Select v-model="form.search_radius">
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select radius" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="5">5 miles</SelectItem>
                                            <SelectItem value="10">10 miles</SelectItem>
                                            <SelectItem value="25">25 miles</SelectItem>
                                            <SelectItem value="50">50 miles</SelectItem>
                                            <SelectItem value="100">100 miles</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>

                                <div class="space-y-2">
                                    <Label>Current Location</Label>
                                    <Button
                                        type="button"
                                        variant="outline"
                                        @click="getCurrentLocation"
                                        class="w-full"
                                    >
                                        <i class="fas fa-location-arrow mr-2"></i>
                                        Use Current Location
                                    </Button>
                                </div>
                            </div>

                            <div v-if="form.latitude && form.longitude" class="p-3 bg-blue-50 rounded-lg">
                                <p class="text-sm text-blue-800">
                                    <i class="fas fa-map-marker-alt mr-2"></i>
                                    Location set: {{ form.latitude.toFixed(4) }}, {{ form.longitude.toFixed(4) }}
                                </p>
                            </div>
                        </CardContent>
                    </Card>

                    <!-- Save Button -->
                    <div class="flex justify-end">
                        <Button
                            type="submit"
                            :disabled="saving"
                            class="min-w-[120px]"
                        >
                            <i v-if="saving" class="fas fa-spinner fa-spin mr-2"></i>
                            <i v-else class="fas fa-save mr-2"></i>
                            {{ saving ? 'Saving...' : 'Save Preferences' }}
                        </Button>
                    </div>
                </form>
            </div>
        </div>
    </Layout>
</template>
