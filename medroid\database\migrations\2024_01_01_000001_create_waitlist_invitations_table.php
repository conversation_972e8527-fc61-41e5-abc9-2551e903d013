<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('waitlist_invitations', function (Blueprint $table) {
            $table->id();
            $table->string('email');
            $table->string('token')->unique();
            $table->enum('club_type', ['founder', 'premium', 'regular'])->default('regular');
            $table->string('membership_level')->default('basic');
            $table->enum('status', ['pending', 'sent', 'used', 'expired'])->default('pending');
            $table->timestamp('expires_at');
            $table->timestamp('sent_at')->nullable();
            $table->timestamp('used_at')->nullable();
            $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('used_by')->nullable()->constrained('users')->onDelete('set null');
            $table->json('metadata')->nullable();
            $table->timestamps();

            $table->index(['email', 'status']);
            $table->index(['token', 'status']);
            $table->index('expires_at');
        });

        Schema::create('waitlist_requests', function (Blueprint $table) {
            $table->id();
            $table->string('email')->unique();
            $table->string('name')->nullable();
            $table->enum('status', ['pending', 'invited', 'registered'])->default('pending');
            $table->timestamp('invited_at')->nullable();
            $table->timestamp('registered_at')->nullable();
            $table->foreignId('invitation_id')->nullable()->constrained('waitlist_invitations')->onDelete('set null');
            $table->json('metadata')->nullable();
            $table->timestamps();

            $table->index(['email', 'status']);
            $table->index('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('waitlist_requests');
        Schema::dropIfExists('waitlist_invitations');
    }
};
