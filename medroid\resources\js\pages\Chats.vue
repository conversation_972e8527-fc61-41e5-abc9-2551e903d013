<script setup>
import AppLayout from '@/layouts/AppLayout.vue';
import ChatDetailModal from '@/components/ChatDetailModal.vue';
import { Head, Link } from '@inertiajs/vue3';
import { ref, onMounted, computed, watch } from 'vue';

const breadcrumbs = [
    { title: 'Dashboard', href: '/dashboard' },
    { title: 'Chats', href: '/chats' },
];

const loading = ref(false);
const chats = ref([]); // Always initialize as empty array
const stats = ref({});
const pagination = ref({
    current_page: 1,
    last_page: 1,
    per_page: 15,
    total: 0
});

// Filters
const filters = ref({
    search: '',
    type: '', // all, user_chat, ai_chat
    role: '',
    status: '',
    flagged: '',
    archived: '',
    start_date: '',
    end_date: '',
    sort_by: 'created_at',
    sort_dir: 'desc',
    per_page: 15,
    page: 1
});

// Chat detail modal
const showChatDetail = ref(false);
const selectedChatId = ref(null);

const fetchChats = async () => {
    loading.value = true;
    try {
        const params = new URLSearchParams();
        Object.keys(filters.value).forEach(key => {
            if (filters.value[key] !== '' && filters.value[key] !== null) {
                params.append(key, filters.value[key]);
            }
        });

        const response = await window.axios.get(`/chats-list?${params.toString()}`);

        // Ensure chats is always an array
        const responseData = response.data;
        if (Array.isArray(responseData)) {
            // Direct array response
            chats.value = responseData;
            pagination.value = {
                current_page: 1,
                last_page: 1,
                per_page: responseData.length,
                total: responseData.length
            };
        } else if (responseData && Array.isArray(responseData.data)) {
            // Paginated response
            chats.value = responseData.data;
            pagination.value = {
                current_page: responseData.current_page || 1,
                last_page: responseData.last_page || 1,
                per_page: responseData.per_page || 15,
                total: responseData.total || 0
            };
        } else {
            // Fallback - ensure chats is always an array
            console.warn('Unexpected response structure:', responseData);
            chats.value = [];
            pagination.value = {
                current_page: 1,
                last_page: 1,
                per_page: 15,
                total: 0
            };
        }
    } catch (error) {
        console.error('Error fetching chats:', error);
        // Ensure chats is always an array even on error
        chats.value = [];
        pagination.value = {
            current_page: 1,
            last_page: 1,
            per_page: 15,
            total: 0
        };
    } finally {
        loading.value = false;
    }
};

const fetchStats = async () => {
    try {
        const response = await window.axios.get('/chats-stats');
        stats.value = response.data;
    } catch (error) {
        console.error('Error fetching stats:', error);
    }
};

onMounted(() => {
    fetchChats();
    fetchStats();
});

// Watch for filter changes
watch(filters, () => {
    filters.value.page = 1; // Reset to first page when filters change
    fetchChats();
}, { deep: true });

const clearFilters = () => {
    filters.value = {
        search: '',
        type: '',
        role: '',
        status: '',
        flagged: '',
        archived: '',
        start_date: '',
        end_date: '',
        sort_by: 'created_at',
        sort_dir: 'desc',
        per_page: 15,
        page: 1
    };
};

const viewChatDetail = (chatId) => {
    selectedChatId.value = chatId;
    showChatDetail.value = true;
};

const onChatUpdated = () => {
    fetchChats();
    fetchStats();
};

const changePage = (page) => {
    filters.value.page = page;
};

const getStatusBadgeClass = (status) => {
    const classes = {
        'active': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
        'inactive': 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200',
        'ended': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
    };
    return classes[status] || 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
};

const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString();
};

const totalChats = computed(() => pagination.value.total || 0);
const activeChats = computed(() => {
    if (!Array.isArray(chats.value)) return 0;
    return chats.value.filter(c => c.status === 'active' || (c.type === 'ai_chat' && !c.escalated)).length;
});
const flaggedChats = computed(() => {
    if (!Array.isArray(chats.value)) return 0;
    return chats.value.filter(c => c.is_flagged || (c.type === 'ai_chat' && c.escalated)).length;
});
const archivedChats = computed(() => {
    if (!Array.isArray(chats.value)) return 0;
    return chats.value.filter(c => c.is_archived).length;
});
const aiChats = computed(() => {
    if (!Array.isArray(chats.value)) return 0;
    return chats.value.filter(c => c.type === 'ai_chat').length;
});
const userChats = computed(() => {
    if (!Array.isArray(chats.value)) return 0;
    return chats.value.filter(c => c.type === 'user_chat').length;
});
</script>

<template>
    <Head title="Chat Management" />

    <AppLayout>
        <template #header>
            <div class="flex items-center justify-between">
                <div>
                    <h2 class="text-xl font-semibold leading-tight text-gray-800 dark:text-gray-200">
                        Chat Management
                    </h2>
                    <nav class="flex mt-2" aria-label="Breadcrumb">
                        <ol class="inline-flex items-center space-x-1 md:space-x-3">
                            <li v-for="(breadcrumb, index) in breadcrumbs" :key="index" class="inline-flex items-center">
                                <Link v-if="index < breadcrumbs.length - 1"
                                    :href="breadcrumb.href"
                                    class="text-sm font-medium text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                                    {{ breadcrumb.title }}
                                </Link>
                                <span v-else class="text-sm font-medium text-gray-700 dark:text-gray-400">
                                    {{ breadcrumb.title }}
                                </span>
                                <svg v-if="index < breadcrumbs.length - 1" class="w-3 h-3 mx-1 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                </svg>
                            </li>
                        </ol>
                    </nav>
                </div>
            </div>
        </template>

        <div class="py-12">
            <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
                <!-- Stats Cards -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-comments text-2xl text-blue-500"></i>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Chats</p>
                                    <p class="text-2xl font-semibold text-gray-900 dark:text-gray-100">{{ totalChats }}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-circle text-2xl text-green-500"></i>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Active Chats</p>
                                    <p class="text-2xl font-semibold text-gray-900 dark:text-gray-100">{{ activeChats }}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-robot text-2xl text-purple-500"></i>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">AI Chats</p>
                                    <p class="text-2xl font-semibold text-gray-900 dark:text-gray-100">{{ aiChats }}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-flag text-2xl text-red-500"></i>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Flagged/Escalated</p>
                                    <p class="text-2xl font-semibold text-gray-900 dark:text-gray-100">{{ flaggedChats }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filters -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg mb-6">
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-4">
                            <!-- Search -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Search</label>
                                <input v-model="filters.search" type="text" placeholder="Search users, messages..."
                                    class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                            </div>

                            <!-- Chat Type Filter -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Chat Type</label>
                                <select v-model="filters.type"
                                    class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                    <option value="">All Types</option>
                                    <option value="user_chat">User Chats</option>
                                    <option value="ai_chat">AI Chats</option>
                                </select>
                            </div>

                            <!-- Role Filter -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Role</label>
                                <select v-model="filters.role"
                                    class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                    <option value="">All Roles</option>
                                    <option value="patient">Patient</option>
                                    <option value="provider">Provider</option>
                                </select>
                            </div>

                            <!-- Status Filter -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Status</label>
                                <select v-model="filters.status"
                                    class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                    <option value="">All Status</option>
                                    <option value="active">Active</option>
                                    <option value="inactive">Inactive</option>
                                    <option value="ended">Ended</option>
                                    <option value="escalated">Escalated</option>
                                </select>
                            </div>

                            <!-- Flagged Filter -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Flagged/Escalated</label>
                                <select v-model="filters.flagged"
                                    class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                    <option value="">All</option>
                                    <option value="true">Flagged/Escalated Only</option>
                                    <option value="false">Not Flagged/Escalated</option>
                                </select>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <!-- Date Range -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Start Date</label>
                                <input v-model="filters.start_date" type="date"
                                    class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">End Date</label>
                                <input v-model="filters.end_date" type="date"
                                    class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                            </div>

                            <!-- Actions -->
                            <div class="flex items-end">
                                <button @click="clearFilters"
                                    class="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                    <i class="fas fa-times mr-2"></i>
                                    Clear Filters
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Chats Table -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 text-gray-900 dark:text-gray-100">
                        <div v-if="loading" class="text-center py-8">
                            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
                        </div>

                        <div v-else class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                                <thead class="bg-gray-50 dark:bg-gray-700">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Type
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Participants
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Created
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Messages
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Status
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Flags
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Actions
                                        </th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                    <tr v-for="chat in (Array.isArray(chats) ? chats : [])" :key="chat.id" class="hover:bg-gray-50 dark:hover:bg-gray-700">
                                        <!-- Chat Type -->
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span v-if="chat.type === 'ai_chat'" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                                                <i class="fas fa-robot mr-1"></i>
                                                AI Chat
                                            </span>
                                            <span v-else class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                                <i class="fas fa-users mr-1"></i>
                                                User Chat
                                            </span>
                                        </td>

                                        <!-- Participants -->
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="flex-shrink-0 h-10 w-10">
                                                    <div class="h-10 w-10 rounded-full flex items-center justify-center"
                                                         :class="chat.type === 'ai_chat' ? 'bg-purple-100 dark:bg-purple-900' : 'bg-blue-100 dark:bg-blue-900'">
                                                        <i :class="chat.type === 'ai_chat' ? 'fas fa-robot text-purple-600 dark:text-purple-400' : 'fas fa-comment text-blue-600 dark:text-blue-400'"></i>
                                                    </div>
                                                </div>
                                                <div class="ml-4">
                                                    <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                                        {{ chat.patient?.user?.name || 'Unknown Patient' }}
                                                    </div>
                                                    <div class="text-sm text-gray-500 dark:text-gray-400" v-if="chat.type === 'user_chat'">
                                                        with {{ chat.provider?.user?.name || 'Unknown Provider' }}
                                                    </div>
                                                    <div class="text-sm text-gray-500 dark:text-gray-400" v-else>
                                                        with AI Assistant
                                                    </div>
                                                    <div class="text-xs text-gray-400 dark:text-gray-500">
                                                        {{ chat.patient?.user?.email || '' }}
                                                    </div>
                                                </div>
                                            </div>
                                        </td>

                                        <!-- Created -->
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900 dark:text-gray-100">
                                                {{ formatDate(chat.created_at) }}
                                            </div>
                                            <div class="text-sm text-gray-500 dark:text-gray-400" v-if="chat.title">
                                                {{ chat.title }}
                                            </div>
                                        </td>

                                        <!-- Messages -->
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                            {{ chat.message_count || 0 }}
                                        </td>

                                        <!-- Status -->
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span v-if="chat.type === 'ai_chat'" :class="chat.escalated ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' : 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                                                {{ chat.escalated ? 'Escalated' : 'Active' }}
                                            </span>
                                            <span v-else :class="getStatusBadgeClass(chat.status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                                                {{ chat.status }}
                                            </span>
                                        </td>

                                        <!-- Flags -->
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex flex-col space-y-1">
                                                <span v-if="chat.is_flagged || (chat.type === 'ai_chat' && chat.escalated)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                                                    <i class="fas fa-flag mr-1"></i>
                                                    {{ chat.type === 'ai_chat' ? 'Escalated' : 'Flagged' }}
                                                </span>
                                                <span v-if="chat.is_archived" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">
                                                    <i class="fas fa-archive mr-1"></i>
                                                    Archived
                                                </span>
                                                <span v-if="chat.type === 'ai_chat' && chat.is_anonymous" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                                                    <i class="fas fa-user-secret mr-1"></i>
                                                    Anonymous
                                                </span>
                                                <span v-if="chat.type === 'ai_chat' && chat.is_public" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                                    <i class="fas fa-globe mr-1"></i>
                                                    Public
                                                </span>
                                            </div>
                                        </td>

                                        <!-- Actions -->
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <button @click="viewChatDetail(chat.id)" class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3">
                                                <i class="fas fa-eye mr-1"></i>
                                                View Details
                                            </button>
                                        </td>
                                    </tr>
                                    <tr v-if="!Array.isArray(chats) || chats.length === 0">
                                        <td colspan="7" class="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                                            No chats found matching your criteria
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div v-if="pagination.last_page > 1" class="mt-6 flex items-center justify-between">
                            <div class="text-sm text-gray-700 dark:text-gray-300">
                                Showing {{ ((pagination.current_page - 1) * pagination.per_page) + 1 }} to
                                {{ Math.min(pagination.current_page * pagination.per_page, pagination.total) }} of
                                {{ pagination.total }} results
                            </div>
                            <div class="flex space-x-2">
                                <button @click="changePage(pagination.current_page - 1)"
                                    :disabled="pagination.current_page <= 1"
                                    class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed dark:bg-gray-800 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700">
                                    Previous
                                </button>
                                <span class="px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                                    Page {{ pagination.current_page }} of {{ pagination.last_page }}
                                </span>
                                <button @click="changePage(pagination.current_page + 1)"
                                    :disabled="pagination.current_page >= pagination.last_page"
                                    class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed dark:bg-gray-800 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700">
                                    Next
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Chat Detail Modal -->
        <ChatDetailModal
            :show="showChatDetail"
            :chat-id="selectedChatId"
            @close="showChatDetail = false"
            @updated="onChatUpdated"
        />
    </AppLayout>
</template>
