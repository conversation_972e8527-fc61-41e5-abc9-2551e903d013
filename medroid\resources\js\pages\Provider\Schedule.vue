<script setup>
import AppLayout from '@/layouts/AppLayout.vue';
import { Head } from '@inertiajs/vue3';
import { ref, computed, onMounted } from 'vue';
import axios from 'axios';

const breadcrumbs = [
    { title: 'Dashboard', href: '/dashboard' },
    { title: 'My Schedule', href: '/provider/schedule' },
];

// Reactive data
const loading = ref(false);
const appointments = ref([]);
const selectedDate = ref(new Date().toISOString().split('T')[0]);
const viewMode = ref('day'); // 'day', 'week', 'month'

// Computed properties
const filteredAppointments = computed(() => {
    if (viewMode.value === 'day') {
        return appointments.value.filter(apt => 
            apt.scheduled_at.startsWith(selectedDate.value)
        );
    }
    // Add week and month filtering logic here
    return appointments.value;
});

const todayAppointments = computed(() => {
    const today = new Date().toISOString().split('T')[0];
    return appointments.value.filter(apt => 
        apt.scheduled_at.startsWith(today)
    );
});

const upcomingAppointments = computed(() => {
    const today = new Date().toISOString().split('T')[0];
    return appointments.value.filter(apt => 
        apt.scheduled_at.split('T')[0] > today
    ).slice(0, 5);
});

// Methods
const fetchAppointments = async () => {
    loading.value = true;
    try {
        const response = await axios.get('/provider/get-appointments');
        appointments.value = response.data.appointments || [];
    } catch (error) {
        console.error('Error fetching appointments:', error);
    } finally {
        loading.value = false;
    }
};

const formatTime = (dateTimeString) => {
    return new Date(dateTimeString).toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
    });
};

const formatDate = (dateTimeString) => {
    return new Date(dateTimeString).toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
};

const getStatusClass = (status) => {
    const classes = {
        'scheduled': 'bg-blue-100 text-blue-800',
        'confirmed': 'bg-green-100 text-green-800',
        'in_progress': 'bg-yellow-100 text-yellow-800',
        'completed': 'bg-gray-100 text-gray-800',
        'cancelled': 'bg-red-100 text-red-800'
    };
    return classes[status] || 'bg-gray-100 text-gray-800';
};

const getStatusIcon = (status) => {
    const icons = {
        'scheduled': 'fa-clock',
        'confirmed': 'fa-check-circle',
        'in_progress': 'fa-play-circle',
        'completed': 'fa-check-double',
        'cancelled': 'fa-times-circle'
    };
    return icons[status] || 'fa-clock';
};

// Initialize on mount
onMounted(() => {
    fetchAppointments();
});
</script>

<template>
    <Head title="My Schedule" />

    <AppLayout :breadcrumbs="breadcrumbs">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <!-- Header -->
            <div class="mb-8">
                <h1 class="text-3xl font-bold text-gray-900">My Schedule</h1>
                <p class="mt-2 text-gray-600">View and manage your appointments</p>
            </div>

            <!-- Controls -->
            <div class="mb-6 flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
                <div class="flex items-center space-x-4">
                    <!-- View Mode Toggle -->
                    <div class="flex bg-gray-100 rounded-lg p-1">
                        <button
                            @click="viewMode = 'day'"
                            :class="[
                                'px-3 py-1 rounded text-sm font-medium transition-colors',
                                viewMode === 'day' ? 'bg-white text-gray-900 shadow' : 'text-gray-600 hover:text-gray-900'
                            ]"
                        >
                            Day
                        </button>
                        <button
                            @click="viewMode = 'week'"
                            :class="[
                                'px-3 py-1 rounded text-sm font-medium transition-colors',
                                viewMode === 'week' ? 'bg-white text-gray-900 shadow' : 'text-gray-600 hover:text-gray-900'
                            ]"
                        >
                            Week
                        </button>
                        <button
                            @click="viewMode = 'month'"
                            :class="[
                                'px-3 py-1 rounded text-sm font-medium transition-colors',
                                viewMode === 'month' ? 'bg-white text-gray-900 shadow' : 'text-gray-600 hover:text-gray-900'
                            ]"
                        >
                            Month
                        </button>
                    </div>

                    <!-- Date Picker -->
                    <input
                        v-model="selectedDate"
                        type="date"
                        class="border border-gray-300 rounded-lg px-3 py-2 text-sm"
                    />
                </div>

                <!-- Refresh Button -->
                <button
                    @click="fetchAppointments"
                    :disabled="loading"
                    class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
                >
                    <i :class="['fas', loading ? 'fa-spinner fa-spin' : 'fa-sync-alt', 'mr-2']"></i>
                    {{ loading ? 'Loading...' : 'Refresh' }}
                </button>
            </div>

            <!-- Main Content -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Schedule View -->
                <div class="lg:col-span-2">
                    <div class="bg-white rounded-lg shadow-sm border">
                        <div class="p-6 border-b">
                            <h2 class="text-xl font-semibold text-gray-900">
                                {{ viewMode === 'day' ? formatDate(selectedDate + 'T00:00:00') : 'Schedule' }}
                            </h2>
                        </div>

                        <div class="p-6">
                            <div v-if="loading" class="text-center py-8">
                                <i class="fas fa-spinner fa-spin text-2xl text-gray-400 mb-4"></i>
                                <p class="text-gray-600">Loading appointments...</p>
                            </div>

                            <div v-else-if="filteredAppointments.length === 0" class="text-center py-8">
                                <i class="fas fa-calendar-times text-4xl text-gray-300 mb-4"></i>
                                <p class="text-gray-600">No appointments scheduled for this period</p>
                            </div>

                            <div v-else class="space-y-4">
                                <div
                                    v-for="appointment in filteredAppointments"
                                    :key="appointment.id"
                                    class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
                                >
                                    <div class="flex justify-between items-start">
                                        <div class="flex-1">
                                            <div class="flex items-center mb-2">
                                                <i :class="['fas', getStatusIcon(appointment.status), 'text-gray-400 mr-2']"></i>
                                                <h3 class="font-medium text-gray-900">
                                                    {{ appointment.patient?.user?.name || 'Unknown Patient' }}
                                                </h3>
                                            </div>
                                            
                                            <div class="text-sm text-gray-600 space-y-1">
                                                <p>
                                                    <i class="fas fa-clock mr-2"></i>
                                                    {{ formatTime(appointment.scheduled_at) }}
                                                </p>
                                                <p v-if="appointment.service">
                                                    <i class="fas fa-stethoscope mr-2"></i>
                                                    {{ appointment.service.name }}
                                                </p>
                                                <p v-if="appointment.notes">
                                                    <i class="fas fa-sticky-note mr-2"></i>
                                                    {{ appointment.notes }}
                                                </p>
                                            </div>
                                        </div>

                                        <div class="flex flex-col items-end space-y-2">
                                            <span
                                                :class="[
                                                    'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                                                    getStatusClass(appointment.status)
                                                ]"
                                            >
                                                {{ appointment.status.replace('_', ' ').toUpperCase() }}
                                            </span>

                                            <div class="flex space-x-2">
                                                <button
                                                    v-if="appointment.status === 'confirmed'"
                                                    class="text-green-600 hover:text-green-800 text-sm"
                                                >
                                                    <i class="fas fa-video mr-1"></i>
                                                    Start
                                                </button>
                                                <button class="text-blue-600 hover:text-blue-800 text-sm">
                                                    <i class="fas fa-edit mr-1"></i>
                                                    Edit
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="space-y-6">
                    <!-- Today's Summary -->
                    <div class="bg-white rounded-lg shadow-sm border p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Today's Summary</h3>
                        
                        <div class="space-y-3">
                            <div class="flex justify-between">
                                <span class="text-gray-600">Total Appointments</span>
                                <span class="font-medium">{{ todayAppointments.length }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Completed</span>
                                <span class="font-medium text-green-600">
                                    {{ todayAppointments.filter(apt => apt.status === 'completed').length }}
                                </span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Remaining</span>
                                <span class="font-medium text-blue-600">
                                    {{ todayAppointments.filter(apt => ['scheduled', 'confirmed'].includes(apt.status)).length }}
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- Upcoming Appointments -->
                    <div class="bg-white rounded-lg shadow-sm border p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Upcoming</h3>
                        
                        <div v-if="upcomingAppointments.length === 0" class="text-gray-500 text-sm">
                            No upcoming appointments
                        </div>
                        
                        <div v-else class="space-y-3">
                            <div
                                v-for="appointment in upcomingAppointments"
                                :key="appointment.id"
                                class="border-l-4 border-blue-500 pl-3 py-2"
                            >
                                <p class="font-medium text-sm">{{ appointment.patient?.user?.name }}</p>
                                <p class="text-xs text-gray-600">
                                    {{ formatDate(appointment.scheduled_at) }}
                                </p>
                                <p class="text-xs text-gray-600">
                                    {{ formatTime(appointment.scheduled_at) }}
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="bg-white rounded-lg shadow-sm border p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
                        
                        <div class="space-y-3">
                            <button class="w-full text-left p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                                <i class="fas fa-calendar-alt text-blue-600 mr-3"></i>
                                <span class="text-sm font-medium">Manage Availability</span>
                            </button>
                            <button class="w-full text-left p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                                <i class="fas fa-users text-green-600 mr-3"></i>
                                <span class="text-sm font-medium">View Patients</span>
                            </button>
                            <button class="w-full text-left p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                                <i class="fas fa-chart-line text-purple-600 mr-3"></i>
                                <span class="text-sm font-medium">View Earnings</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
