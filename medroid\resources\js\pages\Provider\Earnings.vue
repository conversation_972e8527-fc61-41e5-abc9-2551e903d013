<script setup>
import AppLayout from '@/layouts/AppLayout.vue';
import { Head } from '@inertiajs/vue3';
import { ref, computed, onMounted } from 'vue';
import axios from 'axios';

const breadcrumbs = [
    { title: 'Dashboard', href: '/dashboard' },
    { title: 'Earnings', href: '/provider/earnings' },
];

// Reactive data
const loading = ref(false);
const earnings = ref({
    total: 0,
    thisMonth: 0,
    lastMonth: 0,
    pending: 0,
    paid: 0
});
const transactions = ref([]);
const selectedPeriod = ref('month'); // 'week', 'month', 'year'

// Computed properties
const monthlyGrowth = computed(() => {
    if (earnings.value.lastMonth === 0) return 0;
    return ((earnings.value.thisMonth - earnings.value.lastMonth) / earnings.value.lastMonth * 100).toFixed(1);
});

// Methods
const fetchEarnings = async () => {
    loading.value = true;
    try {
        const response = await axios.get('/provider/get-earnings', {
            params: { period: selectedPeriod.value }
        });
        earnings.value = response.data.earnings || earnings.value;
        transactions.value = response.data.transactions || [];
    } catch (error) {
        console.error('Error fetching earnings:', error);
    } finally {
        loading.value = false;
    }
};

const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount);
};

const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
};

const getStatusClass = (status) => {
    const classes = {
        'paid': 'bg-green-100 text-green-800',
        'pending': 'bg-yellow-100 text-yellow-800',
        'processing': 'bg-blue-100 text-blue-800',
        'failed': 'bg-red-100 text-red-800'
    };
    return classes[status] || 'bg-gray-100 text-gray-800';
};

// Initialize on mount
onMounted(() => {
    fetchEarnings();
});
</script>

<template>
    <Head title="Earnings" />

    <AppLayout :breadcrumbs="breadcrumbs">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <!-- Header -->
            <div class="mb-8">
                <h1 class="text-3xl font-bold text-gray-900">Earnings</h1>
                <p class="mt-2 text-gray-600">Track your income and payment history</p>
            </div>

            <!-- Period Selector -->
            <div class="mb-6 flex justify-between items-center">
                <div class="flex bg-gray-100 rounded-lg p-1">
                    <button
                        @click="selectedPeriod = 'week'; fetchEarnings()"
                        :class="[
                            'px-3 py-1 rounded text-sm font-medium transition-colors',
                            selectedPeriod === 'week' ? 'bg-white text-gray-900 shadow' : 'text-gray-600 hover:text-gray-900'
                        ]"
                    >
                        This Week
                    </button>
                    <button
                        @click="selectedPeriod = 'month'; fetchEarnings()"
                        :class="[
                            'px-3 py-1 rounded text-sm font-medium transition-colors',
                            selectedPeriod === 'month' ? 'bg-white text-gray-900 shadow' : 'text-gray-600 hover:text-gray-900'
                        ]"
                    >
                        This Month
                    </button>
                    <button
                        @click="selectedPeriod = 'year'; fetchEarnings()"
                        :class="[
                            'px-3 py-1 rounded text-sm font-medium transition-colors',
                            selectedPeriod === 'year' ? 'bg-white text-gray-900 shadow' : 'text-gray-600 hover:text-gray-900'
                        ]"
                    >
                        This Year
                    </button>
                </div>

                <button
                    @click="fetchEarnings"
                    :disabled="loading"
                    class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
                >
                    <i :class="['fas', loading ? 'fa-spinner fa-spin' : 'fa-sync-alt', 'mr-2']"></i>
                    {{ loading ? 'Loading...' : 'Refresh' }}
                </button>
            </div>

            <!-- Earnings Overview -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <div class="bg-white rounded-lg shadow-sm border p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-green-100 mr-4">
                            <i class="fas fa-dollar-sign text-green-600 text-xl"></i>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">Total Earnings</p>
                            <p class="text-2xl font-bold text-gray-900">{{ formatCurrency(earnings.total) }}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-sm border p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-blue-100 mr-4">
                            <i class="fas fa-calendar-alt text-blue-600 text-xl"></i>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">This Month</p>
                            <p class="text-2xl font-bold text-gray-900">{{ formatCurrency(earnings.thisMonth) }}</p>
                            <p :class="[
                                'text-xs mt-1',
                                monthlyGrowth >= 0 ? 'text-green-600' : 'text-red-600'
                            ]">
                                <i :class="['fas', monthlyGrowth >= 0 ? 'fa-arrow-up' : 'fa-arrow-down', 'mr-1']"></i>
                                {{ Math.abs(monthlyGrowth) }}% from last month
                            </p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-sm border p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-yellow-100 mr-4">
                            <i class="fas fa-clock text-yellow-600 text-xl"></i>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">Pending</p>
                            <p class="text-2xl font-bold text-gray-900">{{ formatCurrency(earnings.pending) }}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-sm border p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-purple-100 mr-4">
                            <i class="fas fa-check-circle text-purple-600 text-xl"></i>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">Paid Out</p>
                            <p class="text-2xl font-bold text-gray-900">{{ formatCurrency(earnings.paid) }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Transaction History -->
            <div class="bg-white rounded-lg shadow-sm border">
                <div class="p-6 border-b">
                    <h2 class="text-xl font-semibold text-gray-900">Transaction History</h2>
                </div>

                <div class="p-6">
                    <div v-if="loading" class="text-center py-8">
                        <i class="fas fa-spinner fa-spin text-2xl text-gray-400 mb-4"></i>
                        <p class="text-gray-600">Loading transactions...</p>
                    </div>

                    <div v-else-if="transactions.length === 0" class="text-center py-8">
                        <i class="fas fa-receipt text-4xl text-gray-300 mb-4"></i>
                        <p class="text-gray-600">No transactions found</p>
                        <p class="text-sm text-gray-500 mt-2">Transactions will appear here after completed appointments</p>
                    </div>

                    <div v-else class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Date
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Patient
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Service
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Amount
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Status
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr v-for="transaction in transactions" :key="transaction.id">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {{ formatDate(transaction.created_at) }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {{ transaction.patient?.user?.name || 'Unknown' }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {{ transaction.service?.name || 'Consultation' }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                        {{ formatCurrency(transaction.amount) }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span
                                            :class="[
                                                'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                                                getStatusClass(transaction.status)
                                            ]"
                                        >
                                            {{ transaction.status.toUpperCase() }}
                                        </span>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
