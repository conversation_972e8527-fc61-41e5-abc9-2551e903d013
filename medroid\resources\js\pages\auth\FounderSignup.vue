<script setup lang="ts">
import { Head, Link, useForm } from '@inertiajs/vue3';
import { computed } from 'vue';
import InputError from '@/components/InputError.vue';

interface Props {
    waitlistStatus: {
        enabled: boolean;
        message: string;
    };
}

const props = defineProps<Props>();

// Computed property for founder link
const shareableLink = computed(() => {
    if (typeof window !== 'undefined') {
        return `${window.location.origin}/join-founders`;
    }
    return '/join-founders';
});

const form = useForm({
    name: '',
    email: '',
    password: '',
    password_confirmation: '',
    role: 'patient', // Always patient for founder signups
    gender: '',
    date_of_birth: '',
});



const shareText = computed(() => {
    return `EXCLUSIVE: Welcome to the Medroid Founders' Club

Meet your new AI Doctor!

Get instant medical advice 24/7 with Medroid - powered by advanced medical AI that understands symptoms, suggests treatments, and provides evidence-based care guidance.

Get exclusive access to Medroid with:

- Founders' Club badge & priority support
- Early access to advanced AI features
- $10 welcome bonus
- Private Slack access
- Direct access to our founders

Join now: ${shareableLink.value}

Be among the first to have an AI Doctor in your pocket

Join our exclusive Medroid's Founders' Club Slack community - connect with fellow pioneers, get insider updates, and chat directly with our founding team:
https://join.slack.com/share/enQtODk5ODQyMTMzOTEwNy02ZjU0N2I0OGZjODJlMmY1MmE4YjBlNWI2OTAxY2FkOWI5NjdiZmU1ODVmMTg5YTA0YzgwMjJkMDdmZTc4OGU2

#AIDoctor #Health #HealthTech #Innovation`;
});

const shareOnLinkedIn = () => {
    const text = encodeURIComponent(shareText.value);
    const url = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(shareableLink.value)}&summary=${text}`;
    window.open(url, '_blank');
};

const shareOnWhatsApp = () => {
    const text = encodeURIComponent(shareText.value);
    const url = `https://wa.me/?text=${text}`;
    window.open(url, '_blank');
};

const copyToClipboard = async () => {
    try {
        await navigator.clipboard.writeText(shareText.value);
        alert('Link copied to clipboard!');
    } catch (err) {
        console.error('Failed to copy: ', err);
    }
};

const submit = () => {
    form.post(route('founder.signup.store'));
};
</script>

<template>
    <Head title="Join the Medroid Founders' Club" />

        <div class="min-h-screen bg-gradient-to-br from-medroid-sage via-medroid-cream to-white">
            <div class="container mx-auto px-4 py-8">
                <!-- Header -->
                <div class="text-center mb-8">
                    <div class="flex justify-center mb-4">
                        <img src="/medroid_logo.png" alt="Medroid" class="h-12 w-auto">
                    </div>
                    <h1 class="text-4xl font-bold text-gray-900 mb-2">Join the Medroid Founders' Club</h1>
                    <p class="text-lg text-gray-600">Be the first to try out our AI Doctor</p>
                </div>

                <div class="max-w-6xl mx-auto grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <!-- Benefits Section -->
                    <div class="bg-white rounded-2xl shadow-xl p-8">
                        <div class="text-center mb-6">
                            <div class="w-16 h-16 bg-gradient-to-r from-medroid-orange to-medroid-teal rounded-full flex items-center justify-center mx-auto mb-4">
                                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                                </svg>
                            </div>
                            <h2 class="text-2xl font-bold text-medroid-navy mb-2">Exclusive Benefits</h2>
                            <p class="text-medroid-slate">Join us in disrupting the healthcare industry</p>
                        </div>

                        <div class="space-y-4">
                            <div class="flex items-start space-x-3">
                                <div class="w-6 h-6 bg-medroid-orange/20 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                    <svg class="w-4 h-4 text-medroid-orange" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-medroid-navy whitespace-nowrap">Founders' Club Member Badge</h3>
                                    <p class="text-sm text-medroid-slate">Recognition for your trust in Medroid's mission</p>
                                </div>
                            </div>

                            <div class="flex items-start space-x-3">
                                <div class="w-6 h-6 bg-medroid-teal/20 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                    <svg class="w-4 h-4 text-medroid-teal" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M12 2.25a.75.75 0 01.75.75v2.25a.75.75 0 01-1.5 0V3a.75.75 0 01.75-.75zM12 18.75a.75.75 0 01.75.75v2.25a.75.75 0 01-1.5 0V19.5a.75.75 0 01.75-.75z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-medroid-navy">Priority Support</h3>
                                    <p class="text-sm text-medroid-slate">Get faster response times and dedicated assistance</p>
                                </div>
                            </div>

                            <div class="flex items-start space-x-3">
                                <div class="w-6 h-6 bg-medroid-teal/20 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                    <svg class="w-4 h-4 text-medroid-teal" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-medroid-navy">Early Access to New Features</h3>
                                    <p class="text-sm text-medroid-slate">Be the first to try cutting-edge healthcare innovations</p>
                                </div>
                            </div>

                            <div class="flex items-start space-x-3">
                                <div class="w-6 h-6 bg-medroid-orange/20 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                    <svg class="w-4 h-4 text-medroid-orange" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-medroid-navy">$10 Welcome Bonus</h3>
                                    <p class="text-sm text-medroid-slate">Start your journey with bonus credits</p>
                                </div>
                            </div>

                            <div class="flex items-start space-x-3">
                                <div class="w-6 h-6 bg-medroid-teal/20 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                    <svg class="w-4 h-4 text-medroid-teal" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <h3 class="font-semibold text-medroid-navy">Exclusive Slack Channel</h3>
                                    <p class="text-sm text-medroid-slate mb-2">Connect with the founders and shape the future of healthcare</p>
                                    <a
                                        href="https://join.slack.com/share/enQtODk5ODQyMTMzOTEwNy02ZjU0N2I0OGZjODJlMmY1MmE4YjBlNWI2OTAxY2FkOWI5NjdiZmU1ODVmMTg5YTA0YzgwMjJkMDdmZTc4OGU2"
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        class="inline-flex items-center space-x-2 bg-medroid-teal text-white px-3 py-1.5 rounded-md text-xs font-medium hover:bg-medroid-teal/90 transition-colors"
                                    >
                                        <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M5.042 15.165a2.528 2.528 0 0 1-2.52-2.523A2.528 2.528 0 0 1 5.042 10.12h2.52v2.522a2.528 2.528 0 0 1-2.52 2.523Zm0-6.584A2.528 2.528 0 0 1 2.522 6.058 2.528 2.528 0 0 1 5.042 3.535a2.528 2.528 0 0 1 2.52 2.523v2.523H5.042Zm6.584 0a2.528 2.528 0 0 1-2.523-2.523A2.528 2.528 0 0 1 11.626 3.535a2.528 2.528 0 0 1 2.523 2.523v2.523h-2.523Zm0 6.584a2.528 2.528 0 0 1 2.523 2.523 2.528 2.528 0 0 1-2.523 2.523H9.103v-2.523a2.528 2.528 0 0 1 2.523-2.523Zm6.584-2.523a2.528 2.528 0 0 1 2.523-2.522 2.528 2.528 0 0 1 2.523 2.522 2.528 2.528 0 0 1-2.523 2.523h-2.523v-2.523Zm0-6.584a2.528 2.528 0 0 1 2.523 2.523 2.528 2.528 0 0 1-2.523 2.523h-2.523V6.058a2.528 2.528 0 0 1 2.523-2.523Z"/>
                                        </svg>
                                        <span>Join Slack</span>
                                    </a>
                                </div>
                            </div>

                            <div class="flex items-start space-x-3">
                                <div class="w-6 h-6 bg-medroid-orange/20 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                    <svg class="w-4 h-4 text-medroid-orange" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-medroid-navy">Direct Access to the Founders</h3>
                                    <p class="text-sm text-medroid-slate">Get direct communication with Medroid's founding team</p>
                                </div>
                            </div>
                        </div>

                        <!-- Share Section -->
                        <div class="mt-8 pt-6 border-t border-medroid-border">
                            <h3 class="text-lg font-semibold text-medroid-navy mb-4">Share with Friends</h3>
                            <div class="flex flex-wrap gap-3">
                                <button
                                    @click="shareOnLinkedIn"
                                    class="flex items-center space-x-2 bg-medroid-teal text-white px-4 py-2 rounded-lg hover:bg-medroid-teal/90 transition-colors"
                                >
                                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                                    </svg>
                                    <span>LinkedIn</span>
                                </button>
                                <button
                                    @click="shareOnWhatsApp"
                                    class="flex items-center space-x-2 bg-medroid-orange text-white px-4 py-2 rounded-lg hover:bg-medroid-orange/90 transition-colors"
                                >
                                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
                                    </svg>
                                    <span>WhatsApp</span>
                                </button>
                                <button
                                    @click="copyToClipboard"
                                    class="flex items-center space-x-2 bg-medroid-slate text-white px-4 py-2 rounded-lg hover:bg-medroid-slate/90 transition-colors"
                                >
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                    </svg>
                                    <span>Copy Link</span>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Signup Form -->
                    <div class="bg-white rounded-2xl shadow-xl p-8 flex flex-col justify-center min-h-full">
                        <div class="text-center mb-6">
                            <h2 class="text-2xl font-bold text-medroid-navy mb-2">Create Your Account</h2>
                            <p class="text-medroid-slate">Join thousands of early adopters</p>
                        </div>

                        <form @submit.prevent="submit" class="space-y-6">
                            <div>
                                <label for="name" class="block text-sm font-medium text-medroid-navy mb-2">Full Name</label>
                                <input
                                    id="name"
                                    type="text"
                                    v-model="form.name"
                                    required
                                    autofocus
                                    autocomplete="name"
                                    class="w-full px-3 py-2 border border-medroid-border rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-medroid-orange transition-all duration-200 text-medroid-navy placeholder-medroid-slate bg-white"
                                    placeholder="Enter your full name"
                                />
                                <InputError class="mt-2" :message="form.errors.name" />
                            </div>

                            <div>
                                <label for="email" class="block text-sm font-medium text-medroid-navy mb-2">Email</label>
                                <input
                                    id="email"
                                    type="email"
                                    v-model="form.email"
                                    required
                                    autocomplete="username"
                                    class="w-full px-3 py-2 border border-medroid-border rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-medroid-orange transition-all duration-200 text-medroid-navy placeholder-medroid-slate bg-white"
                                    placeholder="Enter your email address"
                                />
                                <InputError class="mt-2" :message="form.errors.email" />
                            </div>

                            <div>
                                <label for="password" class="block text-sm font-medium text-medroid-navy mb-2">Password</label>
                                <input
                                    id="password"
                                    type="password"
                                    v-model="form.password"
                                    required
                                    autocomplete="new-password"
                                    class="w-full px-3 py-2 border border-medroid-border rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-medroid-orange transition-all duration-200 text-medroid-navy placeholder-medroid-slate bg-white"
                                    placeholder="Create a strong password"
                                />
                                <InputError class="mt-2" :message="form.errors.password" />
                            </div>

                            <div>
                                <label for="password_confirmation" class="block text-sm font-medium text-medroid-navy mb-2">Confirm Password</label>
                                <input
                                    id="password_confirmation"
                                    type="password"
                                    v-model="form.password_confirmation"
                                    required
                                    autocomplete="new-password"
                                    class="w-full px-3 py-2 border border-medroid-border rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-medroid-orange transition-all duration-200 text-medroid-navy placeholder-medroid-slate bg-white"
                                    placeholder="Confirm your password"
                                />
                                <InputError class="mt-2" :message="form.errors.password_confirmation" />
                            </div>



                            <div class="flex items-center justify-between">
                                <button
                                    type="submit"
                                    :disabled="form.processing"
                                    class="w-full justify-center bg-medroid-orange hover:bg-medroid-orange/90 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200 disabled:opacity-50 shadow-lg"
                                >
                                    {{ form.processing ? 'Creating Account...' : 'Join Founders Club' }}
                                </button>
                            </div>
                        </form>

                        <div class="mt-6 text-center">
                            <p class="text-sm text-medroid-slate">
                                Already have an account?
                                <Link :href="route('login')" class="text-medroid-orange hover:text-medroid-orange/80 font-medium transition-colors duration-200 underline">
                                    Sign in
                                </Link>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
</template>
