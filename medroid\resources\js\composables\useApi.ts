/**
 * API Composable
 * 
 * This composable provides a consistent way to make API calls with proper
 * CSRF token handling, error handling, and loading states.
 */

import { ref, type Ref } from 'vue'
import axios, { type AxiosResponse, type AxiosError } from 'axios'

interface ApiState<T = any> {
  data: Ref<T | null>
  loading: Ref<boolean>
  error: Ref<string | null>
}

interface ApiOptions {
  showErrorAlert?: boolean
  retryOnCsrfError?: boolean
}

export function useApi<T = any>(options: ApiOptions = {}) {
  const data: Ref<T | null> = ref(null)
  const loading = ref(false)
  const error = ref<string | null>(null)

  const { 
    showErrorAlert = false, 
    retryOnCsrfError = true 
  } = options

  /**
   * Make a GET request
   */
  const get = async (url: string): Promise<T | null> => {
    loading.value = true
    error.value = null

    try {
      const response: AxiosResponse<T> = await axios.get(url)
      data.value = response.data
      return response.data
    } catch (err) {
      const errorMessage = handleError(err as AxiosError)
      error.value = errorMessage
      
      if (showErrorAlert) {
        alert(errorMessage)
      }
      
      return null
    } finally {
      loading.value = false
    }
  }

  /**
   * Make a POST request
   */
  const post = async (url: string, payload: any = {}): Promise<T | null> => {
    loading.value = true
    error.value = null

    try {
      const response: AxiosResponse<T> = await axios.post(url, payload)
      data.value = response.data
      return response.data
    } catch (err) {
      const errorMessage = handleError(err as AxiosError)
      error.value = errorMessage
      
      // Special handling for logout - don't show error alert
      if (showErrorAlert && !url.includes('/logout')) {
        alert(errorMessage)
      }
      
      // For logout, return success even if there's an error (token might already be invalid)
      if (url.includes('/logout')) {
        return { message: 'Logged out successfully' } as T
      }
      
      return null
    } finally {
      loading.value = false
    }
  }

  /**
   * Make a PUT request
   */
  const put = async (url: string, payload: any = {}): Promise<T | null> => {
    loading.value = true
    error.value = null

    try {
      const response: AxiosResponse<T> = await axios.put(url, payload)
      data.value = response.data
      return response.data
    } catch (err) {
      const errorMessage = handleError(err as AxiosError)
      error.value = errorMessage
      
      if (showErrorAlert) {
        alert(errorMessage)
      }
      
      return null
    } finally {
      loading.value = false
    }
  }

  /**
   * Make a DELETE request
   */
  const del = async (url: string): Promise<boolean> => {
    loading.value = true
    error.value = null

    try {
      await axios.delete(url)
      return true
    } catch (err) {
      const errorMessage = handleError(err as AxiosError)
      error.value = errorMessage
      
      if (showErrorAlert) {
        alert(errorMessage)
      }
      
      return false
    } finally {
      loading.value = false
    }
  }

  /**
   * Handle API errors consistently
   */
  const handleError = (err: AxiosError): string => {
    console.error('API Error:', err)

    if (err.response) {
      // Server responded with error status
      const status = err.response.status
      const data = err.response.data as any

      switch (status) {
        case 401:
          return 'Authentication required. Please log in again.'
        case 403:
          return 'You do not have permission to perform this action.'
        case 404:
          return 'The requested resource was not found.'
        case 419:
          return 'Security token expired. Please refresh the page and try again.'
        case 422:
          // Validation errors
          if (data?.errors) {
            const firstError = Object.values(data.errors)[0]
            return Array.isArray(firstError) ? firstError[0] : String(firstError)
          }
          return data?.message || 'Validation failed.'
        case 429:
          return 'Too many requests. Please wait a moment and try again.'
        case 500:
          return 'Server error. Please try again later.'
        default:
          return data?.message || `Server error: ${status}`
      }
    } else if (err.request) {
      // Request was made but no response received
      return 'Network error. Please check your connection and try again.'
    } else {
      // Something else happened
      return 'An unexpected error occurred. Please try again.'
    }
  }

  /**
   * Reset the state
   */
  const reset = () => {
    data.value = null
    loading.value = false
    error.value = null
  }

  /**
   * Clear only the error
   */
  const clearError = () => {
    error.value = null
  }

  return {
    // State
    data,
    loading,
    error,
    
    // Methods
    get,
    post,
    put,
    delete: del,
    reset,
    clearError,
    handleError
  }
}

/**
 * Specialized composable for referral API calls
 */
export function useReferralApi() {
  const api = useApi()

  const getReferralCode = () => api.get('/referrals-code')
  const getReferralStats = () => api.get('/referrals-my')
  const sendInvitation = (email: string) => api.post('/send-referral-invite', { email })
  const getCreditBalance = () => api.get('/credits-balance')

  return {
    ...api,
    getReferralCode,
    getReferralStats,
    sendInvitation,
    getCreditBalance
  }
}

/**
 * Specialized composable for provider API calls
 */
export function useProviderApi() {
  const api = useApi()

  const saveAvailability = (data: any) => api.post('/provider/save-availability', data)
  const getAvailability = () => api.get('/provider/get-availability')
  const saveProfile = (data: any) => api.post('/provider/profile', data)
  const getProfile = () => api.get('/provider/get-profile')

  return {
    ...api,
    saveAvailability,
    getAvailability,
    saveProfile,
    getProfile
  }
}
