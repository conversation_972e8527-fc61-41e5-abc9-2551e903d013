<?php

namespace Tests\Feature;

use App\Models\EmailTemplate;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Mail;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class EmailTemplateControllerTest extends TestCase
{
    use RefreshDatabase;

    private User $adminUser;

    protected function setUp(): void
    {
        parent::setUp();
        $this->adminUser = $this->createUserWithRole('admin');
    }

    #[Test]
    public function it_can_list_all_email_templates()
    {
        EmailTemplate::create([
            'name' => 'Template 1',
            'slug' => 'template-1',
            'subject' => 'Subject 1',
            'content' => 'Content 1',
        ]);

        EmailTemplate::create([
            'name' => 'Template 2',
            'slug' => 'template-2',
            'subject' => 'Subject 2',
            'content' => 'Content 2',
        ]);

        $response = $this->actingAs($this->adminUser)->getJson('/api/email-templates');

        $response->assertStatus(200)
            ->assertJsonCount(2)
            ->assertJsonStructure([
                '*' => [
                    'id',
                    'name',
                    'slug',
                    'subject',
                    'content',
                    'description',
                    'is_active',
                    'created_at',
                    'updated_at',
                ]
            ]);
    }

    #[Test]
    public function it_can_show_specific_email_template()
    {
        $template = EmailTemplate::create([
            'name' => 'Test Template',
            'slug' => 'test-template',
            'subject' => 'Test Subject',
            'content' => 'Test Content',
            'description' => 'Test Description',
        ]);

        $response = $this->actingAs($this->adminUser)->getJson("/api/email-templates/{$template->id}");

        $response->assertStatus(200)
            ->assertJson([
                'id' => $template->id,
                'name' => 'Test Template',
                'slug' => 'test-template',
                'subject' => 'Test Subject',
                'content' => 'Test Content',
                'description' => 'Test Description',
            ]);
    }

    #[Test]
    public function it_returns_404_for_non_existent_template()
    {
        $response = $this->actingAs($this->adminUser)->getJson('/api/email-templates/999');

        $response->assertStatus(404)
            ->assertJson(['message' => 'Email template not found']);
    }

    #[Test]
    public function it_can_create_new_email_template()
    {
        $templateData = [
            'name' => 'New Template',
            'slug' => 'new-template',
            'subject' => 'New Subject',
            'content' => 'New Content',
            'description' => 'New Description',
            'is_active' => true,
        ];

        $response = $this->actingAs($this->adminUser)->postJson('/api/email-templates', $templateData);

        $response->assertStatus(201)
            ->assertJson([
                'message' => 'Email template created successfully',
                'template' => [
                    'name' => 'New Template',
                    'slug' => 'new-template',
                    'subject' => 'New Subject',
                    'content' => 'New Content',
                    'description' => 'New Description',
                    'is_active' => true,
                ]
            ]);

        $this->assertDatabaseHas('email_templates', [
            'name' => 'New Template',
            'slug' => 'new-template',
        ]);
    }

    #[Test]
    public function it_validates_required_fields_when_creating_template()
    {
        $response = $this->actingAs($this->adminUser)->postJson('/api/email-templates', []);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['name', 'slug', 'subject', 'content']);
    }

    #[Test]
    public function it_validates_unique_slug_when_creating_template()
    {
        EmailTemplate::create([
            'name' => 'Existing Template',
            'slug' => 'existing-slug',
            'subject' => 'Subject',
            'content' => 'Content',
        ]);

        $response = $this->actingAs($this->adminUser)->postJson('/api/email-templates', [
            'name' => 'New Template',
            'slug' => 'existing-slug',
            'subject' => 'Subject',
            'content' => 'Content',
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['slug']);
    }

    #[Test]
    public function it_can_update_email_template()
    {
        $template = EmailTemplate::create([
            'name' => 'Original Template',
            'slug' => 'original-template',
            'subject' => 'Original Subject',
            'content' => 'Original Content',
        ]);

        $updateData = [
            'name' => 'Updated Template',
            'subject' => 'Updated Subject',
            'content' => 'Updated Content',
            'description' => 'Updated Description',
            'is_active' => false,
        ];

        $response = $this->actingAs($this->adminUser)->putJson("/api/email-templates/{$template->id}", $updateData);

        $response->assertStatus(200)
            ->assertJson([
                'message' => 'Email template updated successfully',
                'template' => [
                    'name' => 'Updated Template',
                    'subject' => 'Updated Subject',
                    'content' => 'Updated Content',
                    'description' => 'Updated Description',
                    'is_active' => false,
                ]
            ]);

        $this->assertDatabaseHas('email_templates', [
            'id' => $template->id,
            'name' => 'Updated Template',
        ]);
    }

    #[Test]
    public function it_can_delete_email_template()
    {
        $template = EmailTemplate::create([
            'name' => 'Template to Delete',
            'slug' => 'template-to-delete',
            'subject' => 'Subject',
            'content' => 'Content',
        ]);

        $response = $this->actingAs($this->adminUser)->deleteJson("/api/email-templates/{$template->id}");

        $response->assertStatus(200)
            ->assertJson(['message' => 'Email template deleted successfully']);

        $this->assertDatabaseMissing('email_templates', ['id' => $template->id]);
    }

    #[Test]
    public function it_can_preview_email_template()
    {
        $template = EmailTemplate::create([
            'name' => 'Preview Template',
            'slug' => 'preview-template',
            'subject' => 'Hello {{userName}}',
            'content' => 'Welcome {{userName}} to {{appName}}!',
        ]);

        $response = $this->actingAs($this->adminUser)->getJson("/api/email-templates/{$template->id}/preview");

        $response->assertStatus(200)
            ->assertJsonStructure(['subject', 'content']);
    }

    #[Test]
    public function it_can_send_test_email()
    {
        Mail::fake();

        $template = EmailTemplate::create([
            'name' => 'Test Email Template',
            'slug' => 'test-email-template',
            'subject' => 'Test Subject',
            'content' => 'Test Content',
        ]);

        $response = $this->actingAs($this->adminUser)->postJson("/api/email-templates/{$template->id}/send-test", [
            'email' => '<EMAIL>'
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'message' => 'Test email sent <NAME_EMAIL>',
                'success' => true
            ]);

        Mail::assertSent(\Illuminate\Mail\Mailable::class);
    }

    #[Test]
    public function it_validates_email_when_sending_test_email()
    {
        $template = EmailTemplate::create([
            'name' => 'Test Template',
            'slug' => 'test-template',
            'subject' => 'Subject',
            'content' => 'Content',
        ]);

        $response = $this->actingAs($this->adminUser)->postJson("/api/email-templates/{$template->id}/send-test", [
            'email' => 'invalid-email'
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['email']);
    }

    #[Test]
    public function it_can_test_email_configuration()
    {
        Mail::fake();

        $response = $this->actingAs($this->adminUser)->postJson('/api/email-templates/test-config', [
            'email' => '<EMAIL>'
        ]);

        $response->assertStatus(200)
            ->assertJsonStructure(['message', 'success']);

        Mail::assertSent(\Illuminate\Mail\Mailable::class);
    }

    #[Test]
    public function it_requires_authentication_for_all_endpoints()
    {
        $template = EmailTemplate::create([
            'name' => 'Test Template',
            'slug' => 'test-template',
            'subject' => 'Subject',
            'content' => 'Content',
        ]);

        // Test all endpoints without authentication
        $this->getJson('/api/email-templates')->assertStatus(401);
        $this->getJson("/api/email-templates/{$template->id}")->assertStatus(401);
        $this->postJson('/api/email-templates', [])->assertStatus(401);
        $this->putJson("/api/email-templates/{$template->id}", [])->assertStatus(401);
        $this->deleteJson("/api/email-templates/{$template->id}")->assertStatus(401);
        $this->getJson("/api/email-templates/{$template->id}/preview")->assertStatus(401);
        $this->postJson("/api/email-templates/{$template->id}/send-test", [])->assertStatus(401);
        $this->postJson('/api/email-templates/test-config', [])->assertStatus(401);
    }

    #[Test]
    public function it_requires_admin_permissions_for_crud_operations()
    {
        $regularUser = $this->createUserWithRole('user');

        $template = EmailTemplate::create([
            'name' => 'Test Template',
            'slug' => 'test-template',
            'subject' => 'Subject',
            'content' => 'Content',
        ]);

        // Test CRUD operations with regular user
        $this->actingAs($regularUser)->postJson('/api/email-templates', [])->assertStatus(403);
        $this->actingAs($regularUser)->putJson("/api/email-templates/{$template->id}", [])->assertStatus(403);
        $this->actingAs($regularUser)->deleteJson("/api/email-templates/{$template->id}")->assertStatus(403);
    }
}
